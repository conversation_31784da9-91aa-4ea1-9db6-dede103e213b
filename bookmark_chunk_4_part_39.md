# Bookmarks from bookmark_chunk_4_part_39.json

## Category: Entertainment / Movies / Pop Culture / Nostalgia / Posters

*   **Author:** @PopCulture2000s
    **Date:** 2023-11-07 01:21:07 +03:00
    **Content:** movies with these type of posters never disappoint https://t.co/uekXUMLFuG
    **URL:** [https://twitter.com/PopCulture2000s/status/1721653969259266416](https://twitter.com/PopCulture2000s/status/1721653969259266416)
    **Media:**
      - Type: photo
      - URL: https://t.co/uekXUMLFuG
      - Thumbnail: https://pbs.twimg.com/media/F-SLGCNWIAAjkh5?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/F-SLGCNWIAAjkh5?format=jpg&name=orig

---

## Category: Web Development / Resources / Tools / Learning / Community / #100DaysOfCode

*   **Author:** @Prathkum
    **Date:** 2023-11-09 13:20:46 +03:00
    **Content:** The best websites for every developer:

- UI Design Daily
https://uidesigndaily.com/

- MentorCruise
https://mentorcruise.com/

- Daily Dev
https://daily.dev/

- Showwcase
https://www.showwcase.com/

- Dev Resources
https://devresourc.es/

- LambdaTest
https://www.lambdatest.com/

- Public APIs
https://public-apis.io/

- Roadmap SH
https://roadmap.sh/

- Resume io
https://resume.io/

- Smalls Dev tools
https://smalldev.tools/

- Over API
https://overapi.com/

- CSS Scan
https://getcssscan.com/

- Meta Tags
https://metatags.io/

- Readme So
https://readme.so/

- Peppertype ai
https://www.peppertype.ai/

- Synthesia
https://www.synthesia.io/

- Storyset
https://storyset.com/

- Dev Hints
https://devhints.io/

- Responsively App
https://responsively.app/

- Carbon Now
https://carbon.now.sh/

- Poet so
https://poet.so/

- Ray So
https://ray.so/

- Code Beautify
https://codebeautify.org/

- Profile Pic Maker
https://pfpmaker.com/

- Remove BG
https://www.remove.bg/

- Resume Worded
https://resumeworded.com/

- Medusa JS
https://medusajs.com/

- UIverse io
https://uiverse.io/

- Dev Docs
https://devdocs.io/

- Free for Dev
https://free-for.dev/

- Permadelete
https://perma.delete.gallery/

- HTTP Cat
https://http.cat/

- HTTP Dog
https://http.dog/

- Programmer Humor
https://programmerhumor.io/

- Can I use
https://caniuse.com/

- Stack Overflow
https://stackoverflow.com/

- W3Schools
https://www.w3schools.com/

- MDN Web Docs
https://developer.mozilla.org/

- CSS Tricks
https://css-tricks.com/

- Smashing Magazine
https://www.smashingmagazine.com/

- SitePoint
https://www.sitepoint.com/

- FreeCodeCamp
https://www.freecodecamp.org/

- The Odin Project
https://www.theodinproject.com/

- Udemy
https://www.udemy.com/

- Coursera
https://www.coursera.org/

- edX
https://www.edx.org/

- Khan Academy
https://www.khanacademy.org/

- Codecademy
https://www.codecademy.com/

- LeetCode
https://leetcode.com/

- HackerRank
https://www.hackerrank.com/

- Coderbyte
https://coderbyte.com/

- Exercism
https://exercism.org/

- TopCoder
https://www.topcoder.com/

- Codewars
https://www.codewars.com/

- GitHub
https://github.com/

- GitLab
https://gitlab.com/

- Bitbucket
https://bitbucket.org/

- StackBlitz
https://stackblitz.com/

- CodeSandbox
https://codesandbox.io/

- JSFiddle
https://jsfiddle.net/

- CodePen
https://codepen.io/

- Repl.it
https://replit.com/

- Glitch
https://glitch.com/

- Netlify
https://www.netlify.com/

- Vercel
https://vercel.com/

- Heroku
https://www.heroku.com/

- AWS Amplify
https://aws.amazon.com/amplify/

- Firebase
https://firebase.google.com/

- Supabase
https://supabase.io/

- DigitalOcean
https://www.digitalocean.com/

- Linode
https://www.linode.com/

- Vultr
https://www.vultr.com/

- Render
https://render.com/

- Fly.io
https://fly.io/

- Railway
https://railway.app/

- Product Hunt
https://www.producthunt.com/

- Indie Hackers
https://www.indiehackers.com/

- Hacker News
https://news.ycombinator.com/

- Reddit (r/programming, r/webdev, etc.)
https://www.reddit.com/

- Dev.to
https://dev.to/

- Medium (programming blogs)
https://medium.com/

- Hashnode
https://hashnode.com/

- Twitter (follow developers and communities)
https://twitter.com/

- LinkedIn (connect with professionals)
https://www.linkedin.com/

- Discord (join developer servers)
https://discord.com/

- Slack (join developer workspaces)
https://slack.com/

- YouTube (coding tutorials and channels)
https://www.youtube.com/

- Twitch (live coding streams)
https://www.twitch.tv/

- Podcasts (developer-focused podcasts)
(No specific link provided in the tweet for this general category)

- Newsletters (subscribe to tech newsletters)
(No specific link provided in the tweet for this general category)

- Books (classic and modern programming books)
(No specific link provided in the tweet for this general category)

- Conferences and Meetups (attend local or online events)
(No specific link provided in the tweet for this general category)

- Open Source Projects (contribute to projects on GitHub)
(No specific link provided in the tweet for this general category)

- Personal Projects (build your own projects to learn and showcase skills)
(No specific link provided in the tweet for this general category)

- Pair Programming (code with a partner)
(No specific link provided in the tweet for this general category)

- Code Reviews (get feedback on your code and review others' code)
(No specific link provided in the tweet for this general category)

- Debugging Tools (learn to use browser dev tools, IDE debuggers, etc.)
(No specific link provided in the tweet for this general category)

- Version Control (master Git and GitHub/GitLab/Bitbucket)
(No specific link provided in the tweet for this general category)

- Testing Frameworks (learn to write unit, integration, and end-to-end tests)
(No specific link provided in the tweet for this general category)

- CI/CD Tools (learn about Jenkins, GitLab CI, GitHub Actions, etc.)
(No specific link provided in the tweet for this general category)

- Cloud Platforms (get familiar with AWS, Azure, GCP)
(No specific link provided in the tweet for this general category)

- Containers and Orchestration (learn Docker and Kubernetes)
(No specific link provided in the tweet for this general category)

- APIs and Microservices (understand REST, GraphQL, and microservice architecture)
(No specific link provided in the tweet for this general category)

- Databases (learn SQL and NoSQL databases)
(No specific link provided in the tweet for this general category)

- Web Security (learn about OWASP Top 10, HTTPS, CORS, etc.)
(No specific link provided in the tweet for this general category)

- Performance Optimization (learn to optimize frontend and backend performance)
(No specific link provided in the tweet for this general category)

- Accessibility (learn to build accessible websites and applications)
(No specific link provided in the tweet for this general category)

- Soft Skills (communication, teamwork, problem-solving, time management)
(No specific link provided in the tweet for this general category)

- Networking (build your professional network)
(No specific link provided in the tweet for this general category)

- Portfolio (create a portfolio to showcase your projects and skills)
(No specific link provided in the tweet for this general category)

- Job Boards (Indeed, LinkedIn Jobs, Glassdoor, AngelList, etc.)
(No specific link provided in the tweet for this general category)

- Freelancing Platforms (Upwork, Fiverr, Toptal, etc.)
(No specific link provided in the tweet for this general category)

- Tech Blogs and News Sites (TechCrunch, The Verge, Wired, etc.)
(No specific link provided in the tweet for this general category)

- Online Communities (Stack Overflow, Reddit, Dev.to, Hacker News, etc.)
(No specific link provided in the tweet for this general category)

- Mentorship Programs (find a mentor or become a mentor)
(No specific link provided in the tweet for this general category)

- Hackathons (participate in hackathons to build projects and network)
(No specific link provided in the tweet for this general category)

- Coding Challenges (solve daily or weekly coding challenges)
(No specific link provided in the tweet for this general category)

- Stay Curious and Keep Learning (the tech world is always evolving)
(No specific link provided in the tweet for this general category)

Which of these resources do you use the most?

#webdevelopment #coding #programming #developer #softwareengineer #techtwitter #100DaysOfCode https://pbs.twimg.com/media/F-c_49bXAAApQ00?format=jpg&name=orig
    **URL:** [https://twitter.com/Prathkum/status/1722579960140005645](https://twitter.com/Prathkum/status/1722579960140005645)
    **Media:**
      - Type: photo
      - URL: https://t.co/xL9pZ0g0g0
      - Thumbnail: https://pbs.twimg.com/media/F-c_49bXAAApQ00?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/F-c_49bXAAApQ00?format=jpg&name=orig

---