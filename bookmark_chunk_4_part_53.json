[{"id": "1761975124335673837", "created_at": "2024-02-26 07:43:00 +03:00", "full_text": "Currently typing from the floor. https://t.co/991pGV3Gx0", "media": [{"type": "photo", "url": "https://t.co/991pGV3Gx0", "thumbnail": "https://pbs.twimg.com/media/GGy2h9nXAAAW8d3?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GGy2h9nXAAAW8d3?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 13468, "retweet_count": 808, "bookmark_count": 708, "quote_count": 59, "reply_count": 134, "views_count": 18916977, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1761975124335673837", "metadata": {"__typename": "Tweet", "rest_id": "1761975124335673837", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NTU5NDQwMjg2NzkzNjQ2MDk=", "rest_id": "955944028679364609", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1384328976408186881/_uEs8y5G_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Jan 23 23:23:11 +0000 2018", "name": "<PERSON><PERSON><PERSON> Giggle", "screen_name": "CryptoGiggle"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Send thousands of apes to your crypto project with a tweet to our 100k followers! \n\n<EMAIL> to claim 25% off our advertising packages!", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "CryptoGiggle.com", "expanded_url": "http://CryptoGiggle.com", "url": "https://t.co/p1AL0geOKL", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 159224, "followers_count": 99762, "friends_count": 7748, "has_custom_timelines": false, "is_translator": false, "listed_count": 117, "media_count": 719, "normal_followers_count": 99762, "pinned_tweet_ids_str": ["1704002377173647655"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/955944028679364609/1684592258", "profile_interstitial_type": "", "statuses_count": 2300, "translator_type": "none", "url": "https://t.co/p1AL0geOKL", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Sarasota, Florida"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1533083955582361600", "professional_type": "Business", "category": [{"id": 580, "name": "Media & News Company", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1761975124335673837"], "editable_until_msecs": "1708926180000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "18916977", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 708, "bookmarked": true, "created_at": "Mon Feb 26 04:43:00 +0000 2024", "conversation_id_str": "1761975124335673837", "display_text_range": [0, 32], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/991pGV3Gx0", "expanded_url": "https://x.com/CryptoGiggle/status/1761975124335673837/photo/1", "id_str": "1759982248986607616", "indices": [33, 56], "media_key": "3_1759982248986607616", "media_url_https": "https://pbs.twimg.com/media/GGy2h9nXAAAW8d3.jpg", "type": "photo", "url": "https://t.co/991pGV3Gx0", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 909, "w": 960, "resize": "fit"}, "medium": {"h": 909, "w": 960, "resize": "fit"}, "small": {"h": 644, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 909, "width": 960, "focus_rects": [{"x": 0, "y": 0, "w": 960, "h": 538}, {"x": 26, "y": 0, "w": 909, "h": 909}, {"x": 82, "y": 0, "w": 797, "h": 909}, {"x": 253, "y": 0, "w": 455, "h": 909}, {"x": 0, "y": 0, "w": 960, "h": 909}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1759982248986607616"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/991pGV3Gx0", "expanded_url": "https://x.com/CryptoGiggle/status/1761975124335673837/photo/1", "id_str": "1759982248986607616", "indices": [33, 56], "media_key": "3_1759982248986607616", "media_url_https": "https://pbs.twimg.com/media/GGy2h9nXAAAW8d3.jpg", "type": "photo", "url": "https://t.co/991pGV3Gx0", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 909, "w": 960, "resize": "fit"}, "medium": {"h": 909, "w": 960, "resize": "fit"}, "small": {"h": 644, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 909, "width": 960, "focus_rects": [{"x": 0, "y": 0, "w": 960, "h": 538}, {"x": 26, "y": 0, "w": 909, "h": 909}, {"x": 82, "y": 0, "w": 797, "h": 909}, {"x": 253, "y": 0, "w": 455, "h": 909}, {"x": 0, "y": 0, "w": 960, "h": 909}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1759982248986607616"}}}]}, "favorite_count": 13468, "favorited": false, "full_text": "Currently typing from the floor. https://t.co/991pGV3Gx0", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 59, "reply_count": 134, "retweet_count": 808, "retweeted": false, "user_id_str": "955944028679364609", "id_str": "1761975124335673837"}, "twe_private_fields": {"created_at": 1708922580000, "updated_at": 1748554172119, "media_count": 1}}}, {"id": "1769867995923575140", "created_at": "2024-03-19 02:26:27 +03:00", "full_text": "Want to dive into the code and see how LLM-PCI works? Check out the GitHub repository for the source code and detailed explanations: https://t.co/6aEskdQacP", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1769867993943826746", "retweeted_status": null, "quoted_status": null, "favorite_count": 1, "retweet_count": 0, "bookmark_count": 4, "quote_count": 0, "reply_count": 0, "views_count": 246, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1769867995923575140", "metadata": {"__typename": "Tweet", "rest_id": "1769867995923575140", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTY2ODM1MjI2NjU0ODIyNDAx", "rest_id": "1566835226654822401", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/GroqInc", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1346576832800452614/IKTBFFTJ_bigger.png"}, "description": "Groq Inc", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1900415305630773249/gTof6pZR_normal.jpg"}, "core": {"created_at": "Mon Sep 05 17:07:01 +0000 2022", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>_"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Head of Evals @GroqInc | prev. @NousResearch", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "aarushsah.com", "expanded_url": "https://aarushsah.com", "url": "https://t.co/vQSrADv7OU", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8863, "followers_count": 8115, "friends_count": 435, "has_custom_timelines": true, "is_translator": false, "listed_count": 30, "media_count": 270, "normal_followers_count": 8115, "pinned_tweet_ids_str": ["1811399930084548827"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1566835226654822401/1741930628", "profile_interstitial_type": "", "statuses_count": 1745, "translator_type": "none", "url": "https://t.co/vQSrADv7OU", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/6aEskdQacP", "legacy": {"binding_values": [{"key": "description", "value": {"string_value": "Project Injector for Long-Context LLMs. Contribute to AarushSah/LLM-PCI development by creating an account on GitHub.", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "********", "path": []}}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "Project Injector for Long-Context LLMs. Contribute to AarushSah/LLM-PCI development by creating an account on GitHub.", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "Project Injector for Long-Context LLMs. Contribute to AarushSah/LLM-PCI development by creating an account on GitHub.", "type": "STRING"}}, {"key": "title", "value": {"string_value": "GitHub - AarushSah/LLM-PCI: Project Injector for Long-Context LLMs", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/6aEskdQacP", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/6aEskdQacP", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620142, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17800, "media_count": 2646, "normal_followers_count": 2620142, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/********/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1769867995923575140"], "editable_until_msecs": "1710807987000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "246", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 4, "bookmarked": true, "created_at": "Mon Mar 18 23:26:27 +0000 2024", "conversation_id_str": "1769867988021465459", "display_text_range": [0, 156], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/AarushSah/LLM-…", "expanded_url": "https://github.com/AarushSah/LLM-PCI", "url": "https://t.co/6aEskdQacP", "indices": [133, 156]}], "user_mentions": []}, "favorite_count": 1, "favorited": false, "full_text": "Want to dive into the code and see how LLM-PCI works? Check out the GitHub repository for the source code and detailed explanations: https://t.co/6aEskdQacP", "in_reply_to_screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>_", "in_reply_to_status_id_str": "1769867993943826746", "in_reply_to_user_id_str": "1566835226654822401", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1566835226654822401", "id_str": "1769867995923575140"}, "twe_private_fields": {"created_at": 1710804387000, "updated_at": 1748554172118, "media_count": 0}}}]