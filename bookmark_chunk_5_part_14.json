[{"id": "1856719730171633787", "created_at": "2024-11-13 18:24:14 +03:00", "full_text": "Detailed Roadmap to become exceptionally Great at \"X86 assembly programming\" from 0 to exceptional level,👇👇\n\nEven a 5 year old kid can follow this, it's my promise.\n\nPhase 1: Fundamentals of Assembly Programming\n\n1. Introduction to Assembly Language\n  a. Understanding low-level programming concepts\n  b. Differences between high-level and assembly language\n  c. Introduction to machine code and assembly language\n  d. Assembler, linker, and loader: their roles in generating executable programs\n  e. Assembly development environment setup (MASM, NASM, or GAS)\n\n  2. Basic Assembly Language Structure\n  a. General structure of an assembly program\n    -Segment registers: .data, .text, .bss, .rodata\n    -Assembly directives (section, global, etc.)\n b. Assembler syntax (Intel vs. AT&T syntax)\n c. Basic syntax: instructions, labels, comments\n\n3. X86 Architecture Overview\n a. Introduction to x86 architecture\n  -CPU registers (General Purpose Registers: EAX, EBX, ECX, EDX, etc.)\n  -Segment registers (CS, DS, SS, ES, FS, GS)\n  -Special registers (Flags, Instruction Pointer EIP, Stack Pointer ESP, Base Pointer EBP)\nb. 16-bit, 32-bit, and 64-bit differences\nc. The x86 memory model (little-endian, memory segmentation, and paging)\n\nPhase 2: Core Assembly Concepts\n\n1. Instruction Set Architecture (ISA) Basics\n a. Introduction to x86 instruction set\n  -Data transfer instructions (MOV, XCHG, PUSH, POP)\n  -Arithmetic operations (ADD, SUB, MUL, DIV, INC, DEC)\n  -Logic and bitwise operations (AND, OR, XOR, NOT, SHL, SHR)\n  -Control flow instructions (JMP, CALL, RET, JZ, JNZ, JE, JNE, LOOP)\n\n2. Memory Addressing and Data Handling\na. Memory addressing modes\n  -Direct, indirect, register indirect, indexed, and base-indexed addressing\n  -Immediate, direct, and register addressing modes\nb. Working with memory: load and store operations\n  -Accessing data in memory using registers and effective addresses\nc. Stack operations\n  -Using the stack (PUSH, POP, CALL, RET)\n  -Stack frame and stack management\n\n3. Branching and Control Flow\na. Conditional jumps (JZ, JNZ, JL, JG, etc.)\nb. Loop constructs (LOOP, LOOPZ, LOOPNZ)\nc. Function calls (CALL, RET) and stack frame organization\nd. Procedures and recursion in assembly\n\nPhase 3: Intermediate Concepts\n\n1. System Programming with Assembly\na. Working with the system: Interrupts and traps\n  -Using software interrupts (INT instruction)\n  -Handling hardware interrupts and exceptions\n  -DOS and BIOS interrupts (INT 21h, INT 10h)\nb. Interaction with operating system services\n  -Using system calls in Linux (int 0x80, syscall)\n  -Assembly in Windows environment (Win32 API)\n\n2. Advanced Data Manipulation\na. Advanced arithmetic instructions\n  -Multiplication and division (IMUL, IDIV, DIV)\n  -Extended precision operations\nb. Bit manipulation and logical instructions (ROL, ROR, BT, BTS, BTC)\nc. Working with floating-point numbers (FPU, FLD, FSTP, SSE)\n\n3. Macros and Procedures\na. Writing and using macros in assembly\nb. Defining and calling procedures\nc. Parameter passing (registers vs stack)\nd. Local variables and managing stack frames (using EBP)\n\n4. Assembler Directives and Advanced Features\na. Common assembler directives (section, global, extern)\nb. Conditional assembly (IF, ELSE, ENDIF)\nc. Defining and using macros and constants (EQU, DEFINE)\nd. Creating modules and linking assembly code\n\nPhase 4: Advanced Topics in X86 Assembly\n\n1. Optimizing Assembly Code\na. Code optimization techniques for speed and size\n  -Register allocation and instruction scheduling\n  -Reducing memory access and branching\nb. Writing highly efficient loops and minimizing pipeline stalls\nc. Optimizing for modern CPU architectures (superscalar, out-of-order execution)\n\n2. Floating-Point and SIMD Programming\na. Floating-point operations (x87 FPU)\nb. Advanced SIMD instructions (SSE, SSE2, AVX, AVX2)\nc. Using SIMD for vectorized operations and parallelism\n\n3. Memory Management\na. Segmentation and paging in detail\nb. Understanding protected mode and real mode memory access\nc. Virtual memory and paging mechanisms (32-bit vs 64-bit paging)\nd. Memory protection: rings and privilege levels\n\n4. Multithreading and Concurrency\na. Writing thread-safe assembly code\nb. Using atomic operations (XCHG, LOCK prefix)\nc. Working with hardware-level synchronization (spinlocks, barriers)\nd. Assembly-level optimization for parallelism\n\n5. System-Level Programming\na. Writing device drivers in assembly\nb. Working with hardware directly (ports, I/O, memory-mapped I/O)\nc. Direct interaction with peripherals (keyboard, display, etc.)\nd. Low-level interaction with BIOS and UEFI\n\nPhase 5: Mastery and Specialization\n\n1. 64-bit Assembly Programming (x86-64)\na. Differences between x86 and x86-64 architectures\nb. Register extensions (RAX, RBX, RCX, RDX, etc.)\nc. 64-bit addressing and relative addressing\nd. Calling conventions in 64-bit mode (Windows vs Linux)\n\n2. Operating System Development with Assembly\na. Writing bootloaders\n  -BIOS boot sector, loading the OS kernel\nb. Kernel programming in assembly\n  -Writing low-level OS components (interrupt handling, task scheduling)\n  -Memory management and process management in assembly\n\n3. Reverse Engineering and Security\na. Introduction to reverse engineering techniques\n  -Analyzing binaries, disassemblers (Ghidra, IDA Pro)\n  -Working with obfuscated code\nb. Writing shellcode\n  -Understanding buffer overflows, writing exploit code\nc. Malware analysis and debugging assembly code\n  -Using tools like OllyDbg and WinDbg for analyzing assembly\n\nPhase 6: Project-Based Learning and Mastery\n\n1. Small Projects\n  a. Implement basic utilities (calculator, file reader) in assembly\n  b. Create a simple x86 emulator\n  c. Build a bootloader that prints \"Hello, World!\"\n\n2. Medium Projects\n  a. Implement a basic real-mode or protected-mode operating system\n  b. Write an efficient assembler or disassembler\n  c. Create an assembly-level debugger for x86\n\n3. Large Projects\na. Contribute to open-source projects that involve assembly (e.g., GRUB, Linux kernel low-level components)\nb. Write your own JIT (Just-In-Time) compiler\nc. Create a fully functional real-time operating system (RTOS)\n\nFinal Phase: Mastery Assessment\n1. Build a complete operating system kernel or virtual machine in assembly.\n2. Contribute to low-level assembly projects and reverse-engineering challenges.\n3. Teach or mentor or help  others in assembly language.\n\nFor learning purpose use these two websites (it is really-really great for learning, especially for beginner):👇👇\na. https://t.co/ONfNxXPm1s (this one is great)\nb. https://t.co/Tc1DXXBuqA (this one is also great)\n\nMy point of view: Also you can do phase 5 and 6 simultaneously, because it will be useful for you, it would give project based learning experience.\n\n[Note: But I would recommend to learn this after having some fundamental knowledge of \"C programming\", it's just my experience.]\n\nFor appreciation:\n1. Like\n2. Bookmark it\n3. \"Repost\" for more reach\n4. Comment your genuine feedbacks, suggestions etc.\n\n🧠Note: Follow everything sequentially to get the benefits and it is absolutely from 0 and make you exceptionally great at it, even if you are a five year old kid.", "media": [{"type": "photo", "url": "https://t.co/kkrnnipcG0", "thumbnail": "https://pbs.twimg.com/media/GcRkv1bWsAAeSMS?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GcRkv1bWsAAeSMS?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 299, "retweet_count": 57, "bookmark_count": 373, "quote_count": 3, "reply_count": 5, "views_count": 19491, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1856719730171633787", "metadata": {"__typename": "Tweet", "rest_id": "1856719730171633787", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzk1MzU5NjM0MDQ5MzM5Mzky", "rest_id": "1795359634049339392", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1888475909176328192/dMNX_3Je_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> May 28 07:41:42 +0000 2024", "name": "Abhishek🌱", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "21. Learning Machine learning, low level stuffs, System Design\n-learning and Building\n-Just post what I love\nBanger Projects Coming Soon...", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/@<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "expanded_url": "https://www.youtube.com/@Abhishekedutain", "url": "https://t.co/9Fcbg6d1tZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 21860, "followers_count": 19317, "friends_count": 200, "has_custom_timelines": false, "is_translator": false, "listed_count": 94, "media_count": 642, "normal_followers_count": 19317, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1795359634049339392/1738904812", "profile_interstitial_type": "", "statuses_count": 15123, "translator_type": "none", "url": "https://t.co/9Fcbg6d1tZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Tokyo"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1796917294058160581", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1856719730171633787"], "editable_until_msecs": "1731515054000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "19491", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NTY3MTk3Mjk0MjUwMjcwNzI=", "text": "Detailed Roadmap to become exceptionally Great at \"X86 assembly programming\" from 0 to exceptional level,👇👇\n\nEven a 5 year old kid can follow this, it's my promise.\n\nPhase 1: Fundamentals of Assembly Programming\n\n1. Introduction to Assembly Language\n  a. Understanding low-level programming concepts\n  b. Differences between high-level and assembly language\n  c. Introduction to machine code and assembly language\n  d. Assembler, linker, and loader: their roles in generating executable programs\n  e. Assembly development environment setup (MASM, NASM, or GAS)\n\n  2. Basic Assembly Language Structure\n  a. General structure of an assembly program\n    -Segment registers: .data, .text, .bss, .rodata\n    -Assembly directives (section, global, etc.)\n b. Assembler syntax (Intel vs. AT&T syntax)\n c. Basic syntax: instructions, labels, comments\n\n3. X86 Architecture Overview\n a. Introduction to x86 architecture\n  -CPU registers (General Purpose Registers: EAX, EBX, ECX, EDX, etc.)\n  -Segment registers (CS, DS, SS, ES, FS, GS)\n  -Special registers (Flags, Instruction Pointer EIP, Stack Pointer ESP, Base Pointer EBP)\nb. 16-bit, 32-bit, and 64-bit differences\nc. The x86 memory model (little-endian, memory segmentation, and paging)\n\nPhase 2: Core Assembly Concepts\n\n1. Instruction Set Architecture (ISA) Basics\n a. Introduction to x86 instruction set\n  -Data transfer instructions (MOV, XCHG, PUSH, POP)\n  -Arithmetic operations (ADD, SUB, MUL, DIV, INC, DEC)\n  -Logic and bitwise operations (AND, OR, XOR, NOT, SHL, SHR)\n  -Control flow instructions (JMP, CALL, RET, JZ, JNZ, JE, JNE, LOOP)\n\n2. Memory Addressing and Data Handling\na. Memory addressing modes\n  -Direct, indirect, register indirect, indexed, and base-indexed addressing\n  -Immediate, direct, and register addressing modes\nb. Working with memory: load and store operations\n  -Accessing data in memory using registers and effective addresses\nc. Stack operations\n  -Using the stack (PUSH, POP, CALL, RET)\n  -Stack frame and stack management\n\n3. Branching and Control Flow\na. Conditional jumps (JZ, JNZ, JL, JG, etc.)\nb. Loop constructs (LOOP, LOOPZ, LOOPNZ)\nc. Function calls (CALL, RET) and stack frame organization\nd. Procedures and recursion in assembly\n\nPhase 3: Intermediate Concepts\n\n1. System Programming with Assembly\na. Working with the system: Interrupts and traps\n  -Using software interrupts (INT instruction)\n  -Handling hardware interrupts and exceptions\n  -DOS and BIOS interrupts (INT 21h, INT 10h)\nb. Interaction with operating system services\n  -Using system calls in Linux (int 0x80, syscall)\n  -Assembly in Windows environment (Win32 API)\n\n2. Advanced Data Manipulation\na. Advanced arithmetic instructions\n  -Multiplication and division (IMUL, IDIV, DIV)\n  -Extended precision operations\nb. Bit manipulation and logical instructions (ROL, ROR, BT, BTS, BTC)\nc. Working with floating-point numbers (FPU, FLD, FSTP, SSE)\n\n3. Macros and Procedures\na. Writing and using macros in assembly\nb. Defining and calling procedures\nc. Parameter passing (registers vs stack)\nd. Local variables and managing stack frames (using EBP)\n\n4. Assembler Directives and Advanced Features\na. Common assembler directives (section, global, extern)\nb. Conditional assembly (IF, ELSE, ENDIF)\nc. Defining and using macros and constants (EQU, DEFINE)\nd. Creating modules and linking assembly code\n\nPhase 4: Advanced Topics in X86 Assembly\n\n1. Optimizing Assembly Code\na. Code optimization techniques for speed and size\n  -Register allocation and instruction scheduling\n  -Reducing memory access and branching\nb. Writing highly efficient loops and minimizing pipeline stalls\nc. Optimizing for modern CPU architectures (superscalar, out-of-order execution)\n\n2. Floating-Point and SIMD Programming\na. Floating-point operations (x87 FPU)\nb. Advanced SIMD instructions (SSE, SSE2, AVX, AVX2)\nc. Using SIMD for vectorized operations and parallelism\n\n3. Memory Management\na. Segmentation and paging in detail\nb. Understanding protected mode and real mode memory access\nc. Virtual memory and paging mechanisms (32-bit vs 64-bit paging)\nd. Memory protection: rings and privilege levels\n\n4. Multithreading and Concurrency\na. Writing thread-safe assembly code\nb. Using atomic operations (XCHG, LOCK prefix)\nc. Working with hardware-level synchronization (spinlocks, barriers)\nd. Assembly-level optimization for parallelism\n\n5. System-Level Programming\na. Writing device drivers in assembly\nb. Working with hardware directly (ports, I/O, memory-mapped I/O)\nc. Direct interaction with peripherals (keyboard, display, etc.)\nd. Low-level interaction with BIOS and UEFI\n\nPhase 5: Mastery and Specialization\n\n1. 64-bit Assembly Programming (x86-64)\na. Differences between x86 and x86-64 architectures\nb. Register extensions (RAX, RBX, RCX, RDX, etc.)\nc. 64-bit addressing and relative addressing\nd. Calling conventions in 64-bit mode (Windows vs Linux)\n\n2. Operating System Development with Assembly\na. Writing bootloaders\n  -BIOS boot sector, loading the OS kernel\nb. Kernel programming in assembly\n  -Writing low-level OS components (interrupt handling, task scheduling)\n  -Memory management and process management in assembly\n\n3. Reverse Engineering and Security\na. Introduction to reverse engineering techniques\n  -Analyzing binaries, disassemblers (Ghidra, IDA Pro)\n  -Working with obfuscated code\nb. Writing shellcode\n  -Understanding buffer overflows, writing exploit code\nc. Malware analysis and debugging assembly code\n  -Using tools like OllyDbg and WinDbg for analyzing assembly\n\nPhase 6: Project-Based Learning and Mastery\n\n1. Small Projects\n  a. Implement basic utilities (calculator, file reader) in assembly\n  b. Create a simple x86 emulator\n  c. Build a bootloader that prints \"Hello, World!\"\n\n2. Medium Projects\n  a. Implement a basic real-mode or protected-mode operating system\n  b. Write an efficient assembler or disassembler\n  c. Create an assembly-level debugger for x86\n\n3. Large Projects\na. Contribute to open-source projects that involve assembly (e.g., GRUB, Linux kernel low-level components)\nb. Write your own JIT (Just-In-Time) compiler\nc. Create a fully functional real-time operating system (RTOS)\n\nFinal Phase: Mastery Assessment\n1. Build a complete operating system kernel or virtual machine in assembly.\n2. Contribute to low-level assembly projects and reverse-engineering challenges.\n3. Teach or mentor or help  others in assembly language.\n\nFor learning purpose use these two websites (it is really-really great for learning, especially for beginner):👇👇\na. https://t.co/ONfNxXPm1s (this one is great)\nb. https://t.co/Tc1DXXBuqA (this one is also great)\n\nMy point of view: Also you can do phase 5 and 6 simultaneously, because it will be useful for you, it would give project based learning experience.\n\n[Note: But I would recommend to learn this after having some fundamental knowledge of \"C programming\", it's just my experience.]\n\nFor appreciation:\n1. Like\n2. Bookmark it\n3. \"Repost\" for more reach\n4. Comment your genuine feedbacks, suggestions etc.\n\n🧠Note: Follow everything sequentially to get the benefits and it is absolutely from 0 and make you exceptionally great at it, even if you are a five year old kid.", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "mycompiler.io/online-assembl…", "expanded_url": "https://www.mycompiler.io/online-assembly-asm-compiler", "url": "https://t.co/ONfNxXPm1s", "indices": [6531, 6554]}, {"display_url": "godbolt.org", "expanded_url": "https://godbolt.org/", "url": "https://t.co/Tc1DXXBuqA", "indices": [6578, 6601]}], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "community_results": {"result": {"__typename": "Community", "id_str": "17839905***********", "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}}}, "author_community_relationship": {"community_results": {"result": {"__typename": "Community", "id_str": "17839905***********", "name": "C and Assembly Developers", "description": "This community is geared towards accelerated knowledge of low-level computing concepts, and active contribution to projects in C and ASM.", "created_at": *************, "question": "THIS IS MANDITORY:\n\nYour GitHub account or a snippet of code you've written in C or Assembly that you're proud of.", "search_tags": ["programming", "c", "asm", "softwaredevelopment", "cprogramming", "lowlevel", "code", "math", "ai", "ml"], "is_nsfw": false, "primary_community_topic": {"topic_id": "303", "topic_name": "Software"}, "actions": {"delete_action_result": {"__typename": "CommunityDeleteActionUnavailable", "reason": "Unavailable"}, "join_action_result": {"__typename": "CommunityJoinActionUnavailable", "reason": "ViewerIsMember", "message": "You are already a member."}, "leave_action_result": {"__typename": "CommunityLeaveAction"}, "pin_action_result": {"__typename": "CommunityTweetPinActionUnavailable"}}, "admin_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "creator_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "invites_result": {"__typename": "CommunityInvites", "remaining_invite_count": 10, "users_to_invite_slice": {"items": [], "slice_info": {}}}, "join_policy": "Open", "invites_policy": "MemberInvitesAllowed", "is_pinned": true, "members_facepile_results": [{"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDk1ODMzNzcxNTI4NDg2OTEy", "rest_id": "1095833771528486912", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1852982863295361024/nt6Wkcdk_normal.jpg"}, "core": {"created_at": "Wed Feb 13 23:55:05 +0000 2019", "name": "<PERSON><PERSON><PERSON>", "screen_name": "DisperseControl"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I am potato. e/acc. Bulltard.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "brrr.money", "expanded_url": "https://brrr.money/", "url": "https://t.co/LnifTg9G7X", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 638143, "followers_count": 4496, "friends_count": 7347, "has_custom_timelines": true, "is_translator": false, "listed_count": 23, "media_count": 26419, "normal_followers_count": 4496, "pinned_tweet_ids_str": ["1917289486897029355"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1095833771528486912/1652435782", "profile_interstitial_type": "", "statuses_count": 55745, "translator_type": "none", "url": "https://t.co/LnifTg9G7X", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Canazuela"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoyMTkyMDM0MTI=", "rest_id": "219203412", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1920040820654432256/68AZgHjH_normal.jpg"}, "core": {"created_at": "Wed Nov 24 06:15:09 +0000 2010", "name": "Erroneous Input", "screen_name": "<PERSON>_<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "what?", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 62246, "followers_count": 1341, "friends_count": 202, "has_custom_timelines": false, "is_translator": false, "listed_count": 63, "media_count": 18475, "normal_followers_count": 1341, "pinned_tweet_ids_str": ["1777265833670262905"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/219203412/1721665890", "profile_interstitial_type": "", "statuses_count": 79937, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDQxNjUxNjg5MDQyNzg4MzUy", "rest_id": "1041651689042788352", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1846890999634681856/iixBL8be_normal.jpg"}, "core": {"created_at": "Mon Sep 17 11:34:50 +0000 2018", "name": "faulty *ptrrr", "screen_name": "0x_shaq"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Chief pager engineer 📟", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pwner.gg/blog/", "expanded_url": "https://pwner.gg/blog/", "url": "https://t.co/VVHKg0umzt", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10259, "followers_count": 4694, "friends_count": 364, "has_custom_timelines": true, "is_translator": false, "listed_count": 24, "media_count": 604, "normal_followers_count": 4694, "pinned_tweet_ids_str": ["1794342802173890682"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1041651689042788352/1659977667", "profile_interstitial_type": "", "statuses_count": 2346, "translator_type": "none", "url": "https://t.co/VVHKg0umzt", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🇮🇱Israel"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxNTAxMTU2Mzk2NTA2OTM5Mzk1", "rest_id": "1501156396506939395", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1810298224382763008/VVGhTJi4_normal.jpg"}, "core": {"created_at": "<PERSON>e Mar 08 11:23:25 +0000 2022", "name": "<PERSON>raw 🍓", "screen_name": "ddnnddc"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Technologist, major node in the cat distribution system.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/playlist?list=…", "expanded_url": "https://www.youtube.com/playlist?list=PLdVyg0SqO9v_FMdp7oPhmlMSP0kRpziVg", "url": "https://t.co/Itnc8qT9Ow", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 49644, "followers_count": 434, "friends_count": 951, "has_custom_timelines": false, "is_translator": false, "listed_count": 2, "media_count": 960, "normal_followers_count": 434, "pinned_tweet_ids_str": ["1832506180339716148"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1501156396506939395/1720444898", "profile_interstitial_type": "", "statuses_count": 8050, "translator_type": "none", "url": "https://t.co/Itnc8qT9Ow", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Melbourne, Australia"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1693828904694563112", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}], "moderator_count": 9, "member_count": 49694, "role": "Member", "rules": [{"rest_id": "1840492394057511216", "name": "Keep posts on topic.", "description": "Focus discussions on C, Assembly programming, and related topics. Avoid posting unrelated content to maintain quality. This implies no politics.."}, {"rest_id": "1783990929403363417", "name": "Explore and share.", "description": "Encourage curiosity and learning. Share your experiences, code snippets, and resources. Help others by contributing to the collective knowledge."}, {"rest_id": "1783990992099848586", "name": "Provide constructive feedback.", "description": "When critiquing code or concepts, offer clear, constructive, and polite feedback. Aim to help others improve and learn."}, {"rest_id": "1785457160471908430", "name": "Privacy and security.", "description": "Respect the privacy and the integrity of the community."}, {"rest_id": "1795107937150730598", "name": "Low effort bait questions lead to permanent removal.", "description": "Posts that use shallow questions to boost engagement risk permanent removal to uphold discussion quality."}, {"rest_id": "1840493930280014028", "name": "Suggestion-Only Policy: DM @7etsuo for a groupchat invite.", "description": "By joining the community group chat, you agree to keep it a space for suggestions, C-related questions, sharing work, and community suggestions."}], "custom_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 21, "green": 194, "blue": 225}, "percentage": 25.43}, {"rgb": {"red": 58, "green": 154, "blue": 255}, "percentage": 11.73}, {"rgb": {"red": 243, "green": 244, "blue": 250}, "percentage": 10.8}, {"rgb": {"red": 20, "green": 190, "blue": 12}, "percentage": 8.56}, {"rgb": {"red": 226, "green": 97, "blue": 162}, "percentage": 8.44}]}, "original_img_url": "https://pbs.twimg.com/community_banner_img/1827118393209618432/I8no5S8w?format=jpg&name=orig", "original_img_width": 1200, "original_img_height": 480, "salient_rect": {"left": 601, "top": 240, "width": 1, "height": 1}}}, "default_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 1, "green": 161, "blue": 155}, "percentage": 79.35}, {"rgb": {"red": 248, "green": 120, "blue": 132}, "percentage": 11.83}, {"rgb": {"red": 212, "green": 133, "blue": 146}, "percentage": 2.97}, {"rgb": {"red": 129, "green": 175, "blue": 168}, "percentage": 1.95}, {"rgb": {"red": 244, "green": 84, "blue": 97}, "percentage": 0.81}]}, "original_img_url": "https://pbs.twimg.com/media/FECQY8MVEAEnZBg.jpg", "original_img_width": 1200, "original_img_height": 480}}, "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}, "join_requests_result": {"__typename": "CommunityJoinRequestsUnavailable"}}}, "role": "Member", "user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzk1MzU5NjM0MDQ5MzM5Mzky", "rest_id": "1795359634049339392", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1888475909176328192/dMNX_3Je_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> May 28 07:41:42 +0000 2024", "name": "Abhishek🌱", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "21. Learning Machine learning, low level stuffs, System Design\n-learning and Building\n-Just post what I love\nBanger Projects Coming Soon...", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/@<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "expanded_url": "https://www.youtube.com/@Abhishekedutain", "url": "https://t.co/9Fcbg6d1tZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 21860, "followers_count": 19317, "friends_count": 200, "has_custom_timelines": false, "is_translator": false, "listed_count": 94, "media_count": 642, "normal_followers_count": 19317, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1795359634049339392/1738904812", "profile_interstitial_type": "", "statuses_count": 15123, "translator_type": "none", "url": "https://t.co/9Fcbg6d1tZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Tokyo"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1796917294058160581", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "legacy": {"bookmark_count": 373, "bookmarked": true, "created_at": "Wed Nov 13 15:24:14 +0000 2024", "conversation_id_str": "1856719730171633787", "display_text_range": [0, 278], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/kkrnnipcG0", "expanded_url": "https://x.com/Abhishekcur/status/1856719730171633787/photo/1", "id_str": "1856719719341928448", "indices": [279, 302], "media_key": "3_1856719719341928448", "media_url_https": "https://pbs.twimg.com/media/GcRkv1bWsAAeSMS.jpg", "type": "photo", "url": "https://t.co/kkrnnipcG0", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 833, "w": 1200, "resize": "fit"}, "medium": {"h": 833, "w": 1200, "resize": "fit"}, "small": {"h": 472, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 833, "width": 1200, "focus_rects": [{"x": 0, "y": 161, "w": 1200, "h": 672}, {"x": 0, "y": 0, "w": 833, "h": 833}, {"x": 0, "y": 0, "w": 731, "h": 833}, {"x": 0, "y": 0, "w": 417, "h": 833}, {"x": 0, "y": 0, "w": 1200, "h": 833}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1856719719341928448"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/kkrnnipcG0", "expanded_url": "https://x.com/Abhishekcur/status/1856719730171633787/photo/1", "id_str": "1856719719341928448", "indices": [279, 302], "media_key": "3_1856719719341928448", "media_url_https": "https://pbs.twimg.com/media/GcRkv1bWsAAeSMS.jpg", "type": "photo", "url": "https://t.co/kkrnnipcG0", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 833, "w": 1200, "resize": "fit"}, "medium": {"h": 833, "w": 1200, "resize": "fit"}, "small": {"h": 472, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 833, "width": 1200, "focus_rects": [{"x": 0, "y": 161, "w": 1200, "h": 672}, {"x": 0, "y": 0, "w": 833, "h": 833}, {"x": 0, "y": 0, "w": 731, "h": 833}, {"x": 0, "y": 0, "w": 417, "h": 833}, {"x": 0, "y": 0, "w": 1200, "h": 833}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1856719719341928448"}}}]}, "favorite_count": 299, "favorited": false, "full_text": "Detailed Roadmap to become exceptionally Great at \"X86 assembly programming\" from 0 to exceptional level,👇👇\n\nEven a 5 year old kid can follow this, it's my promise.\n\nPhase 1: Fundamentals of Assembly Programming\n\n1. Introduction to Assembly Language\n  a. Understanding low-level https://t.co/kkrnnipcG0", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 3, "reply_count": 5, "retweet_count": 57, "retweeted": false, "user_id_str": "1795359634049339392", "id_str": "1856719730171633787"}, "twe_private_fields": {"created_at": 1731511454000, "updated_at": 1748554133840, "media_count": 1}}}, {"id": "1857510110366511256", "created_at": "2024-11-15 22:44:55 +03:00", "full_text": "You can literally build whatever you want https://t.co/uPUU7ff7fW", "media": [{"type": "photo", "url": "https://t.co/uPUU7ff7fW", "thumbnail": "https://pbs.twimg.com/media/GcczipnX0AA0kt4?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GcczipnX0AA0kt4?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 16840, "retweet_count": 1332, "bookmark_count": 22874, "quote_count": 57, "reply_count": 66, "views_count": 500, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1857510110366511256", "metadata": {"__typename": "Tweet", "rest_id": "1857510110366511256", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDcwMTQ1NDY3OTMzNjcxNDI0", "rest_id": "1470145467933671424", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1722295652451856385/m2SB4PkY_normal.jpg"}, "core": {"created_at": "Sun Dec 12 21:36:27 +0000 2021", "name": "Glitchbyte", "screen_name": "0xglitchbyte"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "10+ years Infra Engineer exploring the bounds of Computing and Christianity. <PERSON> is King. Follow for the journey.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "glitchbyte.io", "expanded_url": "http://glitchbyte.io", "url": "https://t.co/7JTvbHjJzP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 12983, "followers_count": 9213, "friends_count": 378, "has_custom_timelines": false, "is_translator": false, "listed_count": 62, "media_count": 624, "normal_followers_count": 9213, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1470145467933671424/1697467836", "profile_interstitial_type": "", "statuses_count": 5081, "translator_type": "none", "url": "https://t.co/7JTvbHjJzP", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Read my writings at:"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1857510110366511256"], "editable_until_msecs": "1731703495000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "500", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 22874, "bookmarked": true, "created_at": "Fri Nov 15 19:44:55 +0000 2024", "conversation_id_str": "1857510110366511256", "display_text_range": [0, 41], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/uPUU7ff7fW", "expanded_url": "https://x.com/0xglitchbyte/status/1857510110366511256/photo/1", "id_str": "1857510041693179904", "indices": [42, 65], "media_key": "3_1857510041693179904", "media_url_https": "https://pbs.twimg.com/media/GcczipnX0AA0kt4.jpg", "type": "photo", "url": "https://t.co/uPUU7ff7fW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1473, "resize": "fit"}, "medium": {"h": 1200, "w": 863, "resize": "fit"}, "small": {"h": 680, "w": 489, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2990, "width": 2151, "focus_rects": [{"x": 0, "y": 0, "w": 2151, "h": 1205}, {"x": 0, "y": 0, "w": 2151, "h": 2151}, {"x": 0, "y": 0, "w": 2151, "h": 2452}, {"x": 0, "y": 0, "w": 1495, "h": 2990}, {"x": 0, "y": 0, "w": 2151, "h": 2990}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1857510041693179904"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/uPUU7ff7fW", "expanded_url": "https://x.com/0xglitchbyte/status/1857510110366511256/photo/1", "id_str": "1857510041693179904", "indices": [42, 65], "media_key": "3_1857510041693179904", "media_url_https": "https://pbs.twimg.com/media/GcczipnX0AA0kt4.jpg", "type": "photo", "url": "https://t.co/uPUU7ff7fW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1473, "resize": "fit"}, "medium": {"h": 1200, "w": 863, "resize": "fit"}, "small": {"h": 680, "w": 489, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2990, "width": 2151, "focus_rects": [{"x": 0, "y": 0, "w": 2151, "h": 1205}, {"x": 0, "y": 0, "w": 2151, "h": 2151}, {"x": 0, "y": 0, "w": 2151, "h": 2452}, {"x": 0, "y": 0, "w": 1495, "h": 2990}, {"x": 0, "y": 0, "w": 2151, "h": 2990}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1857510041693179904"}}}]}, "favorite_count": 16840, "favorited": false, "full_text": "You can literally build whatever you want https://t.co/uPUU7ff7fW", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 57, "reply_count": 66, "retweet_count": 1332, "retweeted": false, "user_id_str": "1470145467933671424", "id_str": "1857510110366511256"}, "twe_private_fields": {"created_at": 1731699895000, "updated_at": 1748554133840, "media_count": 1}}}]