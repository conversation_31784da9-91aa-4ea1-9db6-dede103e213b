[{"id": "1827722604154585403", "created_at": "2024-08-25 18:00:00 +03:00", "full_text": "11 free Github repos to help you grow as a software engineer, the ones that enable you to “learn by doing” will have the most impact:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 734, "retweet_count": 88, "bookmark_count": 2211, "quote_count": 3, "reply_count": 5, "views_count": 129378, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1827722604154585403", "metadata": {"__typename": "Tweet", "rest_id": "1827722604154585403", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MzUyODU1MjI5Njg3ODg5OTI=", "rest_id": "835285522968788992", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1603235043819720704/faMqzkug_normal.jpg"}, "core": {"created_at": "Sat Feb 25 00:29:22 +0000 2017", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "AI/M<PERSON> @<PERSON><PERSON>, ex-Staff engineer @instagram • Building a newsletter, podcast & hardware side project • Join 100k+ engineers who read my newsletter ↓", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/ryan<PERSON><PERSON>erman", "expanded_url": "http://linktr.ee/ryanlpeterman", "url": "https://t.co/UGlYf5d6jm", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 3201, "followers_count": 17355, "friends_count": 304, "has_custom_timelines": true, "is_translator": false, "listed_count": 157, "media_count": 358, "normal_followers_count": 17355, "pinned_tweet_ids_str": ["1771678222557847764"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/835285522968788992/1713739796", "profile_interstitial_type": "", "statuses_count": 2732, "translator_type": "none", "url": "https://t.co/UGlYf5d6jm", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "NYC"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "salary"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1827722604154585403"], "editable_until_msecs": "1724601600000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "129378", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2211, "bookmarked": true, "created_at": "Sun Aug 25 15:00:00 +0000 2024", "conversation_id_str": "1827722604154585403", "display_text_range": [0, 133], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 734, "favorited": false, "full_text": "11 free Github repos to help you grow as a software engineer, the ones that enable you to “learn by doing” will have the most impact:", "is_quote_status": false, "lang": "en", "quote_count": 3, "reply_count": 5, "retweet_count": 88, "retweeted": false, "user_id_str": "835285522968788992", "id_str": "1827722604154585403"}, "twe_private_fields": {"created_at": 1724598000000, "updated_at": 1748554149588, "media_count": 0}}}, {"id": "1828485695029260356", "created_at": "2024-08-27 20:32:15 +03:00", "full_text": "Remember, for every paid SaaS, there is a free open-source self-hosted alternative:\n\nNotion -> Appflowy\nZoom -> <PERSON><PERSON><PERSON> -> Plane\nAirtable -> NocoDB\nVercel -> Coolify\nHeroku -> Dokku\nFirebase -> Pocketbase/Appwrite/Convex/Supabase\nShopify -> Prestashop\nGitHub -> GitLab\nSlack -> Mattermost\nSalesforce CRM -> ERPNext\nDropbox -> NextCloud\nMailchimp -> Mautic\nTrello -> Wekan\nDocusign -> Docuseal\nCalendly -> Cal dot com\nDatadog -> Prometheus\nGoogle Analytics -> Matomo\nMicrosoft Office 365 -> LibreOffice\nAsana -> OpenProject\n\nwhat else?", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 12792, "retweet_count": 1731, "bookmark_count": 17463, "quote_count": 131, "reply_count": 454, "views_count": 709402, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1828485695029260356", "metadata": {"__typename": "Tweet", "rest_id": "1828485695029260356", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTAzMzM0ODMzMzk3MzA5NDU=", "rest_id": "850333483339730945", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1436819851566219267/HEffZjvP_normal.jpg"}, "core": {"created_at": "Fri Apr 07 13:04:35 +0000 2017", "name": "Fireship", "screen_name": "fireship_dev"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Build and ship your app faster with @JeffDelaney23 🔥📽️ https://t.co/oF4GTcT7UC", "entities": {"description": {"urls": [{"display_url": "youtube.com/c/Fireship", "expanded_url": "http://youtube.com/c/Fireship", "url": "https://t.co/oF4GTcT7UC", "indices": [55, 78]}]}, "url": {"urls": [{"display_url": "fireship.io", "expanded_url": "https://fireship.io", "url": "https://t.co/ZqU0L7jpAR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8850, "followers_count": 197501, "friends_count": 932, "has_custom_timelines": false, "is_translator": false, "listed_count": 910, "media_count": 609, "normal_followers_count": 197501, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/850333483339730945/1650981031", "profile_interstitial_type": "", "statuses_count": 2603, "translator_type": "none", "url": "https://t.co/ZqU0L7jpAR", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Phoenix, AZ"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1685122359903309824", "professional_type": "Creator", "category": [{"id": 1055, "name": "Software developer/Programmer/Software engineer", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1828485695029260356"], "editable_until_msecs": "1724783535000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "709402", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4Mjg0ODU2OTQ5MzY5OTc4ODg=", "text": "Remember, for every paid SaaS, there is a free open-source self-hosted alternative:\n\nNotion -> Appflowy\nZoom -> <PERSON><PERSON><PERSON> -> Plane\nAirtable -> NocoDB\nVercel -> Coolify\nHeroku -> Dokku\nFirebase -> Pocketbase/Appwrite/Convex/Supabase\nShopify -> Prestashop\nGitHub -> GitLab\nSlack -> Mattermost\nSalesforce CRM -> ERPNext\nDropbox -> NextCloud\nMailchimp -> Mautic\nTrello -> Wekan\nDocusign -> Docuseal\nCalendly -> Cal dot com\nDatadog -> Prometheus\nGoogle Analytics -> Matomo\nMicrosoft Office 365 -> LibreOffice\nAsana -> OpenProject\n\nwhat else?", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 17463, "bookmarked": true, "created_at": "<PERSON><PERSON> Aug 27 17:32:15 +0000 2024", "conversation_id_str": "1828485695029260356", "display_text_range": [0, 310], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 12792, "favorited": false, "full_text": "Remember, for every paid SaaS, there is a free open-source self-hosted alternative:\n\nNotion -&gt; Appflowy\nZoom -&gt; Jitsi\nJira -&gt; Plane\nAirtable -&gt; NocoDB\nVercel -&gt; Coolify\nHeroku -&gt; Dokku\nFirebase -&gt; Pocketbase/Appwrite/Convex/Supabase\nShopify -&gt; Prestashop\nGitHub -&gt; GitLab\nSlack -&gt;", "is_quote_status": false, "lang": "en", "quote_count": 131, "reply_count": 454, "retweet_count": 1731, "retweeted": false, "user_id_str": "850333483339730945", "id_str": "1828485695029260356"}, "twe_private_fields": {"created_at": 1724779935000, "updated_at": 1748554149588, "media_count": 0}}}]