[{"id": "1578822563387822080", "created_at": "2022-10-08 22:00:06 +03:00", "full_text": "Do it https://t.co/wbm4kGWOkD https://t.co/2j1bU3Y7Um", "media": [{"type": "photo", "url": "https://t.co/2j1bU3Y7Um", "thumbnail": "https://pbs.twimg.com/media/FekawWEaUAEfTEY?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FekawWEaUAEfTEY?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 9525, "retweet_count": 1200, "bookmark_count": 1266, "quote_count": 132, "reply_count": 76, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1578822563387822080", "metadata": {"__typename": "Tweet", "rest_id": "1578822563387822080", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NjU3NzQ5ODM1MjcwNDMwNzI=", "rest_id": "965774983527043072", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/965775897193275398/LLrUTVUs_normal.jpg"}, "core": {"created_at": "Tue Feb 20 02:27:54 +0000 2018", "name": "Programmer <PERSON><PERSON>", "screen_name": "PR0GRAMMERHUM0R"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "r/Programmer<PERSON><PERSON>or", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 146, "followers_count": 266277, "friends_count": 0, "has_custom_timelines": false, "is_translator": false, "listed_count": 1126, "media_count": 30616, "normal_followers_count": 266277, "pinned_tweet_ids_str": ["1923216535377563656"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/965774983527043072/1532439132", "profile_interstitial_type": "", "statuses_count": 38721, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "cash_app_handle": "Programmer<PERSON><PERSON>or"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1578822563387822080"], "editable_until_msecs": "1665257406000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://www.georgeglessner.com\" rel=\"nofollow\">programmerhumor</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1266, "bookmarked": true, "created_at": "Sat Oct 08 19:00:06 +0000 2022", "conversation_id_str": "1578822563387822080", "display_text_range": [0, 29], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/2j1bU3Y7Um", "expanded_url": "https://x.com/PR0GRAMMERHUM0R/status/1578822563387822080/photo/1", "id_str": "1578822562230194177", "indices": [30, 53], "media_key": "3_1578822562230194177", "media_url_https": "https://pbs.twimg.com/media/FekawWEaUAEfTEY.jpg", "type": "photo", "url": "https://t.co/2j1bU3Y7Um", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 30, "y": 444, "h": 119, "w": 119}]}, "medium": {"faces": [{"x": 30, "y": 444, "h": 119, "w": 119}]}, "small": {"faces": [{"x": 17, "y": 258, "h": 69, "w": 69}]}, "orig": {"faces": [{"x": 30, "y": 444, "h": 119, "w": 119}]}}, "sizes": {"large": {"h": 1168, "w": 750, "resize": "fit"}, "medium": {"h": 1168, "w": 750, "resize": "fit"}, "small": {"h": 680, "w": 437, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1168, "width": 750, "focus_rects": [{"x": 0, "y": 111, "w": 750, "h": 420}, {"x": 0, "y": 0, "w": 750, "h": 750}, {"x": 0, "y": 0, "w": 750, "h": 855}, {"x": 0, "y": 0, "w": 584, "h": 1168}, {"x": 0, "y": 0, "w": 750, "h": 1168}]}, "media_results": {"result": {"media_key": "3_1578822562230194177"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "reddit.com/r/programmerhu…", "expanded_url": "https://www.reddit.com/r/programmerhumor/comments/xywtgf", "url": "https://t.co/wbm4kGWOkD", "indices": [6, 29]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/2j1bU3Y7Um", "expanded_url": "https://x.com/PR0GRAMMERHUM0R/status/1578822563387822080/photo/1", "id_str": "1578822562230194177", "indices": [30, 53], "media_key": "3_1578822562230194177", "media_url_https": "https://pbs.twimg.com/media/FekawWEaUAEfTEY.jpg", "type": "photo", "url": "https://t.co/2j1bU3Y7Um", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 30, "y": 444, "h": 119, "w": 119}]}, "medium": {"faces": [{"x": 30, "y": 444, "h": 119, "w": 119}]}, "small": {"faces": [{"x": 17, "y": 258, "h": 69, "w": 69}]}, "orig": {"faces": [{"x": 30, "y": 444, "h": 119, "w": 119}]}}, "sizes": {"large": {"h": 1168, "w": 750, "resize": "fit"}, "medium": {"h": 1168, "w": 750, "resize": "fit"}, "small": {"h": 680, "w": 437, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1168, "width": 750, "focus_rects": [{"x": 0, "y": 111, "w": 750, "h": 420}, {"x": 0, "y": 0, "w": 750, "h": 750}, {"x": 0, "y": 0, "w": 750, "h": 855}, {"x": 0, "y": 0, "w": 584, "h": 1168}, {"x": 0, "y": 0, "w": 750, "h": 1168}]}, "media_results": {"result": {"media_key": "3_1578822562230194177"}}}]}, "favorite_count": 9525, "favorited": false, "full_text": "Do it https://t.co/wbm4kGWOkD https://t.co/2j1bU3Y7Um", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 132, "reply_count": 76, "retweet_count": 1200, "retweeted": false, "user_id_str": "965774983527043072", "id_str": "1578822563387822080"}, "twe_private_fields": {"created_at": 1665255606000, "updated_at": 1748554235792, "media_count": 1}}}, {"id": "1579032749599784960", "created_at": "2022-10-09 11:55:19 +03:00", "full_text": "@BennetonF1T<PERSON><PERSON> claiming <PERSON> pushed him at the end when he literally did this in spa 💀💀 https://t.co/ikeX2e5e00", "media": [{"type": "video", "url": "https://t.co/ikeX2e5e00", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1579032694595485697/pu/img/FnjqszyU5COGAFI6.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/1280x696/ESQLIyA3XsupHvAN.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1579021405567344640", "retweeted_status": null, "quoted_status": null, "favorite_count": 32, "retweet_count": 1, "bookmark_count": 3, "quote_count": 0, "reply_count": 5, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1579032749599784960", "metadata": {"__typename": "Tweet", "rest_id": "1579032749599784960", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDE0Mjk0MzYxNTIxNjc2Mjk3", "rest_id": "1414294361521676297", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1444292297076715524/vbSlwvZb_normal.jpg"}, "core": {"created_at": "Sun Jul 11 18:43:57 +0000 2021", "name": "mahdi 🇹🇿🇵🇸", "screen_name": "defono<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Free Palestine", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 52180, "followers_count": 466, "friends_count": 449, "has_custom_timelines": true, "is_translator": false, "listed_count": 3, "media_count": 620, "normal_followers_count": 466, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1414294361521676297/1745852342", "profile_interstitial_type": "", "statuses_count": 29298, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1579032749599784960"], "editable_until_msecs": "1665307519000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3, "bookmarked": true, "created_at": "Sun Oct 09 08:55:19 +0000 2022", "conversation_id_str": "1579021405567344640", "display_text_range": [16, 97], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/ikeX2e5e00", "expanded_url": "https://x.com/defonotmahdi/status/1579032749599784960/video/1", "id_str": "1579032694595485697", "indices": [98, 121], "media_key": "7_1579032694595485697", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1579032694595485697/pu/img/FnjqszyU5COGAFI6.jpg", "type": "video", "url": "https://t.co/ikeX2e5e00", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 696, "w": 1280, "resize": "fit"}, "medium": {"h": 653, "w": 1200, "resize": "fit"}, "small": {"h": 370, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 696, "width": 1280, "focus_rects": []}, "video_info": {"aspect_ratio": [160, 87], "duration_millis": 6201, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/pl/zjovIn9oEy-atqRa.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/496x270/hCBUDVyPbe43qnQN.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/662x360/X6XBYo7kbO6Grev-.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/1280x696/ESQLIyA3XsupHvAN.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1579032694595485697"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "1191308171605893121", "name": "🖤", "screen_name": "BennetonF1Team", "indices": [0, 15]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/ikeX2e5e00", "expanded_url": "https://x.com/defonotmahdi/status/1579032749599784960/video/1", "id_str": "1579032694595485697", "indices": [98, 121], "media_key": "7_1579032694595485697", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1579032694595485697/pu/img/FnjqszyU5COGAFI6.jpg", "type": "video", "url": "https://t.co/ikeX2e5e00", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 696, "w": 1280, "resize": "fit"}, "medium": {"h": 653, "w": 1200, "resize": "fit"}, "small": {"h": 370, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 696, "width": 1280, "focus_rects": []}, "video_info": {"aspect_ratio": [160, 87], "duration_millis": 6201, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/pl/zjovIn9oEy-atqRa.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/496x270/hCBUDVyPbe43qnQN.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/662x360/X6XBYo7kbO6Grev-.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1579032694595485697/pu/vid/1280x696/ESQLIyA3XsupHvAN.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1579032694595485697"}}}]}, "favorite_count": 32, "favorited": false, "full_text": "@BennetonF1T<PERSON><PERSON> claiming <PERSON> pushed him at the end when he literally did this in spa 💀💀 https://t.co/ikeX2e5e00", "in_reply_to_screen_name": "BennetonF1Team", "in_reply_to_status_id_str": "1579021405567344640", "in_reply_to_user_id_str": "1191308171605893121", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 5, "retweet_count": 1, "retweeted": false, "user_id_str": "1414294361521676297", "id_str": "1579032749599784960"}, "twe_private_fields": {"created_at": 1665305719000, "updated_at": 1748554235792, "media_count": 1}}}]