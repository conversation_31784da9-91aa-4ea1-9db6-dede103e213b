[{"id": "1734954295655534780", "created_at": "2023-12-13 18:11:52 +03:00", "full_text": "Meet Imagen 2: our most advanced text-to-image diffusion technology. ✨\n\nIt features high-quality, photorealistic outputs and stronger consistency with your prompts. 🖼\n\nNow available to use via @GoogleCloud’s #VertexAI platform. → https://t.co/T1IIJMbIW9 https://t.co/iWIzi2jgZH", "media": [{"type": "photo", "url": "https://t.co/iWIzi2jgZH", "thumbnail": "https://pbs.twimg.com/media/GBPK8F_W4AAGU0s?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GBPK8F_W4AAGU0s?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1040, "retweet_count": 263, "bookmark_count": 239, "quote_count": 45, "reply_count": 48, "views_count": 301814, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1734954295655534780", "metadata": {"__typename": "Tweet", "rest_id": "1734954295655534780", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo0NzgzNjkwMDAy", "rest_id": "4783690002", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1695024885070737408/-M-HSH5P_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Jan 19 13:46:08 +0000 2016", "name": "Google DeepMind", "screen_name": "GoogleDeepMind"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "We’re a team of scientists, engineers, ethicists and more, committed to solving intelligence, to advance science and benefit humanity.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "deepmind.google", "expanded_url": "http://deepmind.google", "url": "https://t.co/uR24kkW95X", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2528, "followers_count": 1149346, "friends_count": 277, "has_custom_timelines": true, "is_translator": false, "listed_count": 12614, "media_count": 1509, "normal_followers_count": 1149346, "pinned_tweet_ids_str": ["1924893528062140417"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/4783690002/1747840726", "profile_interstitial_type": "", "statuses_count": 3828, "translator_type": "none", "url": "https://t.co/uR24kkW95X", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "London, UK"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1608581743665156103", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1734954295655534780"], "editable_until_msecs": "1702483912000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "301814", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 239, "bookmarked": true, "created_at": "Wed Dec 13 15:11:52 +0000 2023", "conversation_id_str": "1734954295655534780", "display_text_range": [0, 253], "entities": {"hashtags": [{"indices": [208, 217], "text": "VertexAI"}], "media": [{"display_url": "pic.x.com/iWIzi2jgZH", "expanded_url": "https://x.com/GoogleDeepMind/status/1734954295655534780/photo/1", "id_str": "1734953415216652288", "indices": [254, 277], "media_key": "3_1734953415216652288", "media_url_https": "https://pbs.twimg.com/media/GBPK8F_W4AAGU0s.jpg", "type": "photo", "url": "https://t.co/iWIzi2jgZH", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 675, "w": 1200, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 675, "width": 1200, "focus_rects": [{"x": 0, "y": 3, "w": 1200, "h": 672}, {"x": 525, "y": 0, "w": 675, "h": 675}, {"x": 608, "y": 0, "w": 592, "h": 675}, {"x": 862, "y": 0, "w": 338, "h": 675}, {"x": 0, "y": 0, "w": 1200, "h": 675}]}, "media_results": {"result": {"media_key": "3_1734953415216652288"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "dpmd.ai/x-imagen2", "expanded_url": "https://dpmd.ai/x-imagen2", "url": "https://t.co/T1IIJMbIW9", "indices": [230, 253]}], "user_mentions": [{"id_str": "19367815", "name": "Google Cloud", "screen_name": "googlecloud", "indices": [193, 205]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/iWIzi2jgZH", "expanded_url": "https://x.com/GoogleDeepMind/status/1734954295655534780/photo/1", "id_str": "1734953415216652288", "indices": [254, 277], "media_key": "3_1734953415216652288", "media_url_https": "https://pbs.twimg.com/media/GBPK8F_W4AAGU0s.jpg", "type": "photo", "url": "https://t.co/iWIzi2jgZH", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 675, "w": 1200, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 675, "width": 1200, "focus_rects": [{"x": 0, "y": 3, "w": 1200, "h": 672}, {"x": 525, "y": 0, "w": 675, "h": 675}, {"x": 608, "y": 0, "w": 592, "h": 675}, {"x": 862, "y": 0, "w": 338, "h": 675}, {"x": 0, "y": 0, "w": 1200, "h": 675}]}, "media_results": {"result": {"media_key": "3_1734953415216652288"}}}]}, "favorite_count": 1040, "favorited": false, "full_text": "Meet Imagen 2: our most advanced text-to-image diffusion technology. ✨\n\nIt features high-quality, photorealistic outputs and stronger consistency with your prompts. 🖼\n\nNow available to use via @GoogleCloud’s #VertexAI platform. → https://t.co/T1IIJMbIW9 https://t.co/iWIzi2jgZH", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 45, "reply_count": 48, "retweet_count": 263, "retweeted": false, "user_id_str": "4783690002", "id_str": "1734954295655534780"}, "twe_private_fields": {"created_at": 1702480312000, "updated_at": 1748554186108, "media_count": 1}}}, {"id": "1734994285299388890", "created_at": "2023-12-13 20:50:46 +03:00", "full_text": "Today's the day! \nMy team and sibling team in Vertex AI have released the SDKs for Gemini API. We put a lot of effort in creating a simple and streamline developer experience. With a few lines of code you can use the Gemini Pro model in Python, JavaScript / Node.js, Kotlin, Swift, Go, and Java.  \nA piece of code worth more than 1000 words, here you can find all the quickstarts, samples, and repos with the implementation of the SDKs:\n\nGoogle AI\nhttps://t.co/mfJhUd5cLU\nPython https://t.co/8Qj8jqGyXZ\nJS https://t.co/bzLlRWOkgh\nAndroid https://t.co/jfEtAxkVSE\nSwift https://t.co/L7bwCzGJ6A\nGo https://t.co/CRqWOiPoNT\n\nVertex AI\nhttps://t.co/tQ2KmxTHeG\nPython https://t.co/fLS9MxxNLQ\nNode https://t.co/0NDBHdTaMP\nJava https://t.co/wri74az26N\nGo https://t.co/OJxTolA3ir \n\n(Dart will come pretty soon, we are finishing up some details)", "media": [{"type": "photo", "url": "https://t.co/Kg8KqlV4GA", "thumbnail": "https://pbs.twimg.com/media/GBPvsSPaUAA3ANh?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GBPvsSPaUAA3ANh?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 98, "retweet_count": 16, "bookmark_count": 35, "quote_count": 2, "reply_count": 6, "views_count": 11383, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1734994285299388890", "metadata": {"__typename": "Tweet", "rest_id": "1734994285299388890", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjg5Nzg2NzM1MTcyMTkwMjA4", "rest_id": "1689786735172190208", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1689786957675855872/OrOf9si6_normal.jpg"}, "core": {"created_at": "Thu Aug 10 23:52:25 +0000 2023", "name": "<PERSON>", "screen_name": "MiguelRamosPM"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Product Manager @Google. Leading Google AI (Gemini) and Firebase SDKs🔥. Ex-Microsoft📎. Opinions are my own. From Sevilla,🇪🇸  @Seattle, 🇺🇸.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/miguelrb/", "expanded_url": "https://www.linkedin.com/in/miguelrb/", "url": "https://t.co/TFLBshUCuB", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 339, "followers_count": 346, "friends_count": 547, "has_custom_timelines": false, "is_translator": false, "listed_count": 2, "media_count": 21, "normal_followers_count": 346, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1689786735172190208/1691720170", "profile_interstitial_type": "", "statuses_count": 169, "translator_type": "none", "url": "https://t.co/TFLBshUCuB", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Seattle, WA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1734994285299388890"], "editable_until_msecs": "1702493446000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "11383", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3MzQ5OTQyODUwNjQ0OTUxMDQ=", "text": "Today's the day! \nMy team and sibling team in Vertex AI have released the SDKs for Gemini API. We put a lot of effort in creating a simple and streamline developer experience. With a few lines of code you can use the Gemini Pro model in Python, JavaScript / Node.js, Kotlin, Swift, Go, and Java.  \nA piece of code worth more than 1000 words, here you can find all the quickstarts, samples, and repos with the implementation of the SDKs:\n\nGoogle AI\nhttps://t.co/mfJhUd5cLU\nPython https://t.co/8Qj8jqGyXZ\nJS https://t.co/bzLlRWOkgh\nAndroid https://t.co/jfEtAxkVSE\nSwift https://t.co/L7bwCzGJ6A\nGo https://t.co/CRqWOiPoNT\n\nVertex AI\nhttps://t.co/tQ2KmxTHeG\nPython https://t.co/fLS9MxxNLQ\nNode https://t.co/0NDBHdTaMP\nJava https://t.co/wri74az26N\nGo https://t.co/OJxTolA3ir \n\n(Dart will come pretty soon, we are finishing up some details)", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "ai.google.dev/docs", "expanded_url": "https://ai.google.dev/docs", "url": "https://t.co/mfJhUd5cLU", "indices": [448, 471]}, {"display_url": "github.com/google/generat…", "expanded_url": "https://github.com/google/generative-ai-python", "url": "https://t.co/8Qj8jqGyXZ", "indices": [479, 502]}, {"display_url": "github.com/google/generat…", "expanded_url": "https://github.com/google/generative-ai-js", "url": "https://t.co/bzLlRWOkgh", "indices": [506, 529]}, {"display_url": "github.com/google/generat…", "expanded_url": "https://github.com/google/generative-ai-android", "url": "https://t.co/jfEtAxkVSE", "indices": [538, 561]}, {"display_url": "github.com/google/generat…", "expanded_url": "https://github.com/google/generative-ai-swift", "url": "https://t.co/L7bwCzGJ6A", "indices": [568, 591]}, {"display_url": "github.com/google/generat…", "expanded_url": "https://github.com/google/generative-ai-go", "url": "https://t.co/CRqWOiPoNT", "indices": [595, 618]}, {"display_url": "cloud.devsite.corp.google.com/vertex-ai/docs…", "expanded_url": "https://cloud.devsite.corp.google.com/vertex-ai/docs/generative-ai/start/quickstarts/quickstart-multimodal", "url": "https://t.co/tQ2KmxTHeG", "indices": [630, 653]}, {"display_url": "github.com/googleapis/pyt…", "expanded_url": "https://github.com/googleapis/python-aiplatform", "url": "https://t.co/fLS9MxxNLQ", "indices": [661, 684]}, {"display_url": "github.com/googleapis/nod…", "expanded_url": "https://github.com/googleapis/nodejs-vertexai", "url": "https://t.co/0NDBHdTaMP", "indices": [690, 713]}, {"display_url": "github.com/googleapis/goo…", "expanded_url": "https://github.com/googleapis/google-cloud-java/tree/main/java-vertexai", "url": "https://t.co/wri74az26N", "indices": [719, 742]}, {"display_url": "github.com/googleapis/goo…", "expanded_url": "https://github.com/googleapis/google-cloud-go/tree/main/vertexai", "url": "https://t.co/OJxTolA3ir", "indices": [746, 769]}], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 438, "to_index": 447, "richtext_types": ["Bold"]}, {"from_index": 471, "to_index": 472, "richtext_types": ["Bold"]}, {"from_index": 620, "to_index": 629, "richtext_types": ["Bold"]}, {"from_index": 653, "to_index": 654, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 35, "bookmarked": true, "created_at": "Wed Dec 13 17:50:46 +0000 2023", "conversation_id_str": "1734994285299388890", "display_text_range": [0, 274], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/Kg8KqlV4GA", "expanded_url": "https://x.com/MiguelRamosPM/status/1734994285299388890/photo/1", "id_str": "1734993825557532672", "indices": [275, 298], "media_key": "3_1734993825557532672", "media_url_https": "https://pbs.twimg.com/media/GBPvsSPaUAA3ANh.jpg", "type": "photo", "url": "https://t.co/Kg8KqlV4GA", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 952, "w": 922, "resize": "fit"}, "medium": {"h": 952, "w": 922, "resize": "fit"}, "small": {"h": 680, "w": 659, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 952, "width": 922, "focus_rects": [{"x": 0, "y": 0, "w": 922, "h": 516}, {"x": 0, "y": 0, "w": 922, "h": 922}, {"x": 87, "y": 0, "w": 835, "h": 952}, {"x": 356, "y": 0, "w": 476, "h": 952}, {"x": 0, "y": 0, "w": 922, "h": 952}]}, "media_results": {"result": {"media_key": "3_1734993825557532672"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/Kg8KqlV4GA", "expanded_url": "https://x.com/MiguelRamosPM/status/1734994285299388890/photo/1", "id_str": "1734993825557532672", "indices": [275, 298], "media_key": "3_1734993825557532672", "media_url_https": "https://pbs.twimg.com/media/GBPvsSPaUAA3ANh.jpg", "type": "photo", "url": "https://t.co/Kg8KqlV4GA", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 952, "w": 922, "resize": "fit"}, "medium": {"h": 952, "w": 922, "resize": "fit"}, "small": {"h": 680, "w": 659, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 952, "width": 922, "focus_rects": [{"x": 0, "y": 0, "w": 922, "h": 516}, {"x": 0, "y": 0, "w": 922, "h": 922}, {"x": 87, "y": 0, "w": 835, "h": 952}, {"x": 356, "y": 0, "w": 476, "h": 952}, {"x": 0, "y": 0, "w": 922, "h": 952}]}, "media_results": {"result": {"media_key": "3_1734993825557532672"}}}]}, "favorite_count": 98, "favorited": false, "full_text": "Today's the day! \nMy team and sibling team in Vertex AI have released the SDKs for Gemini API. We put a lot of effort in creating a simple and streamline developer experience. With a few lines of code you can use the Gemini Pro model in Python, JavaScript / Node.js, Kotlin, https://t.co/Kg8KqlV4GA", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 6, "retweet_count": 16, "retweeted": false, "user_id_str": "1689786735172190208", "id_str": "1734994285299388890"}, "twe_private_fields": {"created_at": 1702489846000, "updated_at": 1748554186108, "media_count": 1}}}]