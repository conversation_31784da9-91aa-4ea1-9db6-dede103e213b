[{"id": "1866748799190004053", "created_at": "2024-12-11 10:36:10 +03:00", "full_text": "I read Google's paper about their quantum computer so you don't have to.\n\nThey claim to have ran a quantum computation in 5 minutes that would take a normal computer 10^25 years.\n\nBut what was that computation? Does it live up to the hype?\n\nI will break it down.🧵", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 24855, "retweet_count": 2955, "bookmark_count": 21670, "quote_count": 438, "reply_count": 507, "views_count": 5879119, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1866748799190004053", "metadata": {"__typename": "Tweet", "rest_id": "1866748799190004053", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxOTI0NjI4MDQ=", "rest_id": "192462804", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/RareSkills_io", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1687322395177287680/yeqrjUIi_bigger.jpg"}, "description": "RareSkills", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1819792891558207488/cktMiiIC_normal.jpg"}, "core": {"created_at": "Sun Sep 19 06:22:22 +0000 2010", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Building the graduate school of blockchain engineering @rareskills_io | ZK Book: https://t.co/reIQL12WfQ", "entities": {"description": {"urls": [{"display_url": "rareskills.io/zk-book", "expanded_url": "http://rareskills.io/zk-book", "url": "https://t.co/reIQL12WfQ", "indices": [81, 104]}]}, "url": {"urls": [{"display_url": "rareskills.io", "expanded_url": "http://rareskills.io", "url": "https://t.co/U8D5tEQ71e", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 20290, "followers_count": 13450, "friends_count": 948, "has_custom_timelines": true, "is_translator": false, "listed_count": 110, "media_count": 203, "normal_followers_count": 13450, "pinned_tweet_ids_str": ["1867023555210383377"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/192462804/1650479939", "profile_interstitial_type": "", "statuses_count": 4854, "translator_type": "none", "url": "https://t.co/U8D5tEQ71e", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Jakarta, Indonesia"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1866748799190004053"], "editable_until_msecs": "1733906170000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "5879119", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 21670, "bookmarked": true, "created_at": "Wed Dec 11 07:36:10 +0000 2024", "conversation_id_str": "1866748799190004053", "display_text_range": [0, 263], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 24855, "favorited": false, "full_text": "I read Google's paper about their quantum computer so you don't have to.\n\nThey claim to have ran a quantum computation in 5 minutes that would take a normal computer 10^25 years.\n\nBut what was that computation? Does it live up to the hype?\n\nI will break it down.🧵", "is_quote_status": false, "lang": "en", "quote_count": 438, "reply_count": 507, "retweet_count": 2955, "retweeted": false, "user_id_str": "192462804", "id_str": "1866748799190004053"}, "twe_private_fields": {"created_at": 1733902570000, "updated_at": 1748554127577, "media_count": 0}}}, {"id": "1866962419781869611", "created_at": "2024-12-12 00:45:02 +03:00", "full_text": "Here is an 80-line Python script of how to use the @GoogleDeepMind Gemini 2.0 Flash Live API for real-time conversations. 🗣️\n\n1. Copy the code from the gist\n2. \"pip install pyaudio websockets\" \n2. \"GEMINI_API_KEY={TOKEN} python live_test.py\"\n3. Talk to Gemini it is fun! https://t.co/Cc0BDlPkXn", "media": [{"type": "photo", "url": "https://t.co/Cc0BDlPkXn", "thumbnail": "https://pbs.twimg.com/media/GejH2FhXMAA4HP2?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GejH2FhXMAA4HP2?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1983, "retweet_count": 310, "bookmark_count": 3037, "quote_count": 14, "reply_count": 19, "views_count": 152788, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1866962419781869611", "metadata": {"__typename": "Tweet", "rest_id": "1866962419781869611", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTQxMDUyOTE2NTcwMjE0NDAw", "rest_id": "1141052916570214400", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1831321531852496896/1yBZG884_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 18 18:39:49 +0000 2019", "name": "<PERSON>", "screen_name": "_phil<PERSON><PERSON>d"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "AI Developer Experience @GoogleDeepMind | prev: Tech Lead at @huggingface, AWS ML Hero 🤗 Sharing my own views and AI News 🧑🏻‍💻 https://t.co/7IosdlNz22", "entities": {"description": {"urls": [{"display_url": "philschmid.de", "expanded_url": "https://www.philschmid.de", "url": "https://t.co/7IosdlNz22", "indices": [127, 150]}]}, "url": {"urls": [{"display_url": "philschmid.de", "expanded_url": "https://www.philschmid.de/", "url": "https://t.co/8BDXIK6omb", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6354, "followers_count": 38135, "friends_count": 976, "has_custom_timelines": false, "is_translator": false, "listed_count": 891, "media_count": 1399, "normal_followers_count": 38135, "pinned_tweet_ids_str": ["1905256740762829172"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1141052916570214400/1725456070", "profile_interstitial_type": "", "statuses_count": 4382, "translator_type": "none", "url": "https://t.co/8BDXIK6omb", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Nürnberg"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1866962419781869611"], "editable_until_msecs": "1733957102000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "152788", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3037, "bookmarked": true, "created_at": "Wed Dec 11 21:45:02 +0000 2024", "conversation_id_str": "1866962419781869611", "display_text_range": [0, 270], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/Cc0BDlPkXn", "expanded_url": "https://x.com/_philschmid/status/1866962419781869611/photo/1", "id_str": "1866961777554042880", "indices": [271, 294], "media_key": "3_1866961777554042880", "media_url_https": "https://pbs.twimg.com/media/GejH2FhXMAA4HP2.jpg", "type": "photo", "url": "https://t.co/Cc0BDlPkXn", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 962, "resize": "fit"}, "medium": {"h": 1200, "w": 564, "resize": "fit"}, "small": {"h": 680, "w": 319, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 4096, "width": 1924, "focus_rects": [{"x": 0, "y": 1817, "w": 1924, "h": 1077}, {"x": 0, "y": 1393, "w": 1924, "h": 1924}, {"x": 0, "y": 1259, "w": 1924, "h": 2193}, {"x": 0, "y": 248, "w": 1924, "h": 3848}, {"x": 0, "y": 0, "w": 1924, "h": 4096}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1866961777554042880"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "4783690002", "name": "Google DeepMind", "screen_name": "GoogleDeepMind", "indices": [51, 66]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/Cc0BDlPkXn", "expanded_url": "https://x.com/_philschmid/status/1866962419781869611/photo/1", "id_str": "1866961777554042880", "indices": [271, 294], "media_key": "3_1866961777554042880", "media_url_https": "https://pbs.twimg.com/media/GejH2FhXMAA4HP2.jpg", "type": "photo", "url": "https://t.co/Cc0BDlPkXn", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 962, "resize": "fit"}, "medium": {"h": 1200, "w": 564, "resize": "fit"}, "small": {"h": 680, "w": 319, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 4096, "width": 1924, "focus_rects": [{"x": 0, "y": 1817, "w": 1924, "h": 1077}, {"x": 0, "y": 1393, "w": 1924, "h": 1924}, {"x": 0, "y": 1259, "w": 1924, "h": 2193}, {"x": 0, "y": 248, "w": 1924, "h": 3848}, {"x": 0, "y": 0, "w": 1924, "h": 4096}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1866961777554042880"}}}]}, "favorite_count": 1983, "favorited": false, "full_text": "Here is an 80-line Python script of how to use the @GoogleDeepMind Gemini 2.0 Flash Live API for real-time conversations. 🗣️\n\n1. Copy the code from the gist\n2. \"pip install pyaudio websockets\" \n2. \"GEMINI_API_KEY={TOKEN} python live_test.py\"\n3. Talk to Gemini it is fun! https://t.co/Cc0BDlPkXn", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 14, "reply_count": 19, "retweet_count": 310, "retweeted": false, "user_id_str": "1141052916570214400", "id_str": "1866962419781869611"}, "twe_private_fields": {"created_at": 1733953502000, "updated_at": 1748554127577, "media_count": 1}}}]