[{"id": "1700127142636376391", "created_at": "2023-09-08 15:41:12 +03:00", "full_text": "Learning Data Structures and Algorithms (DSA) is a crucial part of becoming a proficient programmer.\nHere are some important things that we often overlook while learning DSA:\n\n1. Build a Strong Foundation:\nChoose a language and start with the basics. Understand the fundamentals of data structures and algorithms thoroughly. These are the building blocks of DSA.\n\n2. Understand Time and Space Complexity: \nLearn to analyse the time and space complexity of algorithms. This understanding is critical for evaluating the efficiency of your code.\n\n3. Practice Regularly: \nDSA is a skill that improves with practice. Solve a wide range of problems, from simple to complex. Online platforms like @LeetCode , @hackerrank and @codechef etc offer a variety of problems.\n\n4. Learn Multiple Approaches: \nDon't settle for one solution to a problem. Explore multiple approaches and understand the trade-offs between them. This helps you become a more versatile problem solver.\n\n5. Teach Others: \nThis is one of the most effective ways I can vouch for to solidify one's understanding. Try explaining DSA concepts to others.\n\n6. Participate in Coding Contests: \nIt helps you practice problem-solving, algorithmic thinking, and coding under time constraints, which are essential skills for becoming proficient in DSA.\n\n7. Study Well-Documented Code:\nReview well-documented code examples and open-source projects that use DSA. This can provide valuable insights into best practices and real-world applications.\n\nThe most important skill to develop while learning DSA is patience and consistency. At the beginning, it's common not to solve many questions in a day. It takes time and practice to reach your full potential. Feeling discouraged when you can't solve a problem and thinking about giving up should not even be considered as options.\nRemember, the people who inspire you were once beginners like you, and with hard work, you can achieve what they have. Best of luck, everyone!\n#100DaysOfCode", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 380, "retweet_count": 56, "bookmark_count": 444, "quote_count": 1, "reply_count": 11, "views_count": 78629, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1700127142636376391", "metadata": {"__typename": "Tweet", "rest_id": "1700127142636376391", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjg1NzE2MTUyMzI3MDU3NDA5", "rest_id": "1685716152327057409", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927204414697115648/qQAwbdao_normal.jpg"}, "core": {"created_at": "Sun Jul 30 18:17:31 +0000 2023", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "full stack developer | simplifying web development and programming for you & an occasional yapper.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 7771, "followers_count": 58979, "friends_count": 288, "has_custom_timelines": false, "is_translator": false, "listed_count": 282, "media_count": 489, "normal_followers_count": 58979, "pinned_tweet_ids_str": ["1904176502524256342"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1685716152327057409/1723794902", "profile_interstitial_type": "", "statuses_count": 6203, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "India 🇮🇳"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"initial_tweet_id": "1700126399623774409", "edit_control_initial": {"edit_tweet_ids": ["1700126399623774409", "1700127142636376391"], "editable_until_msecs": "1694180295000", "is_edit_eligible": true, "edits_remaining": "4"}}, "previous_counts": {"bookmark_count": 0, "favorite_count": 0, "quote_count": 0, "reply_count": 0, "retweet_count": 0}, "is_translatable": false, "views": {"count": "78629", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3MDAxMjcxNDIzODg4OTk4NDA=", "text": "Learning Data Structures and Algorithms (DSA) is a crucial part of becoming a proficient programmer.\nHere are some important things that we often overlook while learning DSA:\n\n1. Build a Strong Foundation:\nChoose a language and start with the basics. Understand the fundamentals of data structures and algorithms thoroughly. These are the building blocks of DSA.\n\n2. Understand Time and Space Complexity: \nLearn to analyse the time and space complexity of algorithms. This understanding is critical for evaluating the efficiency of your code.\n\n3. Practice Regularly: \nDSA is a skill that improves with practice. Solve a wide range of problems, from simple to complex. Online platforms like @LeetCode , @hackerrank and @codechef etc offer a variety of problems.\n\n4. Learn Multiple Approaches: \nDon't settle for one solution to a problem. Explore multiple approaches and understand the trade-offs between them. This helps you become a more versatile problem solver.\n\n5. Teach Others: \nThis is one of the most effective ways I can vouch for to solidify one's understanding. Try explaining DSA concepts to others.\n\n6. Participate in Coding Contests: \nIt helps you practice problem-solving, algorithmic thinking, and coding under time constraints, which are essential skills for becoming proficient in DSA.\n\n7. Study Well-Documented Code:\nReview well-documented code examples and open-source projects that use DSA. This can provide valuable insights into best practices and real-world applications.\n\nThe most important skill to develop while learning DSA is patience and consistency. At the beginning, it's common not to solve many questions in a day. It takes time and practice to reach your full potential. Feeling discouraged when you can't solve a problem and thinking about giving up should not even be considered as options.\nRemember, the people who inspire you were once beginners like you, and with hard work, you can achieve what they have. Best of luck, everyone!\n#100DaysOfCode", "entity_set": {"hashtags": [{"indices": [1969, 1983], "text": "100DaysOfCode"}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "253461583", "name": "LeetCode", "screen_name": "LeetCode", "indices": [690, 699]}, {"id_str": "598157131", "name": "HackerRank", "screen_name": "hackerrank", "indices": [702, 713]}, {"id_str": "17706336", "name": "CodeChef", "screen_name": "codechef", "indices": [718, 727]}]}, "richtext": {"richtext_tags": [{"from_index": 0, "to_index": 100, "richtext_types": ["Bold"]}, {"from_index": 176, "to_index": 205, "richtext_types": ["Bold"]}, {"from_index": 364, "to_index": 405, "richtext_types": ["Bold"]}, {"from_index": 544, "to_index": 566, "richtext_types": ["Bold"]}, {"from_index": 762, "to_index": 792, "richtext_types": ["Bold"]}, {"from_index": 965, "to_index": 982, "richtext_types": ["Bold"]}, {"from_index": 1111, "to_index": 1145, "richtext_types": ["Bold"]}, {"from_index": 1303, "to_index": 1333, "richtext_types": ["Bold"]}, {"from_index": 1495, "to_index": 1553, "richtext_types": ["Italic"]}, {"from_index": 1553, "to_index": 1577, "richtext_types": ["Italic", "Bold"]}, {"from_index": 1577, "to_index": 1825, "richtext_types": ["Italic"]}, {"from_index": 1826, "to_index": 1968, "richtext_types": ["Italic", "Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 444, "bookmarked": true, "created_at": "Fri Sep 08 12:41:12 +0000 2023", "conversation_id_str": "1700127142636376391", "display_text_range": [0, 278], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 380, "favorited": false, "full_text": "Learning Data Structures and Algorithms (DSA) is a crucial part of becoming a proficient programmer.\nHere are some important things that we often overlook while learning DSA:\n\n1. Build a Strong Foundation:\nChoose a language and start with the basics. Understand the fundamentals", "is_quote_status": false, "lang": "en", "quote_count": 1, "reply_count": 11, "retweet_count": 56, "retweeted": false, "user_id_str": "1685716152327057409", "id_str": "1700127142636376391"}, "twe_private_fields": {"created_at": 1694176872000, "updated_at": 1748554188923, "media_count": 0}}}, {"id": "1701164784429773274", "created_at": "2023-09-11 12:24:25 +03:00", "full_text": "Full Body Bench 45 Exercises in 1 bench\nThe only workout bench you will ever need to train your whole body. \nCLICK ON THE LINK to see all the exercises  that you can do with it.\nget 36kg FREE weights and a 1.2m bar only for 30,000 KES\ncall/whatsapp 0726696926", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1421, "retweet_count": 167, "bookmark_count": 244, "quote_count": 1, "reply_count": 60, "views_count": 1657258, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1701164784429773274", "metadata": {"__typename": "Tweet", "rest_id": "1701164784429773274", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjMzNzAzNzU2ODQxMjIyMTQ3", "rest_id": "1233703756841222147", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1462408662446706688/o8yuycjS_normal.jpg"}, "core": {"created_at": "Sat Feb 29 10:41:19 +0000 2020", "name": "<PERSON><PERSON><PERSON>", "screen_name": "KhairaUmmahFit"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Gym and Fitness equipment wholesaler for all your Home and Commercial needs.\nKUFITNESS a brand that LIFTS", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "khairaummah.co.ke", "expanded_url": "http://www.khairaummah.co.ke", "url": "https://t.co/R75ZSS9MaM", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 436, "followers_count": 2361, "friends_count": 223, "has_custom_timelines": false, "is_translator": false, "listed_count": 5, "media_count": 242, "normal_followers_count": 2361, "pinned_tweet_ids_str": ["1908048588254622069"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1233703756841222147/1734192361", "profile_interstitial_type": "", "statuses_count": 2507, "translator_type": "none", "url": "https://t.co/R75ZSS9MaM", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Nairobi, Kenya"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1654931402117328898", "professional_type": "Business", "category": [{"id": 867, "name": "Gym/Physical Fitness Center", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "card://1701164781611212801", "legacy": {"binding_values": [{"key": "unified_card", "value": {"string_value": "{\"type\":\"video_website\",\"component_objects\":{\"details_1\":{\"type\":\"details\",\"data\":{\"title\":{\"content\":\"FULL BODY workout bench with 36kg weights @30k ONLY\",\"is_rtl\":false},\"subtitle\":{\"content\":\"khairaummah.co.ke\",\"is_rtl\":false},\"destination\":\"override_browser_with_docked_media_1\"}},\"media_1\":{\"type\":\"media\",\"data\":{\"id\":\"13_1696884435163955200\",\"destination\":\"browser_with_docked_media_1\"}}},\"destination_objects\":{\"browser_with_docked_media_1\":{\"type\":\"browser_with_docked_media\",\"data\":{\"url_data\":{\"url\":\"https://khairaummah.co.ke/product/full-body-workout-bench/\",\"vanity\":\"khairaummah.co.ke\"},\"media_id\":\"13_1696884435163955200\"}},\"override_browser_with_docked_media_1\":{\"type\":\"browser\",\"data\":{\"url_data\":{\"url\":\"https://khairaummah.co.ke/product/full-body-workout-bench/\",\"vanity\":\"khairaummah.co.ke\"}}}},\"components\":[\"media_1\",\"details_1\"],\"media_entities\":{\"13_1696884435163955200\":{\"id\":1696884435163955200,\"id_str\":\"1696884435163955200\",\"indices\":[0,0],\"media_url\":\"\",\"media_url_https\":\"https://pbs.twimg.com/amplify_video_thumb/1696884435163955200/img/3MeBkD0Xx_izShah.jpg\",\"url\":\"\",\"display_url\":\"\",\"expanded_url\":\"\",\"type\":\"video\",\"original_info\":{\"width\":1280,\"height\":720},\"sizes\":{\"large\":{\"w\":1280,\"h\":720,\"resize\":\"fit\"},\"medium\":{\"w\":1200,\"h\":675,\"resize\":\"fit\"},\"thumb\":{\"w\":150,\"h\":150,\"resize\":\"crop\"},\"small\":{\"w\":680,\"h\":383,\"resize\":\"fit\"}},\"source_user_id\":1233703756841222147,\"source_user_id_str\":\"1233703756841222147\",\"video_info\":{\"aspect_ratio\":[16,9],\"duration_millis\":53734,\"variants\":[{\"bitrate\":2176000,\"content_type\":\"video/mp4\",\"url\":\"https://video.twimg.com/amplify_video/1696884435163955200/vid/1280x720/6A7LzsAMx_oScAF7.mp4?tag=14\"},{\"bitrate\":832000,\"content_type\":\"video/mp4\",\"url\":\"https://video.twimg.com/amplify_video/1696884435163955200/vid/640x360/pzo0L34wOP1f_6w0.mp4?tag=14\"},{\"bitrate\":288000,\"content_type\":\"video/mp4\",\"url\":\"https://video.twimg.com/amplify_video/1696884435163955200/vid/480x270/0z2_9iqFGToZ-akT.mp4?tag=14\"},{\"content_type\":\"application/x-mpegURL\",\"url\":\"https://video.twimg.com/amplify_video/1696884435163955200/pl/j9t2C9llgdmPcxVN.m3u8?tag=14\"}]},\"media_key\":\"13_1696884435163955200\",\"ext\":{\"mediaColor\":{\"r\":{\"ok\":{\"palette\":[{\"rgb\":{\"red\":0,\"green\":0,\"blue\":0},\"percentage\":66.57},{\"rgb\":{\"red\":145,\"green\":143,\"blue\":140},\"percentage\":11.66},{\"rgb\":{\"red\":251,\"green\":119,\"blue\":140},\"percentage\":3.7},{\"rgb\":{\"red\":234,\"green\":211,\"blue\":5},\"percentage\":1.5},{\"rgb\":{\"red\":158,\"green\":1,\"blue\":2},\"percentage\":1.25}]}},\"ttl\":-1}}}}}", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://twitter.com", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "unified_card", "url": "card://1701164781611212801", "user_refs_results": []}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1701164784429773274"], "editable_until_msecs": "1694427865000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1657258", "state": "EnabledWithCount"}, "source": "<a href=\"https://help.twitter.com/en/using-twitter/how-to-tweet#source-labels\" rel=\"nofollow\">advertiser-interface</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 244, "bookmarked": true, "created_at": "Mon Sep 11 09:24:25 +0000 2023", "conversation_id_str": "1701164784429773274", "display_text_range": [0, 259], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1421, "favorited": false, "full_text": "Full Body Bench 45 Exercises in 1 bench\nThe only workout bench you will ever need to train your whole body. \nCLICK ON THE LINK to see all the exercises  that you can do with it.\nget 36kg FREE weights and a 1.2m bar only for 30,000 KES\ncall/whatsapp 0726696926", "is_quote_status": false, "lang": "en", "quote_count": 1, "reply_count": 60, "retweet_count": 167, "retweeted": false, "scopes": {"followers": false}, "user_id_str": "1233703756841222147", "id_str": "1701164784429773274"}, "twe_private_fields": {"created_at": 1694424265000, "updated_at": 1748554186108, "media_count": 0}}}]