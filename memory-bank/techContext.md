# Technical Context: Twitter Bookmarks HTML Viewer

## 1. Technologies Used

*   **HTML5:** For structuring the content of the bookmark viewer.
*   **CSS3:** For styling the user interface, ensuring a clean and responsive design.
*   **JavaScript (ES6+):** For all client-side logic, including data parsing, dynamic rendering of bookmarks, categorization, and interactive navigation.
*   **JSON:** The input data format for Twitter bookmarks.

## 2. Development Setup

*   **Editor:** VS Code (as indicated by the environment).
*   **Local Server (Optional for viewing):** A simple local HTTP server can be used to serve the generated `bookmarks.html` file during development, though directly opening the file in a browser will also work.
*   **No Build Tools:** Given the requirement for a single, self-contained HTML file, complex build tools (like Webpack, Parcel, etc.) will be avoided to keep the setup simple and the output clean. All CSS and JavaScript will be embedded directly.

## 3. Dependencies

*   **External Libraries:** No external JavaScript or CSS libraries will be used to keep the `bookmarks.html` file entirely self-contained and minimize its size. All functionality will be implemented using vanilla JavaScript and CSS.
*   **Input Data:** The project depends on the presence of `bookmark_chunk_*.json` files in the working directory.

## 4. Tool Usage Patterns

*   **`read_file`:** To inspect the structure of the `bookmark_chunk_*.json` files and other Memory Bank documents.
*   **`write_to_file`:** To create the final `bookmarks.html` file and update Memory Bank documents.
*   **`list_files`:** To identify all `bookmark_chunk_*.json` files in the directory.
*   **`execute_command`:** Potentially for running a local server for testing, or for any data processing script if we decide to use a separate script for JSON parsing and HTML generation.
*   **`browser_action`:** To verify the generated HTML file in a browser.
