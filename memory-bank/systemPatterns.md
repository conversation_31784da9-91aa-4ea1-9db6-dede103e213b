# System Patterns: Twitter Bookmarks HTML Viewer

## 1. System Architecture

The system will consist of two main parts:

1.  **Data Processing & HTML Generation (Offline Script):**
    *   A script (language to be decided, e.g., Python or Node.js) will be responsible for:
        *   Reading and parsing one or more input JSON files (`bookmark_chunk_*.json`).
        *   Consolidating the bookmark data.
        *   Performing categorization based on defined logic (e.g., hashtags, keywords).
        *   Generating a single, static HTML file.
    *   This script is a development-time tool and not part of the final user-facing product.

2.  **Static HTML Application (User-Facing Product):**
    *   A single HTML file (`bookmarks.html`).
    *   This file will contain:
        *   The HTML structure for the layout (categories, bookmark display area).
        *   Embedded CSS for styling, ensuring a good UI.
        *   Embedded JavaScript for interactivity and dynamic content rendering.
        *   The consolidated and categorized bookmark data, likely embedded as a JSON object within a `<script>` tag.
    *   The application runs entirely client-side in the user's web browser. No backend server is required for viewing the bookmarks.

```mermaid
graph TD
    subgraph Offline Script
        JSON1[bookmark_chunk_1.json] --> P[Parser & Categorizer]
        JSON2[bookmark_chunk_2.json] --> P
        JSONN[bookmark_chunk_n.json] --> P
        P --> HTMLGen[HTML Generator]
    end

    HTMLGen --> OutputHTML[bookmarks.html]

    subgraph Client-Side Application (bookmarks.html)
        OutputHTML --> Browser[User's Web Browser]
        Browser -- Renders --> PageLayout[HTML Structure + CSS Styling]
        Browser -- Executes --> JSLogic[Embedded JavaScript]
        JSLogic -- Uses --> EmbeddedData[Embedded Bookmark Data]
        JSLogic -- Manipulates --> PageLayout
    end
```

## 2. Key Technical Decisions (High-Level)

*   **Static Site Generation:** The primary output is a single static HTML file. This maximizes portability, allows offline viewing, and simplifies deployment (just open the file).
*   **Embedded Data:** All bookmark data will be embedded directly into the HTML file. This keeps the application self-contained.
*   **Client-Side Logic:** All dynamic behavior (displaying categories, filtering bookmarks, rendering content) will be handled by JavaScript running in the browser.
*   **Categorization Strategy:** A strategy for categorizing bookmarks needs to be defined. Initial thoughts include using hashtags present in tweets or common keywords. This might evolve.
*   **UI Framework/Library:** For simplicity and to avoid external dependencies in the final HTML, vanilla JavaScript and CSS will be preferred. If a very lightweight CSS framework is considered, it must be embeddable.

## 3. Design Patterns (Conceptual)

*   **Data Embedding:** Bookmark data will be embedded as a JavaScript variable (likely an array of objects) within the HTML.
*   **Dynamic List/Table Rendering:** JavaScript will dynamically create and populate HTML elements (e.g., `<ul>`, `<table>`) to display categories and bookmarks.
*   **Event Handling:** JavaScript event listeners will manage user interactions (e.g., clicking a category to filter bookmarks).

## 4. Component Relationships (within the HTML application)

*   **HTML Structure:** Defines the main layout areas (e.g., a sidebar for categories, a main content area for bookmarks).
*   **CSS:** Styles all visual elements for a clean and intuitive UI.
*   **JavaScript:**
    *   `data_loader.js` (conceptual): Responsible for accessing the embedded bookmark data.
    *   `ui_builder.js` (conceptual): Handles the creation of category lists and bookmark displays in the DOM.
    *   `event_handler.js` (conceptual): Manages user interactions and updates the view accordingly.
    *   These might not be separate files but distinct blocks of logic within the embedded script.

## 5. Critical Implementation Paths

1.  **JSON Parsing:** Reliably parse the structure of the Twitter bookmark JSON files. The exact structure needs to be inspected from `bookmark_chunk_1.json`.
2.  **Categorization Logic:** Develop a robust way to categorize tweets. This is key to the UX.
3.  **HTML/CSS Layout:** Design a responsive and user-friendly layout.
4.  **JavaScript Rendering Engine:** Write efficient JavaScript to render potentially large lists of bookmarks and handle category filtering without performance issues.
