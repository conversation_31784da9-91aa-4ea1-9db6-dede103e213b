# Bookmarks from bookmark_chunk_4_part_34.json

## Category: Education / Programming / Data Structures / Algorithms / Learning Plan / Resources / #100DaysOfCode

*   **Author:** @ainasanghi
    **Date:** 2023-09-14 13:47:30 +03:00
    **Content:** Step by Step guide DSA in 100 days with resources :

Day ♾️to 0:

- Stick  to a programming language like C/C++ or Java. 
- Make sure you're comfortable with pointers/objects.

Day 1:

- Understanding the concept of algorithmic complexity . 
- Skip the theory for now, but for every piece of code you write, you should be able to derive both time and space complexity.

Day 2-10 :

Start with some simple data structures,
- Arrays
- Linked List
- Strings 
- Stacks 
- Queues

Understand their basic operations ( insert, delete, search, traversal) and their complexity - Big O Algorithm Complexity Cheat sheet, and code them all.

Day 11-25 :

Now learn some simple algorithms :
- Sorting 
- Search
- Prime Numbers
- Strings
- Miscellaneous

Resources:
For this you look to <PERSON>'s youtube channel. He is simply brilliant at explaining algorithms. Watch his algorithm playlist in order

Day 26-50 :

Once you are comfortable with everything above, start doing problems from
- Leetcode
- GeeksforGeeks
- HackerRank
- Codeforces
- InterviewBit
- Cracking the coding interview
- Elements of programming Interviews

Stick to chapters of arrays, linked list, strings, stacks, queues, and complexity.

Day 51-60 :

Learn some non-linear data structures now

- Tree
      ~ Binary Tree, Binary Search Tree
      ~ Heaps
- Hash Table
- Graph

Day 61-90 :

Refer to the previous resources and start doing problems from trees, hash tables, heaps and graphs.

Day 91-100:

Understand:
- Computational complexity theory
- and NP- completeness
- Knapsack problem
- Travelling Salesman problem
- SAT problem

Day 101-♾️:

You are now better than most of the CS undergrads.

Keep revising the previous topics and start competitive programming! Good luck!
    **URL:** [https://twitter.com/ainasanghi/status/1702272855801741606](https://twitter.com/ainasanghi/status/1702272855801741606)

---

## Category: Life Hacks / Cleaning / Household Tips / Chemistry / Reply

*   **Author:** @ericson4smith
    **Date:** 2023-09-18 02:47:05 +03:00
    **Content:** @TansuYegen It’s all about the vinegar.

And something called “dwell time”.
Vinegar is a pretty strong acid but it needs some dwell time to eat into where you put it.
The soap and tissue paper holds it in place so it does not immediately run off.

(I run cleaning companies)
    **URL:** [https://twitter.com/ericson4smith/status/1703556207573946860](https://twitter.com/ericson4smith/status/1703556207573946860)
    **In Reply To:** Tweet [1703430593961189759](https://twitter.com/TansuYegen/status/1703430593961189759) by @TansuYegen

---