[{"id": "1870517272915411428", "created_at": "2024-12-21 20:10:45 +03:00", "full_text": "great malloc tutorial blog in C\n-it will teach you how to write malloc in C \n-code is also present\n-great for understanding malloc in C https://t.co/dYRvxzMkiu", "media": [{"type": "photo", "url": "https://t.co/dYRvxzMkiu", "thumbnail": "https://pbs.twimg.com/media/GfVpa2dWcAAHoeR?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GfVpa2dWcAAHoeR?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 432, "retweet_count": 55, "bookmark_count": 411, "quote_count": 0, "reply_count": 3, "views_count": 18797, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1870517272915411428", "metadata": {"__typename": "Tweet", "rest_id": "1870517272915411428", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzk1MzU5NjM0MDQ5MzM5Mzky", "rest_id": "1795359634049339392", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1888475909176328192/dMNX_3Je_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> May 28 07:41:42 +0000 2024", "name": "Abhishek🌱", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "21. Learning Machine learning, low level stuffs, System Design\n-learning and Building\n-Just post what I love\nBanger Projects Coming Soon...", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/@<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "expanded_url": "https://www.youtube.com/@Abhishekedutain", "url": "https://t.co/9Fcbg6d1tZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 21860, "followers_count": 19317, "friends_count": 200, "has_custom_timelines": false, "is_translator": false, "listed_count": 94, "media_count": 642, "normal_followers_count": 19317, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1795359634049339392/1738904812", "profile_interstitial_type": "", "statuses_count": 15123, "translator_type": "none", "url": "https://t.co/9Fcbg6d1tZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Tokyo"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1796917294058160581", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1870517272915411428"], "editable_until_msecs": "1734804645000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "18797", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 411, "bookmarked": true, "created_at": "Sat Dec 21 17:10:45 +0000 2024", "conversation_id_str": "1870517272915411428", "display_text_range": [0, 135], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/dYRvxzMkiu", "expanded_url": "https://x.com/Abhishekcur/status/1870517272915411428/photo/1", "id_str": "1870517130258444288", "indices": [136, 159], "media_key": "3_1870517130258444288", "media_url_https": "https://pbs.twimg.com/media/GfVpa2dWcAAHoeR.jpg", "type": "photo", "url": "https://t.co/dYRvxzMkiu", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 852, "w": 1584, "resize": "fit"}, "medium": {"h": 645, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 852, "width": 1584, "focus_rects": [{"x": 63, "y": 0, "w": 1521, "h": 852}, {"x": 732, "y": 0, "w": 852, "h": 852}, {"x": 837, "y": 0, "w": 747, "h": 852}, {"x": 1158, "y": 0, "w": 426, "h": 852}, {"x": 0, "y": 0, "w": 1584, "h": 852}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870517130258444288"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/dYRvxzMkiu", "expanded_url": "https://x.com/Abhishekcur/status/1870517272915411428/photo/1", "id_str": "1870517130258444288", "indices": [136, 159], "media_key": "3_1870517130258444288", "media_url_https": "https://pbs.twimg.com/media/GfVpa2dWcAAHoeR.jpg", "type": "photo", "url": "https://t.co/dYRvxzMkiu", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 852, "w": 1584, "resize": "fit"}, "medium": {"h": 645, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 852, "width": 1584, "focus_rects": [{"x": 63, "y": 0, "w": 1521, "h": 852}, {"x": 732, "y": 0, "w": 852, "h": 852}, {"x": 837, "y": 0, "w": 747, "h": 852}, {"x": 1158, "y": 0, "w": 426, "h": 852}, {"x": 0, "y": 0, "w": 1584, "h": 852}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870517130258444288"}}}]}, "favorite_count": 432, "favorited": false, "full_text": "great malloc tutorial blog in C\n-it will teach you how to write malloc in C \n-code is also present\n-great for understanding malloc in C https://t.co/dYRvxzMkiu", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 3, "retweet_count": 55, "retweeted": false, "user_id_str": "1795359634049339392", "id_str": "1870517272915411428"}, "twe_private_fields": {"created_at": 1734801045000, "updated_at": 1748554120680, "media_count": 1}}}, {"id": "1870782024203858222", "created_at": "2024-12-22 13:42:46 +03:00", "full_text": "Real Ethical Hacking in 43 Hours: Your Fast-Track to Cybersecurity Mastery\n\n📲 Key Topics Covered:\n\n * Ethical Hacking Foundations\n * Introduction to Ethical Hacking\n * Ethical Hacking Steps\n * Creating Your Ethical Hacking Lab\n * Operating System Fundamentals\n * Vulnerability Assessment\n * OSINT Techniques\n * Storage Media\n * Linux Basics\n * Linux Shell\n * Linux Processes\n * Linux Permissions\n * Network Security Concepts\n * Packet Management Systems\n * Network Security\n * Linux File System\n * Working with Archives\n * Working with Processes\n * Working with Users\n * Networking Fundamentals\n * Network Capture\n * Network Scanning\n * Advanced Networking Topics\n * Information Gathering\n * Web Application Hacking\n * Detecting Web Vulnerabilities\n * The Importance of Programming\n * C++ and C\n * SQL and Relational Databases\n * Functions in C++\n * Ethical Hacking for Data Scientists\n * Ethical Hacking for SQL Datatypes\n * Learning Python for Ethical Hacking\n\nGain hands-on experience with industry-standard tools like Kali Linux, Metasploit, and Nmap. Prepare for and ace certifications like CSEH and CEH.\n\nTo get the link - \n🙌 Follow \n🙌 Like & Retweet \n🙌 Reply \"CEH\" \n\n🚀 I will DM the link to everyone", "media": [{"type": "photo", "url": "https://t.co/7a93fVH8VG", "thumbnail": "https://pbs.twimg.com/media/GfZaVIsWEAACsfV?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GfZaVIsWEAACsfV?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/7a93fVH8VG", "thumbnail": "https://pbs.twimg.com/media/GfZaVImWkAAwyp3?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GfZaVImWkAAwyp3?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/7a93fVH8VG", "thumbnail": "https://pbs.twimg.com/media/GfZaVIsWQAAJtBN?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GfZaVIsWQAAJtBN?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/7a93fVH8VG", "thumbnail": "https://pbs.twimg.com/media/GfZaVIlWMAAedi5?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GfZaVIlWMAAedi5?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1548, "retweet_count": 592, "bookmark_count": 1253, "quote_count": 9, "reply_count": 799, "views_count": 109044, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1870782024203858222", "metadata": {"__typename": "Tweet", "rest_id": "1870782024203858222", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjg5NTEyMzkxMzAwNzUxMzYy", "rest_id": "1689512391300751362", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1690065444978458624/x_R4nuhD_normal.jpg"}, "core": {"created_at": "Thu Aug 10 05:42:03 +0000 2023", "name": "𝕏 Bug Bounty Writeups 𝕏", "screen_name": "bountywriteups"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "🔍 Bug Bounty Hunter | Content Creator | Sharing cybersecurity write-ups & resources | AI | | by @piyush_supiy #bugbounty #bugbountytips", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "securitycipher.com/bounty-writeups", "expanded_url": "https://securitycipher.com/bounty-writeups", "url": "https://t.co/oqBws2isxj", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6819, "followers_count": 31792, "friends_count": 4237, "has_custom_timelines": false, "is_translator": false, "listed_count": 147, "media_count": 3129, "normal_followers_count": 31792, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1689512391300751362/1691777987", "profile_interstitial_type": "", "statuses_count": 16244, "translator_type": "none", "url": "https://t.co/oqBws2isxj", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Security, CO"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1690071842726580377", "professional_type": "Creator", "category": [{"id": 1041, "name": "Digital Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1870782024203858222"], "editable_until_msecs": "1734867766000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "109044", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NzA3ODIwMjQwMzE4MTc3Mjg=", "text": "Real Ethical Hacking in 43 Hours: Your Fast-Track to Cybersecurity Mastery\n\n📲 Key Topics Covered:\n\n * Ethical Hacking Foundations\n * Introduction to Ethical Hacking\n * Ethical Hacking Steps\n * Creating Your Ethical Hacking Lab\n * Operating System Fundamentals\n * Vulnerability Assessment\n * OSINT Techniques\n * Storage Media\n * Linux Basics\n * Linux Shell\n * Linux Processes\n * Linux Permissions\n * Network Security Concepts\n * Packet Management Systems\n * Network Security\n * Linux File System\n * Working with Archives\n * Working with Processes\n * Working with Users\n * Networking Fundamentals\n * Network Capture\n * Network Scanning\n * Advanced Networking Topics\n * Information Gathering\n * Web Application Hacking\n * Detecting Web Vulnerabilities\n * The Importance of Programming\n * C++ and C\n * SQL and Relational Databases\n * Functions in C++\n * Ethical Hacking for Data Scientists\n * Ethical Hacking for SQL Datatypes\n * Learning Python for Ethical Hacking\n\nGain hands-on experience with industry-standard tools like Kali Linux, Metasploit, and Nmap. Prepare for and ace certifications like CSEH and CEH.\n\nTo get the link - \n🙌 Follow \n🙌 Like & Retweet \n🙌 Reply \"CEH\" \n\n🚀 I will DM the link to everyone", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 1253, "bookmarked": true, "created_at": "Sun Dec 22 10:42:46 +0000 2024", "conversation_id_str": "1870782024203858222", "display_text_range": [0, 276], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014376251392", "indices": [277, 300], "media_key": "3_1870782014376251392", "media_url_https": "https://pbs.twimg.com/media/GfZaVIsWEAACsfV.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1575, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 898, "resize": "fit"}, "small": {"h": 680, "w": 509, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1575, "width": 1178, "focus_rects": [{"x": 0, "y": 811, "w": 1178, "h": 660}, {"x": 0, "y": 397, "w": 1178, "h": 1178}, {"x": 0, "y": 232, "w": 1178, "h": 1343}, {"x": 0, "y": 0, "w": 788, "h": 1575}, {"x": 0, "y": 0, "w": 1178, "h": 1575}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014376251392"}}}, {"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014351118336", "indices": [277, 300], "media_key": "3_1870782014351118336", "media_url_https": "https://pbs.twimg.com/media/GfZaVImWkAAwyp3.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1582, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 894, "resize": "fit"}, "small": {"h": 680, "w": 506, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1582, "width": 1178, "focus_rects": [{"x": 0, "y": 342, "w": 1178, "h": 660}, {"x": 0, "y": 83, "w": 1178, "h": 1178}, {"x": 0, "y": 1, "w": 1178, "h": 1343}, {"x": 198, "y": 0, "w": 791, "h": 1582}, {"x": 0, "y": 0, "w": 1178, "h": 1582}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014351118336"}}}, {"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014376263680", "indices": [277, 300], "media_key": "3_1870782014376263680", "media_url_https": "https://pbs.twimg.com/media/GfZaVIsWQAAJtBN.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 148, "y": 404, "h": 197, "w": 197}]}, "medium": {"faces": [{"x": 113, "y": 309, "h": 150, "w": 150}]}, "small": {"faces": [{"x": 64, "y": 175, "h": 85, "w": 85}]}, "orig": {"faces": [{"x": 148, "y": 404, "h": 197, "w": 197}]}}, "sizes": {"large": {"h": 1569, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 901, "resize": "fit"}, "small": {"h": 680, "w": 511, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1569, "width": 1178, "focus_rects": [{"x": 0, "y": 0, "w": 1178, "h": 660}, {"x": 0, "y": 0, "w": 1178, "h": 1178}, {"x": 0, "y": 0, "w": 1178, "h": 1343}, {"x": 0, "y": 0, "w": 785, "h": 1569}, {"x": 0, "y": 0, "w": 1178, "h": 1569}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014376263680"}}}, {"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014346899456", "indices": [277, 300], "media_key": "3_1870782014346899456", "media_url_https": "https://pbs.twimg.com/media/GfZaVIlWMAAedi5.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1566, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 903, "resize": "fit"}, "small": {"h": 680, "w": 512, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1566, "width": 1178, "focus_rects": [{"x": 0, "y": 0, "w": 1178, "h": 660}, {"x": 0, "y": 0, "w": 1178, "h": 1178}, {"x": 0, "y": 0, "w": 1178, "h": 1343}, {"x": 0, "y": 0, "w": 783, "h": 1566}, {"x": 0, "y": 0, "w": 1178, "h": 1566}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014346899456"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014376251392", "indices": [277, 300], "media_key": "3_1870782014376251392", "media_url_https": "https://pbs.twimg.com/media/GfZaVIsWEAACsfV.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1575, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 898, "resize": "fit"}, "small": {"h": 680, "w": 509, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1575, "width": 1178, "focus_rects": [{"x": 0, "y": 811, "w": 1178, "h": 660}, {"x": 0, "y": 397, "w": 1178, "h": 1178}, {"x": 0, "y": 232, "w": 1178, "h": 1343}, {"x": 0, "y": 0, "w": 788, "h": 1575}, {"x": 0, "y": 0, "w": 1178, "h": 1575}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014376251392"}}}, {"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014351118336", "indices": [277, 300], "media_key": "3_1870782014351118336", "media_url_https": "https://pbs.twimg.com/media/GfZaVImWkAAwyp3.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1582, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 894, "resize": "fit"}, "small": {"h": 680, "w": 506, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1582, "width": 1178, "focus_rects": [{"x": 0, "y": 342, "w": 1178, "h": 660}, {"x": 0, "y": 83, "w": 1178, "h": 1178}, {"x": 0, "y": 1, "w": 1178, "h": 1343}, {"x": 198, "y": 0, "w": 791, "h": 1582}, {"x": 0, "y": 0, "w": 1178, "h": 1582}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014351118336"}}}, {"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014376263680", "indices": [277, 300], "media_key": "3_1870782014376263680", "media_url_https": "https://pbs.twimg.com/media/GfZaVIsWQAAJtBN.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 148, "y": 404, "h": 197, "w": 197}]}, "medium": {"faces": [{"x": 113, "y": 309, "h": 150, "w": 150}]}, "small": {"faces": [{"x": 64, "y": 175, "h": 85, "w": 85}]}, "orig": {"faces": [{"x": 148, "y": 404, "h": 197, "w": 197}]}}, "sizes": {"large": {"h": 1569, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 901, "resize": "fit"}, "small": {"h": 680, "w": 511, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1569, "width": 1178, "focus_rects": [{"x": 0, "y": 0, "w": 1178, "h": 660}, {"x": 0, "y": 0, "w": 1178, "h": 1178}, {"x": 0, "y": 0, "w": 1178, "h": 1343}, {"x": 0, "y": 0, "w": 785, "h": 1569}, {"x": 0, "y": 0, "w": 1178, "h": 1569}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014376263680"}}}, {"display_url": "pic.x.com/7a93fVH8VG", "expanded_url": "https://x.com/bountywriteups/status/1870782024203858222/photo/1", "id_str": "1870782014346899456", "indices": [277, 300], "media_key": "3_1870782014346899456", "media_url_https": "https://pbs.twimg.com/media/GfZaVIlWMAAedi5.jpg", "type": "photo", "url": "https://t.co/7a93fVH8VG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1566, "w": 1178, "resize": "fit"}, "medium": {"h": 1200, "w": 903, "resize": "fit"}, "small": {"h": 680, "w": 512, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1566, "width": 1178, "focus_rects": [{"x": 0, "y": 0, "w": 1178, "h": 660}, {"x": 0, "y": 0, "w": 1178, "h": 1178}, {"x": 0, "y": 0, "w": 1178, "h": 1343}, {"x": 0, "y": 0, "w": 783, "h": 1566}, {"x": 0, "y": 0, "w": 1178, "h": 1566}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1870782014346899456"}}}]}, "favorite_count": 1548, "favorited": false, "full_text": "Real Ethical Hacking in 43 Hours: Your Fast-Track to Cybersecurity Mastery\n\n📲 Key Topics Covered:\n\n * Ethical Hacking Foundations\n * Introduction to Ethical Hacking\n * Ethical Hacking Steps\n * Creating Your Ethical Hacking Lab\n * Operating System Fundamentals\n * Vulnerability https://t.co/7a93fVH8VG", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 9, "reply_count": 799, "retweet_count": 592, "retweeted": false, "user_id_str": "1689512391300751362", "id_str": "1870782024203858222"}, "twe_private_fields": {"created_at": 1734864166000, "updated_at": 1748554120680, "media_count": 4}}}]