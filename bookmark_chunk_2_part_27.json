[{"id": "1447807328993095681", "created_at": "2021-10-12 09:12:20 +03:00", "full_text": "These K Movie series are a Must Watch. Check them out. You won't be disappointed. 😊\n\n-Vagabond\n-Jumon<PERSON>\n-<PERSON>\n-<PERSON> in Borderland\n-K2\n-Squid Game\n-<PERSON>\n-The Crowned Clown\n-Six flying dragons\n-Kingdom, <PERSON><PERSON> of the North\n-Hot Stove League\n-The Rebel", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 275, "retweet_count": 93, "bookmark_count": 80, "quote_count": 2, "reply_count": 19, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1447807328993095681", "metadata": {"__typename": "Tweet", "rest_id": "1447807328993095681", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzg2NzcyNjM=", "rest_id": "178677263", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1807746784589914112/vqe0pxSE_normal.jpg"}, "core": {"created_at": "Sun Aug 15 11:27:46 +0000 2010", "name": "BRAVIN YURI", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Political Scientist|| Sociologist|| The Truth does not care about your emotions|| I am responsible for what I say, not what you understand", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtu.be/IAAFod2YIcU?si…", "expanded_url": "https://youtu.be/IAAFod2YIcU?si=8_Ptb-8tmAfpeHdJ", "url": "https://t.co/saqjMN9CN1", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 61697, "followers_count": 367954, "friends_count": 4693, "has_custom_timelines": true, "is_translator": false, "listed_count": 169, "media_count": 1944, "normal_followers_count": 367954, "pinned_tweet_ids_str": ["1462685494194720774"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/178677263/1617227647", "profile_interstitial_type": "", "statuses_count": 108575, "translator_type": "none", "url": "https://t.co/saqjMN9CN1", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Nairobi, Kenya"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": ""}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1447807328993095681"], "editable_until_msecs": "1634020940848", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 80, "bookmarked": true, "created_at": "Tue Oct 12 06:12:20 +0000 2021", "conversation_id_str": "1447807328993095681", "display_text_range": [0, 256], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 275, "favorited": false, "full_text": "These K Movie series are a Must Watch. Check them out. You won't be disappointed. 😊\n\n-Vagabond\n-Jumon<PERSON>\n-<PERSON>\n-<PERSON> in Borderland\n-K2\n-Squid Game\n-<PERSON>\n-The Crowned Clown\n-Six flying dragons\n-Kingdom, <PERSON><PERSON> of the North\n-Hot Stove League\n-The Rebel", "is_quote_status": false, "lang": "en", "quote_count": 2, "reply_count": 19, "retweet_count": 93, "retweeted": false, "user_id_str": "178677263", "id_str": "1447807328993095681"}, "twe_private_fields": {"created_at": 1634019140000, "updated_at": 1748554391966, "media_count": 0}}}, {"id": "1447980938831351810", "created_at": "2021-10-12 20:42:12 +03:00", "full_text": "What's a TV series that everyone should give a try? 💭", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3203, "retweet_count": 288, "bookmark_count": 382, "quote_count": 587, "reply_count": 2080, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1447980938831351810", "metadata": {"__typename": "Tweet", "rest_id": "1447980938831351810", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NTAyMzQyMw==", "rest_id": "95023423", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1603579996064227328/TAcR_zuP_normal.jpg"}, "core": {"created_at": "Sun Dec 06 16:07:01 +0000 2009", "name": "UberFacts", "screen_name": "UberFacts"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The most unimportant things you’ll never need to know 💥", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "instagram.com/uberfacts/", "expanded_url": "https://www.instagram.com/uberfacts/", "url": "https://t.co/uVFxvFrYJP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1162, "followers_count": 12907351, "friends_count": 1, "has_custom_timelines": true, "is_translator": false, "listed_count": 14680, "media_count": 27707, "normal_followers_count": 12907351, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/95023423/1671158239", "profile_interstitial_type": "", "statuses_count": 234950, "translator_type": "regular", "url": "https://t.co/uVFxvFrYJP", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "🌎"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1460999005266059264", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1447980938831351810"], "editable_until_msecs": "1634062332659", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 382, "bookmarked": true, "created_at": "<PERSON><PERSON> Oct 12 17:42:12 +0000 2021", "conversation_id_str": "1447980938831351810", "display_text_range": [0, 53], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 3203, "favorited": false, "full_text": "What's a TV series that everyone should give a try? 💭", "is_quote_status": false, "lang": "en", "quote_count": 587, "reply_count": 2080, "retweet_count": 288, "retweeted": false, "user_id_str": "95023423", "id_str": "1447980938831351810"}, "twe_private_fields": {"created_at": 1634060532000, "updated_at": 1748554391966, "media_count": 0}}}]