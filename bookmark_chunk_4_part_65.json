[{"id": "1799820335891857576", "created_at": "2024-06-09 18:06:21 +03:00", "full_text": "Really interesting repo \n\nA dataset consists of 15,140 ChatGPT prompts from Reddit, Discord, websites, and open-source datasets (including 1,405 jailbreak prompts). https://t.co/iPLJFj0x3t", "media": [{"type": "photo", "url": "https://t.co/iPLJFj0x3t", "thumbnail": "https://pbs.twimg.com/media/GPo_CzvXUAEwl9L?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GPo_CzvXUAEwl9L?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1426, "retweet_count": 213, "bookmark_count": 2700, "quote_count": 6, "reply_count": 13, "views_count": 266512, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1799820335891857576", "metadata": {"__typename": "Tweet", "rest_id": "1799820335891857576", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNTg4MzQ1NDA4", "rest_id": "**********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1816185267037859840/Fd18CH0v_normal.jpg"}, "core": {"created_at": "Wed Jun 25 22:38:54 +0000 2014", "name": "<PERSON><PERSON><PERSON>", "screen_name": "rohanpaul_ai"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "💼 Engineer.\n\n📚 I write daily on actionable AI developments.\n\n🗞️ Subscribe and instantly get a 1300+page Python book → https://t.co/lK1BBqjk0Z", "entities": {"description": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "https://rohan-paul.com", "url": "https://t.co/lK1BBqjk0Z", "indices": [118, 141]}]}, "url": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "http://www.rohan-paul.com", "url": "https://t.co/2NKnK0xg7T", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 32936, "followers_count": 64302, "friends_count": 811, "has_custom_timelines": true, "is_translator": false, "listed_count": 1228, "media_count": 15320, "normal_followers_count": 64302, "pinned_tweet_ids_str": ["1898099124165394754"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/**********/**********", "profile_interstitial_type": "", "statuses_count": 38933, "translator_type": "none", "url": "https://t.co/2NKnK0xg7T", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Ex Inv Banker (Deutsche)"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1826415623603180000", "professional_type": "Creator", "category": [{"id": 958, "name": "Entrepreneur", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1799820335891857576"], "editable_until_msecs": "*************", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "266512", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2700, "bookmarked": true, "created_at": "Sun Jun 09 15:06:21 +0000 2024", "conversation_id_str": "1799820335891857576", "display_text_range": [0, 164], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/iPLJFj0x3t", "expanded_url": "https://x.com/rohanpaul_ai/status/1799820335891857576/photo/1", "id_str": "1799820318569091073", "indices": [165, 188], "media_key": "3_1799820318569091073", "media_url_https": "https://pbs.twimg.com/media/GPo_CzvXUAEwl9L.jpg", "type": "photo", "url": "https://t.co/iPLJFj0x3t", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 575, "y": 650, "h": 45, "w": 45}, {"x": 139, "y": 465, "h": 48, "w": 48}]}, "medium": {"faces": [{"x": 575, "y": 650, "h": 45, "w": 45}, {"x": 139, "y": 465, "h": 48, "w": 48}]}, "small": {"faces": [{"x": 425, "y": 481, "h": 33, "w": 33}, {"x": 102, "y": 344, "h": 35, "w": 35}]}, "orig": {"faces": [{"x": 575, "y": 650, "h": 45, "w": 45}, {"x": 139, "y": 465, "h": 48, "w": 48}]}}, "sizes": {"large": {"h": 798, "w": 918, "resize": "fit"}, "medium": {"h": 798, "w": 918, "resize": "fit"}, "small": {"h": 591, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 798, "width": 918, "focus_rects": [{"x": 0, "y": 0, "w": 918, "h": 514}, {"x": 0, "y": 0, "w": 798, "h": 798}, {"x": 0, "y": 0, "w": 700, "h": 798}, {"x": 0, "y": 0, "w": 399, "h": 798}, {"x": 0, "y": 0, "w": 918, "h": 798}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1799820318569091073"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/iPLJFj0x3t", "expanded_url": "https://x.com/rohanpaul_ai/status/1799820335891857576/photo/1", "id_str": "1799820318569091073", "indices": [165, 188], "media_key": "3_1799820318569091073", "media_url_https": "https://pbs.twimg.com/media/GPo_CzvXUAEwl9L.jpg", "type": "photo", "url": "https://t.co/iPLJFj0x3t", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 575, "y": 650, "h": 45, "w": 45}, {"x": 139, "y": 465, "h": 48, "w": 48}]}, "medium": {"faces": [{"x": 575, "y": 650, "h": 45, "w": 45}, {"x": 139, "y": 465, "h": 48, "w": 48}]}, "small": {"faces": [{"x": 425, "y": 481, "h": 33, "w": 33}, {"x": 102, "y": 344, "h": 35, "w": 35}]}, "orig": {"faces": [{"x": 575, "y": 650, "h": 45, "w": 45}, {"x": 139, "y": 465, "h": 48, "w": 48}]}}, "sizes": {"large": {"h": 798, "w": 918, "resize": "fit"}, "medium": {"h": 798, "w": 918, "resize": "fit"}, "small": {"h": 591, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 798, "width": 918, "focus_rects": [{"x": 0, "y": 0, "w": 918, "h": 514}, {"x": 0, "y": 0, "w": 798, "h": 798}, {"x": 0, "y": 0, "w": 700, "h": 798}, {"x": 0, "y": 0, "w": 399, "h": 798}, {"x": 0, "y": 0, "w": 918, "h": 798}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1799820318569091073"}}}]}, "favorite_count": 1426, "favorited": false, "full_text": "Really interesting repo \n\nA dataset consists of 15,140 ChatGPT prompts from Reddit, Discord, websites, and open-source datasets (including 1,405 jailbreak prompts). https://t.co/iPLJFj0x3t", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 13, "retweet_count": 213, "retweeted": false, "user_id_str": "**********", "id_str": "1799820335891857576"}, "twe_private_fields": {"created_at": 1717945581000, "updated_at": 1748554161183, "media_count": 1}}}, {"id": "1799820367130923137", "created_at": "2024-06-09 18:06:29 +03:00", "full_text": "https://t.co/hHfG4bgwVI", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1799820335891857576", "retweeted_status": null, "quoted_status": null, "favorite_count": 125, "retweet_count": 20, "bookmark_count": 273, "quote_count": 0, "reply_count": 0, "views_count": 10554, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1799820367130923137", "metadata": {"__typename": "Tweet", "rest_id": "1799820367130923137", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNTg4MzQ1NDA4", "rest_id": "**********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1816185267037859840/Fd18CH0v_normal.jpg"}, "core": {"created_at": "Wed Jun 25 22:38:54 +0000 2014", "name": "<PERSON><PERSON><PERSON>", "screen_name": "rohanpaul_ai"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "💼 Engineer.\n\n📚 I write daily on actionable AI developments.\n\n🗞️ Subscribe and instantly get a 1300+page Python book → https://t.co/lK1BBqjk0Z", "entities": {"description": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "https://rohan-paul.com", "url": "https://t.co/lK1BBqjk0Z", "indices": [118, 141]}]}, "url": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "http://www.rohan-paul.com", "url": "https://t.co/2NKnK0xg7T", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 32936, "followers_count": 64302, "friends_count": 811, "has_custom_timelines": true, "is_translator": false, "listed_count": 1228, "media_count": 15320, "normal_followers_count": 64302, "pinned_tweet_ids_str": ["1898099124165394754"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/**********/**********", "profile_interstitial_type": "", "statuses_count": 38933, "translator_type": "none", "url": "https://t.co/2NKnK0xg7T", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Ex Inv Banker (Deutsche)"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1826415623603180000", "professional_type": "Creator", "category": [{"id": 958, "name": "Entrepreneur", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/hHfG4bgwVI", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 200, "width": 400, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "[CCS'24] A dataset consists of 15,140 ChatGPT prompts from Reddit, Discord, websites, and open-source datasets (including 1,405 jailbreak prompts). - verazuo/jailbreak_llms", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 600, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=600x600"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 72, "width": 144, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "[CCS'24] A dataset consists of 15,140 ChatGPT prompts from Reddit, Discord, websites, and open-source datasets (including 1,405 jailbreak prompts). - verazuo/jailbreak_llms", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "[CCS'24] A dataset consists of 15,140 ChatGPT prompts from Reddit, Discord, websites, and open-source datasets (including 1,405 jailbreak prompts). - verazuo/jailbreak_llms", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 91.55}, {"rgb": {"blue": 117, "green": 111, "red": 107}, "percentage": 2.32}, {"rgb": {"blue": 12, "green": 91, "red": 218}, "percentage": 1.95}, {"rgb": {"blue": 112, "green": 158, "red": 233}, "percentage": 1.56}, {"rgb": {"blue": 118, "green": 159, "red": 188}, "percentage": 1.11}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "GitHub - verazuo/jailbreak_llms: [CCS'24] A dataset consists of 15,140 ChatGPT prompts from Reddit,...", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 91.55}, {"rgb": {"blue": 117, "green": 111, "red": 107}, "percentage": 2.32}, {"rgb": {"blue": 12, "green": 91, "red": 218}, "percentage": 1.95}, {"rgb": {"blue": 112, "green": 158, "red": 233}, "percentage": 1.56}, {"rgb": {"blue": 118, "green": 159, "red": 188}, "percentage": 1.11}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 91.55}, {"rgb": {"blue": 117, "green": 111, "red": 107}, "percentage": 2.32}, {"rgb": {"blue": 12, "green": 91, "red": 218}, "percentage": 1.95}, {"rgb": {"blue": 112, "green": 158, "red": 233}, "percentage": 1.56}, {"rgb": {"blue": 118, "green": 159, "red": 188}, "percentage": 1.11}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/hHfG4bgwVI", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927730913837989888/QnqQOWf3?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/hHfG4bgwVI", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620091, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17801, "media_count": 2646, "normal_followers_count": 2620091, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1799820367130923137"], "editable_until_msecs": "1717949189000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "10554", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 273, "bookmarked": true, "created_at": "Sun Jun 09 15:06:29 +0000 2024", "conversation_id_str": "1799820335891857576", "display_text_range": [0, 23], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/verazuo/jailbr…", "expanded_url": "https://github.com/verazuo/jailbreak_llms", "url": "https://t.co/hHfG4bgwVI", "indices": [0, 23]}], "user_mentions": []}, "favorite_count": 125, "favorited": false, "full_text": "https://t.co/hHfG4bgwVI", "in_reply_to_screen_name": "rohanpaul_ai", "in_reply_to_status_id_str": "1799820335891857576", "in_reply_to_user_id_str": "**********", "is_quote_status": false, "lang": "zxx", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 0, "retweet_count": 20, "retweeted": false, "user_id_str": "**********", "id_str": "1799820367130923137"}, "twe_private_fields": {"created_at": 1717945589000, "updated_at": 1748554161183, "media_count": 0}}}]