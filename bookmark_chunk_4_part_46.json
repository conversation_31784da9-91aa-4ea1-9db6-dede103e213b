[{"id": "1742734709501632841", "created_at": "2024-01-04 05:28:27 +03:00", "full_text": "A fix has been found for this problem! \n\nIf you want stream apps from your Samsung device running Android 14 to your Windows PC without having to constantly regrant Link to Windows permission to record the screen, you just need to do a few simple steps.\n\n1) Download the \"App Ops - Permission Manager\" app from Google Play.\n\n2) Go through its setup process, which involves downloading a secondary app called <PERSON>zuku that acts as a helper to enable the App Ops app to access certain normally inaccessible APIs (in this case, AppOpsManager) using the shell UID.\n\n3) Find \"Link to Windows\" in the list and scroll down until you see \"Project media\". Change it from \"Ignore\" to \"Allow\".\n\n4) That's it! You should no longer see a MediaProjection consent dialog when you try to stream an app from your phone. \n\nUnder the hood, what you're doing is changing the OP_PROJECT_MEDIA appop from \"ignore\" to \"allow\".\n\nBefore:\n\nAfter:\n\n(Sidenote: I tried changing the appop using the shell interface, but it didn't persist after a reboot. Changing the appop via the App Op app did. I don't know why, but it may be because the app is using the API directly instead of through this CLI.)\n\nThanks to @Abdelrahman5T for the tip! My complaints have led to oddly productive results today, lol.", "media": [{"type": "photo", "url": "https://t.co/cEeIP532tj", "thumbnail": "https://pbs.twimg.com/media/GC9vbN8XoAAXrLc?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GC9vbN8XoAAXrLc?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/cEeIP532tj", "thumbnail": "https://pbs.twimg.com/media/GC9vnnaWIAAhTBy?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GC9vnnaWIAAhTBy?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/cEeIP532tj", "thumbnail": "https://pbs.twimg.com/media/GC9vwOOWsAMm3Xp?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GC9vwOOWsAMm3Xp?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": "1742626814927778057", "favorite_count": 186, "retweet_count": 11, "bookmark_count": 55, "quote_count": 2, "reply_count": 7, "views_count": 29470, "favorited": true, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1742734709501632841", "metadata": {"__typename": "Tweet", "rest_id": "1742734709501632841", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MDgzOTI3NTE2Mjg4NzM3MzY=", "rest_id": "808392751628873736", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1795585926304784384/CBaEtw09_normal.jpg"}, "core": {"created_at": "Mon Dec 12 19:27:06 +0000 2016", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The best source for Android OS news. \n\nEditor-at-large @AndroidAuth, Co-host @AndroidFaithful.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expanded_url": "https://linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://t.co/1I4ZpxSLyG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16539, "followers_count": 68160, "friends_count": 523, "has_custom_timelines": true, "is_translator": false, "listed_count": 464, "media_count": 5574, "normal_followers_count": 68160, "pinned_tweet_ids_str": ["1840914465404923985"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/808392751628873736/1716936020", "profile_interstitial_type": "", "statuses_count": 27411, "translator_type": "none", "url": "https://t.co/1I4ZpxSLyG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1469919091762864128", "professional_type": "Creator", "category": [{"id": 955, "name": "Journalist", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "patreon_handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1742734709501632841"], "editable_until_msecs": "1704338907000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "29470", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3NDI3MzQ3MDkzNDIyNzM1MzY=", "text": "A fix has been found for this problem! \n\nIf you want stream apps from your Samsung device running Android 14 to your Windows PC without having to constantly regrant Link to Windows permission to record the screen, you just need to do a few simple steps.\n\n1) Download the \"App Ops - Permission Manager\" app from Google Play.\n\n2) Go through its setup process, which involves downloading a secondary app called <PERSON>zuku that acts as a helper to enable the App Ops app to access certain normally inaccessible APIs (in this case, AppOpsManager) using the shell UID.\n\n3) Find \"Link to Windows\" in the list and scroll down until you see \"Project media\". Change it from \"Ignore\" to \"Allow\".\n\n4) That's it! You should no longer see a MediaProjection consent dialog when you try to stream an app from your phone. \n\nUnder the hood, what you're doing is changing the OP_PROJECT_MEDIA appop from \"ignore\" to \"allow\".\n\nBefore:\n\nAfter:\n\n(Sidenote: I tried changing the appop using the shell interface, but it didn't persist after a reboot. Changing the appop via the App Op app did. I don't know why, but it may be because the app is using the API directly instead of through this CLI.)\n\nThanks to @Abdelrahman5T for the tip! My complaints have led to oddly productive results today, lol.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "4785939681", "name": "<PERSON>", "screen_name": "Abdelrahman5T", "indices": [1182, 1196]}]}, "richtext": {"richtext_tags": [{"from_index": 128, "to_index": 135, "richtext_types": ["Italic"]}]}, "media": {"inline_media": [{"media_id": "1742734094205689856", "index": 682}, {"media_id": "1742734307200737280", "index": 913}, {"media_id": "1742734455058378755", "index": 921}]}}}}, "grok_analysis_button": true, "quoted_status_result": {"result": {"__typename": "Tweet", "rest_id": "1742626814927778057", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MDgzOTI3NTE2Mjg4NzM3MzY=", "rest_id": "808392751628873736", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1795585926304784384/CBaEtw09_normal.jpg"}, "core": {"created_at": "Mon Dec 12 19:27:06 +0000 2016", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The best source for Android OS news. \n\nEditor-at-large @AndroidAuth, Co-host @AndroidFaithful.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expanded_url": "https://linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://t.co/1I4ZpxSLyG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16539, "followers_count": 68160, "friends_count": 523, "has_custom_timelines": true, "is_translator": false, "listed_count": 464, "media_count": 5574, "normal_followers_count": 68160, "pinned_tweet_ids_str": ["1840914465404923985"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/808392751628873736/1716936020", "profile_interstitial_type": "", "statuses_count": 27411, "translator_type": "none", "url": "https://t.co/1I4ZpxSLyG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1469919091762864128", "professional_type": "Creator", "category": [{"id": 955, "name": "Journalist", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "patreon_handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1742626814927778057"], "editable_until_msecs": "1704313183000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "54495", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3NDI2MjY4MTQ3NzI2MDA4MzI=", "text": "Android 14 made streaming apps to your Windows PC slightly more annoying\n\nIf you use Link to Windows on an Android smartphone that supports streaming apps, you may have noticed that, after the Android 14 update, you have to tap \"start now\" every single time you want to mirror your screen or an app. In previous releases, you only had to do this generally once per boot. This means that you now have to reach over to your phone every time you want to launch a new app.\n\nYou don't have to do this if you're using Chrome OS's app streaming feature on Android 14, so what gives? Turns out that Android 14 changed the way that the MediaProjection API behaves. MediaProjection is the API that apps use to record the screen. To avoid having to ask the user for permission to capture the screen every time, many screen recorder apps and apps like Link to Windows would reuse the same intent returned by the API. Doing this now in Android 14 throws a SecurityException. \n\nThis is an intentional change by Google to force apps to ask the user to give consent before each capture session, but it definitely makes app streaming through Link to Windows more annoying until Microsoft finds a workaround. App streaming to Chromebooks isn't affected because Cross-Device Services uses the new privileged Companion App Streaming APIs in Android 13 rather than MediaProjection.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 240, "to_index": 257, "richtext_types": ["Italic"]}]}, "media": {"inline_media": [{"media_id": "1742622630086094848", "index": 74}, {"media_id": "1742626670450712576", "index": 964}]}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 37, "bookmarked": false, "created_at": "Wed Jan 03 19:19:43 +0000 2024", "conversation_id_str": "1742626814927778057", "display_text_range": [0, 276], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/Xp4DRzbWSS", "expanded_url": "https://x.com/MishaalRahman/status/1742626814927778057/photo/1", "id_str": "1742622630086094848", "indices": [277, 300], "media_key": "3_1742622630086094848", "media_url_https": "https://pbs.twimg.com/media/GC8KDJvWsAAtdvG.jpg", "type": "photo", "url": "https://t.co/Xp4DRzbWSS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 1222, "y": 453, "h": 71, "w": 71}]}, "medium": {"faces": [{"x": 763, "y": 283, "h": 44, "w": 44}]}, "small": {"faces": [{"x": 432, "y": 160, "h": 25, "w": 25}]}, "orig": {"faces": [{"x": 1222, "y": 453, "h": 71, "w": 71}]}}, "sizes": {"large": {"h": 1079, "w": 1920, "resize": "fit"}, "medium": {"h": 674, "w": 1200, "resize": "fit"}, "small": {"h": 382, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1079, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1920, "h": 1075}, {"x": 841, "y": 0, "w": 1079, "h": 1079}, {"x": 974, "y": 0, "w": 946, "h": 1079}, {"x": 1312, "y": 0, "w": 540, "h": 1079}, {"x": 0, "y": 0, "w": 1920, "h": 1079}]}, "media_results": {"result": {"media_key": "3_1742622630086094848"}}}, {"display_url": "pic.x.com/Xp4DRzbWSS", "expanded_url": "https://x.com/MishaalRahman/status/1742626814927778057/photo/1", "id_str": "1742626670450712576", "indices": [277, 300], "media_key": "3_1742626670450712576", "media_url_https": "https://pbs.twimg.com/media/GC8NuVRWIAAwDHT.jpg", "type": "photo", "url": "https://t.co/Xp4DRzbWSS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1920, "w": 1638, "resize": "fit"}, "medium": {"h": 1200, "w": 1024, "resize": "fit"}, "small": {"h": 680, "w": 580, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1920, "width": 1638, "focus_rects": [{"x": 0, "y": 550, "w": 1638, "h": 917}, {"x": 0, "y": 189, "w": 1638, "h": 1638}, {"x": 0, "y": 53, "w": 1638, "h": 1867}, {"x": 339, "y": 0, "w": 960, "h": 1920}, {"x": 0, "y": 0, "w": 1638, "h": 1920}]}, "media_results": {"result": {"media_key": "3_1742626670450712576"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/Xp4DRzbWSS", "expanded_url": "https://x.com/MishaalRahman/status/1742626814927778057/photo/1", "id_str": "1742622630086094848", "indices": [277, 300], "media_key": "3_1742622630086094848", "media_url_https": "https://pbs.twimg.com/media/GC8KDJvWsAAtdvG.jpg", "type": "photo", "url": "https://t.co/Xp4DRzbWSS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 1222, "y": 453, "h": 71, "w": 71}]}, "medium": {"faces": [{"x": 763, "y": 283, "h": 44, "w": 44}]}, "small": {"faces": [{"x": 432, "y": 160, "h": 25, "w": 25}]}, "orig": {"faces": [{"x": 1222, "y": 453, "h": 71, "w": 71}]}}, "sizes": {"large": {"h": 1079, "w": 1920, "resize": "fit"}, "medium": {"h": 674, "w": 1200, "resize": "fit"}, "small": {"h": 382, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1079, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1920, "h": 1075}, {"x": 841, "y": 0, "w": 1079, "h": 1079}, {"x": 974, "y": 0, "w": 946, "h": 1079}, {"x": 1312, "y": 0, "w": 540, "h": 1079}, {"x": 0, "y": 0, "w": 1920, "h": 1079}]}, "media_results": {"result": {"media_key": "3_1742622630086094848"}}}, {"display_url": "pic.x.com/Xp4DRzbWSS", "expanded_url": "https://x.com/MishaalRahman/status/1742626814927778057/photo/1", "id_str": "1742626670450712576", "indices": [277, 300], "media_key": "3_1742626670450712576", "media_url_https": "https://pbs.twimg.com/media/GC8NuVRWIAAwDHT.jpg", "type": "photo", "url": "https://t.co/Xp4DRzbWSS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1920, "w": 1638, "resize": "fit"}, "medium": {"h": 1200, "w": 1024, "resize": "fit"}, "small": {"h": 680, "w": 580, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1920, "width": 1638, "focus_rects": [{"x": 0, "y": 550, "w": 1638, "h": 917}, {"x": 0, "y": 189, "w": 1638, "h": 1638}, {"x": 0, "y": 53, "w": 1638, "h": 1867}, {"x": 339, "y": 0, "w": 960, "h": 1920}, {"x": 0, "y": 0, "w": 1638, "h": 1920}]}, "media_results": {"result": {"media_key": "3_1742626670450712576"}}}]}, "favorite_count": 331, "favorited": false, "full_text": "Android 14 made streaming apps to your Windows PC slightly more annoying\n\nIf you use Link to Windows on an Android smartphone that supports streaming apps, you may have noticed that, after the Android 14 update, you have to tap \"start now\" every single time you want to mirror https://t.co/Xp4DRzbWSS", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 11, "retweet_count": 19, "retweeted": false, "user_id_str": "808392751628873736", "id_str": "1742626814927778057"}}}, "legacy": {"bookmark_count": 55, "bookmarked": true, "created_at": "Thu Jan 04 02:28:27 +0000 2024", "conversation_id_str": "1742734709501632841", "display_text_range": [0, 279], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/cEeIP532tj", "expanded_url": "https://x.com/MishaalRahman/status/1742734709501632841/photo/1", "id_str": "1742734094205689856", "indices": [280, 303], "media_key": "3_1742734094205689856", "media_url_https": "https://pbs.twimg.com/media/GC9vbN8XoAAXrLc.jpg", "type": "photo", "url": "https://t.co/cEeIP532tj", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1686, "w": 1980, "resize": "fit"}, "medium": {"h": 1022, "w": 1200, "resize": "fit"}, "small": {"h": 579, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1686, "width": 1980, "focus_rects": [{"x": 0, "y": 0, "w": 1980, "h": 1109}, {"x": 147, "y": 0, "w": 1686, "h": 1686}, {"x": 251, "y": 0, "w": 1479, "h": 1686}, {"x": 569, "y": 0, "w": 843, "h": 1686}, {"x": 0, "y": 0, "w": 1980, "h": 1686}]}, "media_results": {"result": {"media_key": "3_1742734094205689856"}}}, {"display_url": "pic.x.com/cEeIP532tj", "expanded_url": "https://x.com/MishaalRahman/status/1742734709501632841/photo/1", "id_str": "1742734307200737280", "indices": [280, 303], "media_key": "3_1742734307200737280", "media_url_https": "https://pbs.twimg.com/media/GC9vnnaWIAAhTBy.jpg", "type": "photo", "url": "https://t.co/cEeIP532tj", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 412, "w": 1396, "resize": "fit"}, "medium": {"h": 354, "w": 1200, "resize": "fit"}, "small": {"h": 201, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 412, "width": 1396, "focus_rects": [{"x": 330, "y": 0, "w": 736, "h": 412}, {"x": 492, "y": 0, "w": 412, "h": 412}, {"x": 518, "y": 0, "w": 361, "h": 412}, {"x": 595, "y": 0, "w": 206, "h": 412}, {"x": 0, "y": 0, "w": 1396, "h": 412}]}, "media_results": {"result": {"media_key": "3_1742734307200737280"}}}, {"display_url": "pic.x.com/cEeIP532tj", "expanded_url": "https://x.com/MishaalRahman/status/1742734709501632841/photo/1", "id_str": "1742734455058378755", "indices": [280, 303], "media_key": "3_1742734455058378755", "media_url_https": "https://pbs.twimg.com/media/GC9vwOOWsAMm3Xp.jpg", "type": "photo", "url": "https://t.co/cEeIP532tj", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 412, "w": 1550, "resize": "fit"}, "medium": {"h": 319, "w": 1200, "resize": "fit"}, "small": {"h": 181, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 412, "width": 1550, "focus_rects": [{"x": 407, "y": 0, "w": 736, "h": 412}, {"x": 569, "y": 0, "w": 412, "h": 412}, {"x": 595, "y": 0, "w": 361, "h": 412}, {"x": 672, "y": 0, "w": 206, "h": 412}, {"x": 0, "y": 0, "w": 1550, "h": 412}]}, "media_results": {"result": {"media_key": "3_1742734455058378755"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/cEeIP532tj", "expanded_url": "https://x.com/MishaalRahman/status/1742734709501632841/photo/1", "id_str": "1742734094205689856", "indices": [280, 303], "media_key": "3_1742734094205689856", "media_url_https": "https://pbs.twimg.com/media/GC9vbN8XoAAXrLc.jpg", "type": "photo", "url": "https://t.co/cEeIP532tj", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1686, "w": 1980, "resize": "fit"}, "medium": {"h": 1022, "w": 1200, "resize": "fit"}, "small": {"h": 579, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1686, "width": 1980, "focus_rects": [{"x": 0, "y": 0, "w": 1980, "h": 1109}, {"x": 147, "y": 0, "w": 1686, "h": 1686}, {"x": 251, "y": 0, "w": 1479, "h": 1686}, {"x": 569, "y": 0, "w": 843, "h": 1686}, {"x": 0, "y": 0, "w": 1980, "h": 1686}]}, "media_results": {"result": {"media_key": "3_1742734094205689856"}}}, {"display_url": "pic.x.com/cEeIP532tj", "expanded_url": "https://x.com/MishaalRahman/status/1742734709501632841/photo/1", "id_str": "1742734307200737280", "indices": [280, 303], "media_key": "3_1742734307200737280", "media_url_https": "https://pbs.twimg.com/media/GC9vnnaWIAAhTBy.jpg", "type": "photo", "url": "https://t.co/cEeIP532tj", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 412, "w": 1396, "resize": "fit"}, "medium": {"h": 354, "w": 1200, "resize": "fit"}, "small": {"h": 201, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 412, "width": 1396, "focus_rects": [{"x": 330, "y": 0, "w": 736, "h": 412}, {"x": 492, "y": 0, "w": 412, "h": 412}, {"x": 518, "y": 0, "w": 361, "h": 412}, {"x": 595, "y": 0, "w": 206, "h": 412}, {"x": 0, "y": 0, "w": 1396, "h": 412}]}, "media_results": {"result": {"media_key": "3_1742734307200737280"}}}, {"display_url": "pic.x.com/cEeIP532tj", "expanded_url": "https://x.com/MishaalRahman/status/1742734709501632841/photo/1", "id_str": "1742734455058378755", "indices": [280, 303], "media_key": "3_1742734455058378755", "media_url_https": "https://pbs.twimg.com/media/GC9vwOOWsAMm3Xp.jpg", "type": "photo", "url": "https://t.co/cEeIP532tj", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 412, "w": 1550, "resize": "fit"}, "medium": {"h": 319, "w": 1200, "resize": "fit"}, "small": {"h": 181, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 412, "width": 1550, "focus_rects": [{"x": 407, "y": 0, "w": 736, "h": 412}, {"x": 569, "y": 0, "w": 412, "h": 412}, {"x": 595, "y": 0, "w": 361, "h": 412}, {"x": 672, "y": 0, "w": 206, "h": 412}, {"x": 0, "y": 0, "w": 1550, "h": 412}]}, "media_results": {"result": {"media_key": "3_1742734455058378755"}}}]}, "favorite_count": 186, "favorited": true, "full_text": "A fix has been found for this problem! \n\nIf you want stream apps from your Samsung device running Android 14 to your Windows PC without having to constantly regrant Link to Windows permission to record the screen, you just need to do a few simple steps.\n\n1) Download the \"App Ops https://t.co/cEeIP532tj", "is_quote_status": true, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "quoted_status_id_str": "1742626814927778057", "quoted_status_permalink": {"url": "https://t.co/ckP40MuztD", "expanded": "https://twitter.com/MishaalRahman/status/1742626814927778057", "display": "x.com/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/…"}, "reply_count": 7, "retweet_count": 11, "retweeted": false, "user_id_str": "808392751628873736", "id_str": "1742734709501632841"}, "twe_private_fields": {"created_at": 1704335307000, "updated_at": 1748554178748, "media_count": 3}}}, {"id": "1743678390027121105", "created_at": "2024-01-06 19:58:18 +03:00", "full_text": "Young Kenyans who wish to do animal and plant agriculture as a source of income, here are possible resources for you, and I can guarantee the officers in these facilities will be of help:\n1. For fish rearing, processing, and value addition, including selecting quality feeds and fingerings, talk to Kenya Marine and Fisheries Research Institute @KmfriResearch https://t.co/WQFTxRdiqB. They have stations in Sagana, Naivasha, Mombasa and other stations serving the country.\n2. For goat and bovine semen, that is well acclimatized for Kenya. Talk to @KAGRC_KE https://t.co/PKRwbvbrqN. By the way, not all import semen is for breeds that make sense in Kenya. There is no point in importing a pure Holstein to a tropical environment that is low on feed resources and high disease incidence (story for another day).\n3. For rabbits, all breeds: Hybrid rabbits include New Zealand White, California White, and Flemish Giant and their crosses. talk to the National Rabbits Breeding Centre Ngong FTC and Veterinary Farm are under the Livestock Department, and you can call them at +254  0733782096.\n4. For beef and dairy cattle, indigenous and improved poultry, beekeeping and new tech talk to KALRO @kalromkulima  https://t.co/VaKvcUNCsp. Do not fear them; go to their offices and stations and request help, and you can be sure they will help you. \n5. For fruit trees, if you are in Mt Kenya Region, go to @DiscoverJKUAT; if you are in counties surrounding Machakos (ASALs), visit https://t.co/55WKAHdCVd Katumani. KALRO also has other Rift Valley and Western/Nyanza centers.\n6. When I visited the Perkerra Irrigation Scheme at Marigat, they had some highland rice that didn't need a lot of water and some nice fruiting tree seedlings, including pawpaws, peaches, etc. I am not sure whether they still have that rice variety, but you can reach out to them @Irrigation_Auth \n7. For youngsters who want to do training for diplomas and short-term certs in animal health and affiliated courses, dairy tech visit @AhitiKabete and Dairy Training <NAME_EMAIL> respectively. \n8. For biological plant pest management, visit @icipe. They have some very nice pest management methods for crops.   \nSocial media has a lot of young men and women doing wonderful stuff, talk to them when you can and learn from each other.\nFor animal nutrition purposes, talk to graduate students  from our universities @egertonunikenya @DiscoverJKUAT @uonbi @KenyattaUni @EmbuUniversity among others (please be generous in your renumeration for services rendered and give them feedback on what to improve on).      \n@shobanes @CalebKaruga @TuJadili @wmnjoya @akams_ @LegalEzra", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 858, "retweet_count": 466, "bookmark_count": 1137, "quote_count": 12, "reply_count": 27, "views_count": 101581, "favorited": true, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1743678390027121105", "metadata": {"__typename": "Tweet", "rest_id": "1743678390027121105", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMjg5MDIwNTQ=", "rest_id": "328902054", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1868193152084922368/Dpa0kpto_normal.jpg"}, "core": {"created_at": "Mon Jul 04 07:01:18 +0000 2011", "name": "<PERSON>", "screen_name": "Maina_Poultry"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Animal Scientist| Retweets ❌ endorsements| Anglican, Conservative and Kenyan Nationalist| https://t.co/DTRSDMOOdY| @AgriNutriLTD", "entities": {"description": {"urls": [{"display_url": "scholar.google.com/citations?hl=e…", "expanded_url": "https://scholar.google.com/citations?hl=en&user=169Xcf", "url": "https://t.co/DTRSDMOOdY", "indices": [90, 113]}]}, "url": {"urls": [{"display_url": "agrinutriventures.org", "expanded_url": "https://agrinutriventures.org", "url": "https://t.co/pcvbwZMQsG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 27000, "followers_count": 7754, "friends_count": 953, "has_custom_timelines": true, "is_translator": false, "listed_count": 13, "media_count": 1150, "normal_followers_count": 7754, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/328902054/1723647591", "profile_interstitial_type": "", "statuses_count": 63076, "translator_type": "none", "url": "https://t.co/pcvbwZMQsG", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Mafikeng, South Africa"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1656894560239247361", "professional_type": "Business", "category": [{"id": 1067, "name": "Researcher", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1743678390027121105"], "editable_until_msecs": "1704563898000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "101581", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3NDM2NzgzODk2OTU3ODcwMDg=", "text": "Young Kenyans who wish to do animal and plant agriculture as a source of income, here are possible resources for you, and I can guarantee the officers in these facilities will be of help:\n1. For fish rearing, processing, and value addition, including selecting quality feeds and fingerings, talk to Kenya Marine and Fisheries Research Institute @KmfriResearch https://t.co/WQFTxRdiqB. They have stations in Sagana, Naivasha, Mombasa and other stations serving the country.\n2. For goat and bovine semen, that is well acclimatized for Kenya. Talk to @KAGRC_KE https://t.co/PKRwbvbrqN. By the way, not all import semen is for breeds that make sense in Kenya. There is no point in importing a pure Holstein to a tropical environment that is low on feed resources and high disease incidence (story for another day).\n3. For rabbits, all breeds: Hybrid rabbits include New Zealand White, California White, and Flemish Giant and their crosses. talk to the National Rabbits Breeding Centre Ngong FTC and Veterinary Farm are under the Livestock Department, and you can call them at +254  0733782096.\n4. For beef and dairy cattle, indigenous and improved poultry, beekeeping and new tech talk to KALRO @kalromkulima  https://t.co/VaKvcUNCsp. Do not fear them; go to their offices and stations and request help, and you can be sure they will help you. \n5. For fruit trees, if you are in Mt Kenya Region, go to @DiscoverJKUAT; if you are in counties surrounding Machakos (ASALs), visit https://t.co/55WKAHdCVd Katumani. KALRO also has other Rift Valley and Western/Nyanza centers.\n6. When I visited the Perkerra Irrigation Scheme at Marigat, they had some highland rice that didn't need a lot of water and some nice fruiting tree seedlings, including pawpaws, peaches, etc. I am not sure whether they still have that rice variety, but you can reach out to them @Irrigation_Auth \n7. For youngsters who want to do training for diplomas and short-term certs in animal health and affiliated courses, dairy tech visit @AhitiKabete and Dairy Training <NAME_EMAIL> respectively. \n8. For biological plant pest management, visit @icipe. They have some very nice pest management methods for crops.   \nSocial media has a lot of young men and women doing wonderful stuff, talk to them when you can and learn from each other.\nFor animal nutrition purposes, talk to graduate students  from our universities @egertonunikenya @DiscoverJKUAT @uonbi @KenyattaUni @EmbuUniversity among others (please be generous in your renumeration for services rendered and give them feedback on what to improve on).      \n@shobanes @CalebKaruga @TuJadili @wmnjoya @akams_ @LegalEzra", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "kmfri.go.ke", "expanded_url": "https://www.kmfri.go.ke/", "url": "https://t.co/WQFTxRdiqB", "indices": [360, 383]}, {"display_url": "kagrc.go.ke", "expanded_url": "https://kagrc.go.ke/", "url": "https://t.co/PKRwbvbrqN", "indices": [558, 581]}, {"display_url": "kalro.org/divisions/live…", "expanded_url": "https://www.kalro.org/divisions/livestock/", "url": "https://t.co/VaKvcUNCsp", "indices": [1206, 1229]}, {"display_url": "kalro.org", "expanded_url": "https://www.kalro.org", "url": "https://t.co/55WKAHdCVd", "indices": [1473, 1496]}], "user_mentions": [{"id_str": "902139078325530624", "name": "KMFRI", "screen_name": "KmfriResearch", "indices": [345, 359]}, {"id_str": "807203202135392256", "name": "Kenya Animal Genetic Resources Centre", "screen_name": "KAGRC_KE", "indices": [548, 557]}, {"id_str": "2601506209", "name": "KALRO", "screen_name": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "indices": [1191, 1204]}, {"id_str": "73829129", "name": "Discover JKUAT", "screen_name": "DiscoverJKUAT", "indices": [1398, 1412]}, {"id_str": "804431221", "name": "National Irrigation Authority", "screen_name": "Irrigation_Auth", "indices": [1848, 1864]}, {"id_str": "1242762270687584256", "name": "AHITI KABETE", "screen_name": "AhitiKabete", "indices": [2000, 2012]}, {"id_str": "60883810", "name": "i<PERSON>pe", "screen_name": "i<PERSON>pe", "indices": [2141, 2147]}, {"id_str": "701837484028727299", "name": "Egerton University", "screen_name": "egertonunikenya", "indices": [2414, 2430]}, {"id_str": "73829129", "name": "Discover JKUAT", "screen_name": "DiscoverJKUAT", "indices": [2431, 2445]}, {"id_str": "151467226", "name": "University of Nairobi", "screen_name": "u<PERSON><PERSON>", "indices": [2446, 2452]}, {"id_str": "347829246", "name": "Kenyatta University #ExperienceKU", "screen_name": "KenyattaUni", "indices": [2453, 2465]}, {"id_str": "1949319510", "name": "Embu University", "screen_name": "EmbuUniversity", "indices": [2466, 2481]}, {"id_str": "337640257", "name": "<PERSON><PERSON><PERSON>", "screen_name": "shobanes", "indices": [2611, 2620]}, {"id_str": "253019710", "name": "The Farmercist", "screen_name": "<PERSON><PERSON><PERSON><PERSON>", "indices": [2621, 2633]}, {"id_str": "781709113713684480", "name": "Croissant Raider INM #<PERSON><PERSON><PERSON>li", "screen_name": "<PERSON><PERSON><PERSON><PERSON>", "indices": [2634, 2643]}, {"id_str": "405874056", "name": "#LandIsNotProperty <PERSON><PERSON><PERSON><PERSON>", "screen_name": "wmn<PERSON><PERSON>", "indices": [2644, 2652]}, {"id_str": "162738412", "name": ".", "screen_name": "akams_", "indices": [2653, 2660]}, {"id_str": "327428920", "name": "Inimicus <PERSON>iae", "screen_name": "LegalEzra", "indices": [2661, 2671]}]}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 1137, "bookmarked": true, "created_at": "Sat Jan 06 16:58:18 +0000 2024", "conversation_id_str": "1743678390027121105", "display_text_range": [0, 278], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 858, "favorited": true, "full_text": "Young Kenyans who wish to do animal and plant agriculture as a source of income, here are possible resources for you, and I can guarantee the officers in these facilities will be of help:\n1. For fish rearing, processing, and value addition, including selecting quality feeds and", "is_quote_status": false, "lang": "en", "quote_count": 12, "reply_count": 27, "retweet_count": 466, "retweeted": false, "user_id_str": "328902054", "id_str": "1743678390027121105"}, "twe_private_fields": {"created_at": 1704560298000, "updated_at": 1748554178748, "media_count": 0}}}]