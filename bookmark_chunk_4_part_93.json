[{"id": "1842571646797811716", "created_at": "2024-10-05 17:24:48 +03:00", "full_text": "You can Crawl entire website with Claude 3.5 or GPT4 with @firecrawl_dev  💯\n\nIts open-sourced and code in github\n\n- Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.\n\n- Crawls all accessible subpages and give you clean data for each. No sitemap required.\n\n- The greatest benefit is that the extracted data is catered for LLM-based pipelines.\n\n- The api is self hostable and opensource\n-----\n\nSome benefits of firecrawl\n\n1. handles crawling (with or without sitemaps)\n2. runs headless browsers scalably\n3. handles bot protections and proxies\n4. a team of dedicated engineers to solve the millions of edge-cases on the web for you\n5. quality formatting to markdown by default\n\nBeautiful soup doesn't generalize, thats why we built firecrawl", "media": [{"type": "photo", "url": "https://t.co/5NYSckzJh7", "thumbnail": "https://pbs.twimg.com/media/GZIhI8ZWsAAxv63?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GZIhI8ZWsAAxv63?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3257, "retweet_count": 397, "bookmark_count": 7027, "quote_count": 17, "reply_count": 63, "views_count": 365630, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1842571646797811716", "metadata": {"__typename": "Tweet", "rest_id": "1842571646797811716", "core": {"user_results": {"result": {"__typename": "User", "id": "********************", "rest_id": "**********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1816185267037859840/Fd18CH0v_normal.jpg"}, "core": {"created_at": "Wed Jun 25 22:38:54 +0000 2014", "name": "<PERSON><PERSON><PERSON>", "screen_name": "rohanpaul_ai"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "💼 Engineer.\n\n📚 I write daily on actionable AI developments.\n\n🗞️ Subscribe and instantly get a 1300+page Python book → https://t.co/lK1BBqjk0Z", "entities": {"description": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "https://rohan-paul.com", "url": "https://t.co/lK1BBqjk0Z", "indices": [118, 141]}]}, "url": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "http://www.rohan-paul.com", "url": "https://t.co/2NKnK0xg7T", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 32936, "followers_count": 64302, "friends_count": 811, "has_custom_timelines": true, "is_translator": false, "listed_count": 1228, "media_count": 15320, "normal_followers_count": 64302, "pinned_tweet_ids_str": ["1898099124165394754"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/**********/**********", "profile_interstitial_type": "", "statuses_count": 38933, "translator_type": "none", "url": "https://t.co/2NKnK0xg7T", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Ex Inv Banker (Deutsche)"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1826415623603180000", "professional_type": "Creator", "category": [{"id": 958, "name": "Entrepreneur", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1842571646797811716"], "editable_until_msecs": "*************", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "365630", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NDI1NzE2NDY2NTkzODczOTI=", "text": "You can Crawl entire website with Claude 3.5 or GPT4 with @firecrawl_dev  💯\n\nIts open-sourced and code in github\n\n- Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.\n\n- Crawls all accessible subpages and give you clean data for each. No sitemap required.\n\n- The greatest benefit is that the extracted data is catered for LLM-based pipelines.\n\n- The api is self hostable and opensource\n-----\n\nSome benefits of firecrawl\n\n1. handles crawling (with or without sitemaps)\n2. runs headless browsers scalably\n3. handles bot protections and proxies\n4. a team of dedicated engineers to solve the millions of edge-cases on the web for you\n5. quality formatting to markdown by default\n\nBeautiful soup doesn't generalize, thats why we built firecrawl", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1796642877172551682", "name": "Firecrawl", "screen_name": "firecrawl_dev", "indices": [58, 72]}]}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 7027, "bookmarked": true, "created_at": "Sat Oct 05 14:24:48 +0000 2024", "conversation_id_str": "1842571646797811716", "display_text_range": [0, 278], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/5NYSckzJh7", "expanded_url": "https://x.com/rohanpaul_ai/status/1842571646797811716/photo/1", "id_str": "1842571635083096064", "indices": [279, 302], "media_key": "3_1842571635083096064", "media_url_https": "https://pbs.twimg.com/media/GZIhI8ZWsAAxv63.jpg", "type": "photo", "url": "https://t.co/5NYSckzJh7", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1434, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2631, "width": 1842, "focus_rects": [{"x": 0, "y": 865, "w": 1842, "h": 1032}, {"x": 0, "y": 460, "w": 1842, "h": 1842}, {"x": 0, "y": 331, "w": 1842, "h": 2100}, {"x": 263, "y": 0, "w": 1316, "h": 2631}, {"x": 0, "y": 0, "w": 1842, "h": 2631}]}, "media_results": {"result": {"media_key": "3_1842571635083096064"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "1796642877172551682", "name": "Firecrawl", "screen_name": "firecrawl_dev", "indices": [58, 72]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/5NYSckzJh7", "expanded_url": "https://x.com/rohanpaul_ai/status/1842571646797811716/photo/1", "id_str": "1842571635083096064", "indices": [279, 302], "media_key": "3_1842571635083096064", "media_url_https": "https://pbs.twimg.com/media/GZIhI8ZWsAAxv63.jpg", "type": "photo", "url": "https://t.co/5NYSckzJh7", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1434, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2631, "width": 1842, "focus_rects": [{"x": 0, "y": 865, "w": 1842, "h": 1032}, {"x": 0, "y": 460, "w": 1842, "h": 1842}, {"x": 0, "y": 331, "w": 1842, "h": 2100}, {"x": 263, "y": 0, "w": 1316, "h": 2631}, {"x": 0, "y": 0, "w": 1842, "h": 2631}]}, "media_results": {"result": {"media_key": "3_1842571635083096064"}}}]}, "favorite_count": 3257, "favorited": false, "full_text": "You can Crawl entire website with Claude 3.5 or GPT4 with @firecrawl_dev  💯\n\nIts open-sourced and code in github\n\n- Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.\n\n- Crawls all accessible subpages and give you clean https://t.co/5NYSckzJh7", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 17, "reply_count": 63, "retweet_count": 397, "retweeted": false, "user_id_str": "**********", "id_str": "1842571646797811716"}, "twe_private_fields": {"created_at": 1728138288000, "updated_at": 1748554144882, "media_count": 1}}}, {"id": "1842605840383316386", "created_at": "2024-10-05 19:40:40 +03:00", "full_text": "GitHub repo:\nhttps://t.co/pmAfee8zYT", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1842605532793934200", "retweeted_status": null, "quoted_status": null, "favorite_count": 49, "retweet_count": 7, "bookmark_count": 57, "quote_count": 0, "reply_count": 0, "views_count": 5296, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1842605840383316386", "metadata": {"__typename": "Tweet", "rest_id": "1842605840383316386", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MDM2MDE5NzI=", "rest_id": "703601972", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1578327351544360960/YFpWSWIX_normal.jpg"}, "core": {"created_at": "Wed Jul 18 18:58:39 +0000 2012", "name": "<PERSON><PERSON><PERSON> 🚀", "screen_name": "akshay_pachaar"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Simplifying LLMs, AI Agents, RAGs and Machine Learning for you! • Co-founder @dailydoseofds_• BITS Pilani • 3 Patents • ex-AI Engineer @ LightningAI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "join.dailydoseofds.com", "expanded_url": "http://join.dailydoseofds.com", "url": "https://t.co/TLsKA1fohN", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 20351, "followers_count": 206136, "friends_count": 470, "has_custom_timelines": true, "is_translator": false, "listed_count": 2743, "media_count": 3895, "normal_followers_count": 206136, "pinned_tweet_ids_str": ["1724322731376918560"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/703601972/1733646485", "profile_interstitial_type": "", "statuses_count": 17580, "translator_type": "none", "url": "https://t.co/TLsKA1fohN", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Learn AI Engineering 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1476572433905717251", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/pmAfee8zYT", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 200, "width": 400, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Your agent in your terminal, equipped with local tools: writes code, uses the terminal, browses the web, vision. - gptme/gptme", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 600, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=600x600"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 72, "width": 144, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "Your agent in your terminal, equipped with local tools: writes code, uses the terminal, browses the web, vision. - gptme/gptme", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "Your agent in your terminal, equipped with local tools: writes code, uses the terminal, browses the web, vision. - gptme/gptme", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 93.77}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 2.91}, {"rgb": {"blue": 124, "green": 115, "red": 92}, "percentage": 2.8}, {"rgb": {"blue": 70, "green": 56, "red": 19}, "percentage": 0.2}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "GitHub - gptme/gptme: Your agent in your terminal, equipped with local tools: writes code, uses the...", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 93.77}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 2.91}, {"rgb": {"blue": 124, "green": 115, "red": 92}, "percentage": 2.8}, {"rgb": {"blue": 70, "green": 56, "red": 19}, "percentage": 0.2}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 93.77}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 2.91}, {"rgb": {"blue": 124, "green": 115, "red": 92}, "percentage": 2.8}, {"rgb": {"blue": 70, "green": 56, "red": 19}, "percentage": 0.2}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/pmAfee8zYT", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927575857595387904/7jHj9pdc?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/pmAfee8zYT", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620091, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17801, "media_count": 2646, "normal_followers_count": 2620091, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1842605840383316386"], "editable_until_msecs": "1728150040000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "5296", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 57, "bookmarked": true, "created_at": "Sat Oct 05 16:40:40 +0000 2024", "conversation_id_str": "1842605532793934200", "display_text_range": [0, 36], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/ErikBjare/gptme", "expanded_url": "https://github.com/ErikBjare/gptme", "url": "https://t.co/pmAfee8zYT", "indices": [13, 36]}], "user_mentions": []}, "favorite_count": 49, "favorited": false, "full_text": "GitHub repo:\nhttps://t.co/pmAfee8zYT", "in_reply_to_screen_name": "akshay_pachaar", "in_reply_to_status_id_str": "1842605532793934200", "in_reply_to_user_id_str": "703601972", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 0, "retweet_count": 7, "retweeted": false, "user_id_str": "703601972", "id_str": "1842605840383316386"}, "twe_private_fields": {"created_at": 1728146440000, "updated_at": 1748554144882, "media_count": 0}}}]