[{"id": "1606620961612013568", "created_at": "2022-12-24 15:01:01 +03:00", "full_text": "I have been coding in Python for 8 years now. ⏳\n\nHere's a roadmap to Master Python! 🚀\n\nA Thread 🧵👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4871, "retweet_count": 1531, "bookmark_count": 3969, "quote_count": 29, "reply_count": 151, "views_count": 595461, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1606620961612013568", "metadata": {"__typename": "Tweet", "rest_id": "1606620961612013568", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MDM2MDE5NzI=", "rest_id": "703601972", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1578327351544360960/YFpWSWIX_normal.jpg"}, "core": {"created_at": "Wed Jul 18 18:58:39 +0000 2012", "name": "<PERSON><PERSON><PERSON> 🚀", "screen_name": "akshay_pachaar"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Simplifying LLMs, AI Agents, RAGs and Machine Learning for you! • Co-founder @dailydoseofds_• BITS Pilani • 3 Patents • ex-AI Engineer @ LightningAI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "join.dailydoseofds.com", "expanded_url": "http://join.dailydoseofds.com", "url": "https://t.co/TLsKA1fohN", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 20351, "followers_count": 206134, "friends_count": 470, "has_custom_timelines": true, "is_translator": false, "listed_count": 2743, "media_count": 3895, "normal_followers_count": 206134, "pinned_tweet_ids_str": ["1724322731376918560"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/703601972/1733646485", "profile_interstitial_type": "", "statuses_count": 17580, "translator_type": "none", "url": "https://t.co/TLsKA1fohN", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Learn AI Engineering 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1476572433905717251", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1606620961612013568"], "editable_until_msecs": "1671885061000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "595461", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3969, "bookmarked": true, "created_at": "Sat Dec 24 12:01:01 +0000 2022", "conversation_id_str": "1606620961612013568", "display_text_range": [0, 98], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 4871, "favorited": false, "full_text": "I have been coding in Python for 8 years now. ⏳\n\nHere's a roadmap to Master Python! 🚀\n\nA Thread 🧵👇", "is_quote_status": false, "lang": "en", "quote_count": 29, "reply_count": 151, "retweet_count": 1531, "retweeted": false, "user_id_str": "703601972", "id_str": "1606620961612013568"}, "twe_private_fields": {"created_at": 1671883261000, "updated_at": 1748554206264, "media_count": 0}}}, {"id": "1606650761206960130", "created_at": "2022-12-24 16:59:26 +03:00", "full_text": "5 things everyone wants:\n\nMore Money\nBetter Health\nOvercome Debt\nConsistent Habits\nIncreased Knowledge\n\n5 Excel &amp; Google Sheets templates you can use to build a better you in 2023: 📊", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1005, "retweet_count": 204, "bookmark_count": 1115, "quote_count": 6, "reply_count": 38, "views_count": 486028, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1606650761206960130", "metadata": {"__typename": "Tweet", "rest_id": "1606650761206960130", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MTUzNzgyMzQ5OTc2MTY2NDA=", "rest_id": "715378234997616640", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1613615173507420199/UuYkLgPK_normal.jpg"}, "core": {"created_at": "Thu Mar 31 03:20:37 +0000 2016", "name": "<PERSON>", "screen_name": "blake<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Fan of Bourbon, Books, and Good Advice 🥃 📚 💬", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 30503, "followers_count": 405535, "friends_count": 178, "has_custom_timelines": true, "is_translator": false, "listed_count": 9166, "media_count": 1103, "normal_followers_count": 405535, "pinned_tweet_ids_str": ["1795103776459907270"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/715378234997616640/1693065189", "profile_interstitial_type": "", "statuses_count": 15328, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Oklahoma City, OK"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1557501059995766786", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "<PERSON><PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1606650761206960130"], "editable_until_msecs": "1671892166000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "486028", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1115, "bookmarked": true, "created_at": "Sat Dec 24 13:59:26 +0000 2022", "conversation_id_str": "1606650761206960130", "display_text_range": [0, 186], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1005, "favorited": false, "full_text": "5 things everyone wants:\n\nMore Money\nBetter Health\nOvercome Debt\nConsistent Habits\nIncreased Knowledge\n\n5 Excel &amp; Google Sheets templates you can use to build a better you in 2023: 📊", "is_quote_status": false, "lang": "en", "quote_count": 6, "reply_count": 38, "retweet_count": 204, "retweeted": false, "user_id_str": "715378234997616640", "id_str": "1606650761206960130"}, "twe_private_fields": {"created_at": 1671890366000, "updated_at": 1748554206264, "media_count": 0}}}]