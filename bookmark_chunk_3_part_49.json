[{"id": "1560407703708745735", "created_at": "2022-08-19 02:26:02 +03:00", "full_text": "Adding a CTRL key to an alphabet is magic. \nSave it. https://t.co/y1QTOgkok6", "media": [{"type": "photo", "url": "https://t.co/y1QTOgkok6", "thumbnail": "https://pbs.twimg.com/media/Faeuc7nX0AAYpZd?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Faeuc7nX0AAYpZd?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 9508, "retweet_count": 2320, "bookmark_count": 2729, "quote_count": 43, "reply_count": 33, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1560407703708745735", "metadata": {"__typename": "Tweet", "rest_id": "1560407703708745735", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjcyMzMwODgxODk0NjcwMzQx", "rest_id": "1272330881894670341", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1272513897447264267/_7KnHbcM_normal.png"}, "core": {"created_at": "Mon Jun 15 00:52:19 +0000 2020", "name": "Tech <PERSON>ito", "screen_name": "TechBurritoUno"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Tech | Learning | Innovation \"Any sufficiently advanced technology is equivalent to magic.\" – Sir <PERSON>", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 3155, "followers_count": 829703, "friends_count": 292, "has_custom_timelines": true, "is_translator": false, "listed_count": 3640, "media_count": 8511, "normal_followers_count": 829703, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1272330881894670341/1592182561", "profile_interstitial_type": "", "statuses_count": 10664, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1636758295766618113", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1560407703708745735"], "editable_until_msecs": "1660866962000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2729, "bookmarked": true, "created_at": "Thu Aug 18 23:26:02 +0000 2022", "conversation_id_str": "1560407703708745735", "display_text_range": [0, 52], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/y1QTOgkok6", "expanded_url": "https://x.com/TechAmazing/status/1560407703708745735/photo/1", "id_str": "1560407607969566720", "indices": [53, 76], "media_key": "3_1560407607969566720", "media_url_https": "https://pbs.twimg.com/media/Faeuc7nX0AAYpZd.jpg", "type": "photo", "url": "https://t.co/y1QTOgkok6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 768, "w": 403, "resize": "fit"}, "medium": {"h": 768, "w": 403, "resize": "fit"}, "small": {"h": 680, "w": 357, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 768, "width": 403, "focus_rects": [{"x": 0, "y": 0, "w": 403, "h": 226}, {"x": 0, "y": 0, "w": 403, "h": 403}, {"x": 0, "y": 0, "w": 403, "h": 459}, {"x": 0, "y": 0, "w": 384, "h": 768}, {"x": 0, "y": 0, "w": 403, "h": 768}]}, "media_results": {"result": {"media_key": "3_1560407607969566720"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/y1QTOgkok6", "expanded_url": "https://x.com/TechAmazing/status/1560407703708745735/photo/1", "id_str": "1560407607969566720", "indices": [53, 76], "media_key": "3_1560407607969566720", "media_url_https": "https://pbs.twimg.com/media/Faeuc7nX0AAYpZd.jpg", "type": "photo", "url": "https://t.co/y1QTOgkok6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 768, "w": 403, "resize": "fit"}, "medium": {"h": 768, "w": 403, "resize": "fit"}, "small": {"h": 680, "w": 357, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 768, "width": 403, "focus_rects": [{"x": 0, "y": 0, "w": 403, "h": 226}, {"x": 0, "y": 0, "w": 403, "h": 403}, {"x": 0, "y": 0, "w": 403, "h": 459}, {"x": 0, "y": 0, "w": 384, "h": 768}, {"x": 0, "y": 0, "w": 403, "h": 768}]}, "media_results": {"result": {"media_key": "3_1560407607969566720"}}}]}, "favorite_count": 9508, "favorited": false, "full_text": "Adding a CTRL key to an alphabet is magic. \nSave it. https://t.co/y1QTOgkok6", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 43, "reply_count": 33, "retweet_count": 2320, "retweeted": false, "user_id_str": "1272330881894670341", "id_str": "1560407703708745735"}, "twe_private_fields": {"created_at": 1660865162000, "updated_at": 1748554247988, "media_count": 1}}}, {"id": "1561043840865566725", "created_at": "2022-08-20 20:33:49 +03:00", "full_text": "190 Python Projects with Source Code: solved and explained\nhttps://t.co/JROrDkFvX9\n\n#Data #DataScience #dataanalysis #DataAnalytics #dataScientist #machinelearning #python #pythonprogramming #pythonprojects #pythoncode #artificialintelligence #ai #100DaysOfCode https://t.co/7Hjzg6RyRG", "media": [{"type": "photo", "url": "https://t.co/7Hjzg6RyRG", "thumbnail": "https://pbs.twimg.com/media/FanxCBvagAAKexM?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FanxCBvagAAKexM?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4531, "retweet_count": 1366, "bookmark_count": 3534, "quote_count": 23, "reply_count": 127, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1561043840865566725", "metadata": {"__typename": "Tweet", "rest_id": "1561043840865566725", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo0MjQ0MDgyMTQw", "rest_id": "4244082140", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1914219424690716672/oEBkVEtp_normal.jpg"}, "core": {"created_at": "Sun Nov 15 09:22:14 +0000 2015", "name": "<PERSON><PERSON>", "screen_name": "amankk_9"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I write stories behind the data📈 Founder @ https://t.co/GugwcPV7Ab Sharing Data Science tips & resources to prepare you for the real world 📈", "entities": {"description": {"urls": [{"display_url": "statso.io", "expanded_url": "http://statso.io", "url": "https://t.co/GugwcPV7Ab", "indices": [43, 66]}]}, "url": {"urls": [{"display_url": "amzn.in/d/dcayyT5", "expanded_url": "https://amzn.in/d/dcayyT5", "url": "https://t.co/PuqkGhji7d", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6531, "followers_count": 8966, "friends_count": 74, "has_custom_timelines": true, "is_translator": false, "listed_count": 159, "media_count": 533, "normal_followers_count": 8966, "pinned_tweet_ids_str": ["1858191996281651622"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/4244082140/1729955762", "profile_interstitial_type": "", "statuses_count": 4002, "translator_type": "none", "url": "https://t.co/PuqkGhji7d", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "New Delhi, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1538450349123436545", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1561043840865566725"], "editable_until_msecs": "1661018629000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3534, "bookmarked": true, "created_at": "Sat Aug 20 17:33:49 +0000 2022", "conversation_id_str": "1561043840865566725", "display_text_range": [0, 261], "entities": {"hashtags": [{"indices": [84, 89], "text": "Data"}, {"indices": [90, 102], "text": "DataScience"}, {"indices": [103, 116], "text": "dataanalysis"}, {"indices": [117, 131], "text": "DataAnalytics"}, {"indices": [132, 146], "text": "dataScientist"}, {"indices": [147, 163], "text": "machinelearning"}, {"indices": [164, 171], "text": "python"}, {"indices": [172, 190], "text": "pythonprogramming"}, {"indices": [191, 206], "text": "pythonprojects"}, {"indices": [207, 218], "text": "pythoncode"}, {"indices": [219, 242], "text": "artificialintelligence"}, {"indices": [243, 246], "text": "ai"}, {"indices": [247, 261], "text": "100DaysOfCode"}], "media": [{"display_url": "pic.x.com/7Hjzg6RyRG", "expanded_url": "https://x.com/amankk_9/status/1561043840865566725/photo/1", "id_str": "1561043762989924352", "indices": [262, 285], "media_key": "3_1561043762989924352", "media_url_https": "https://pbs.twimg.com/media/FanxCBvagAAKexM.jpg", "type": "photo", "url": "https://t.co/7Hjzg6RyRG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 787, "w": 1400, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 382, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 787, "width": 1400, "focus_rects": [{"x": 0, "y": 0, "w": 1400, "h": 784}, {"x": 201, "y": 0, "w": 787, "h": 787}, {"x": 249, "y": 0, "w": 690, "h": 787}, {"x": 397, "y": 0, "w": 394, "h": 787}, {"x": 0, "y": 0, "w": 1400, "h": 787}]}, "media_results": {"result": {"media_key": "3_1561043762989924352"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "medium.com/@aman<PERSON><PERSON>/1…", "expanded_url": "https://medium.com/@amankharwal/130-python-projects-with-source-code-61f498591bb", "url": "https://t.co/JROrDkFvX9", "indices": [59, 82]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/7Hjzg6RyRG", "expanded_url": "https://x.com/amankk_9/status/1561043840865566725/photo/1", "id_str": "1561043762989924352", "indices": [262, 285], "media_key": "3_1561043762989924352", "media_url_https": "https://pbs.twimg.com/media/FanxCBvagAAKexM.jpg", "type": "photo", "url": "https://t.co/7Hjzg6RyRG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 787, "w": 1400, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 382, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 787, "width": 1400, "focus_rects": [{"x": 0, "y": 0, "w": 1400, "h": 784}, {"x": 201, "y": 0, "w": 787, "h": 787}, {"x": 249, "y": 0, "w": 690, "h": 787}, {"x": 397, "y": 0, "w": 394, "h": 787}, {"x": 0, "y": 0, "w": 1400, "h": 787}]}, "media_results": {"result": {"media_key": "3_1561043762989924352"}}}]}, "favorite_count": 4531, "favorited": false, "full_text": "190 Python Projects with Source Code: solved and explained\nhttps://t.co/JROrDkFvX9\n\n#Data #DataScience #dataanalysis #DataAnalytics #dataScientist #machinelearning #python #pythonprogramming #pythonprojects #pythoncode #artificialintelligence #ai #100DaysOfCode https://t.co/7Hjzg6RyRG", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 23, "reply_count": 127, "retweet_count": 1366, "retweeted": false, "user_id_str": "4244082140", "id_str": "1561043840865566725"}, "twe_private_fields": {"created_at": 1661016829000, "updated_at": 1748554247988, "media_count": 1}}}]