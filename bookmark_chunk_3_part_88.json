[{"id": "1595714342183473152", "created_at": "2022-11-24 12:42:00 +03:00", "full_text": "Recently, I wrote several threads on academic writing that went (sort of) viral on Academic Twitter.\n\nThought I'd put them together so folks can find them all in one place.\n\nSo, here goes 👇\n\nThe habit and process of academic writing: a meta-thread 🧵", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2471, "retweet_count": 660, "bookmark_count": 2743, "quote_count": 27, "reply_count": 49, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1595714342183473152", "metadata": {"__typename": "Tweet", "rest_id": "1595714342183473152", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTEzMjM1Mjk4MTkyNDM3MjYy", "rest_id": "1513235298192437262", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1575897887019499520/xs2ar5XJ_normal.jpg"}, "core": {"created_at": "Sun Apr 10 19:20:02 +0000 2022", "name": "<PERSON><PERSON><PERSON><PERSON>, PhD", "screen_name": "MushtaqBilalPhD"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I simplify the process of academic writing | Helped 6,000+ become efficient academic writers with AI | https://t.co/SKfrXx2V5q 1,000+ users https://t.co/uBj501H1TR 250+ users", "entities": {"description": {"urls": [{"display_url": "ResearchKick.com", "expanded_url": "http://ResearchKick.com", "url": "https://t.co/SKfrXx2V5q", "indices": [103, 126]}, {"display_url": "ChatAcademia.com", "expanded_url": "http://ChatAcademia.com", "url": "https://t.co/uBj501H1TR", "indices": [140, 163]}]}, "url": {"urls": [{"display_url": "mushtaqbilalphd.thrivecart.com/ai", "expanded_url": "https://mushtaqbilalphd.thrivecart.com/ai", "url": "https://t.co/BcK6JY3TJS", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 24593, "followers_count": 237342, "friends_count": 29, "has_custom_timelines": true, "is_translator": false, "listed_count": 3079, "media_count": 4308, "normal_followers_count": 237342, "pinned_tweet_ids_str": ["1924446185483297194"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1513235298192437262/1726904371", "profile_interstitial_type": "", "statuses_count": 21367, "translator_type": "none", "url": "https://t.co/BcK6JY3TJS", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Get AI tutorial here ↓"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1534985053637427200", "professional_type": "Creator", "category": [{"id": 1065, "name": "Academic", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1595714342183473152"], "editable_until_msecs": "1669284720000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2743, "bookmarked": true, "created_at": "Thu Nov 24 09:42:00 +0000 2022", "conversation_id_str": "1595714342183473152", "display_text_range": [0, 249], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2471, "favorited": false, "full_text": "Recently, I wrote several threads on academic writing that went (sort of) viral on Academic Twitter.\n\nThought I'd put them together so folks can find them all in one place.\n\nSo, here goes 👇\n\nThe habit and process of academic writing: a meta-thread 🧵", "is_quote_status": false, "lang": "en", "quote_count": 27, "reply_count": 49, "retweet_count": 660, "retweeted": false, "user_id_str": "1513235298192437262", "id_str": "1595714342183473152"}, "twe_private_fields": {"created_at": 1669282920000, "updated_at": 1748554222417, "media_count": 0}}}, {"id": "1595770036039614465", "created_at": "2022-11-24 16:23:19 +03:00", "full_text": "PowerBI is a very powerful data visualization tool, which any Data Analyst can't ignore.\n\nMaster PowerBI with these FREE Courses. \n\nA Thread 🧵👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2492, "retweet_count": 755, "bookmark_count": 1991, "quote_count": 10, "reply_count": 81, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1595770036039614465", "metadata": {"__typename": "Tweet", "rest_id": "1595770036039614465", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTQ1Njg4MDY0OTU3NTA5NjMy", "rest_id": "1545688064957509632", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1545703628987834368/foTxvTxE_normal.jpg"}, "core": {"created_at": "Sat Jul 09 08:35:34 +0000 2022", "name": "Python Space", "screen_name": "python_spaces"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Full-time Python Engineer  \n\n- Sharing daily insights on Python, ML and AI.  \n\n- DM/<EMAIL> for collaboration \n\n- Discord: https://t.co/3JkAuo6imH", "entities": {"description": {"urls": [{"display_url": "discord.gg/ycM8EJBHdE", "expanded_url": "http://discord.gg/ycM8EJBHdE", "url": "https://t.co/3JkAuo6imH", "indices": [138, 161]}]}, "url": {"urls": [{"display_url": "youtube.com/@python_space", "expanded_url": "https://www.youtube.com/@python_space", "url": "https://t.co/J4WKjK3BxI", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7148, "followers_count": 88565, "friends_count": 155, "has_custom_timelines": true, "is_translator": false, "listed_count": 996, "media_count": 796, "normal_followers_count": 88565, "pinned_tweet_ids_str": ["1925577617903652989"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1545688064957509632/1666782479", "profile_interstitial_type": "", "statuses_count": 4121, "translator_type": "none", "url": "https://t.co/J4WKjK3BxI", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Remote"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1595770036039614465"], "editable_until_msecs": "1669297999000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1991, "bookmarked": true, "created_at": "Thu Nov 24 13:23:19 +0000 2022", "conversation_id_str": "1595770036039614465", "display_text_range": [0, 143], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2492, "favorited": false, "full_text": "PowerBI is a very powerful data visualization tool, which any Data Analyst can't ignore.\n\nMaster PowerBI with these FREE Courses. \n\nA Thread 🧵👇", "is_quote_status": false, "lang": "en", "quote_count": 10, "reply_count": 81, "retweet_count": 755, "retweeted": false, "user_id_str": "1545688064957509632", "id_str": "1595770036039614465"}, "twe_private_fields": {"created_at": 1669296199000, "updated_at": 1748554222418, "media_count": 0}}}]