# Bookmarks from bookmark_chunk_3_part_43.json

## Category: Computer Science / Data Structures / Algorithms / Learning Resources / Courses / Java / Python

*   **Author:** @javinpaul
    **Date:** 2022-08-14 08:58:54 +03:00
    **Content:** 5 Best Courses to Learn Data Structure
1. Data Structures Deep Dive Using Java - https://t.co/B6UKy9YjBi
2. DSA by <PERSON> - https://t.co/eg4jYMlxGw
3. Algorithms  - https://t.co/fmd5G9Tdhl
3. Data Structures - https://t.co/NXkh4xROaJ
4. DSA in Python - https://t.co/XE5YSs4oL9 https://t.co/AI5jquBSKf
    **URL:** [https://twitter.com/javinpaul/status/1558694634255106054](https://twitter.com/javinpaul/status/1558694634255106054)

---

## Category: Software / Productivity / Microsoft Excel / Tips & Tricks / Data Manipulation

*   **Author:** @pascal_bornet
    **Date:** 2022-08-14 13:56:38 +03:00
    **Content:** Sharing is caring

Here are a few tricks to work faster with Excel.
Excel remains a useful tool for quick manipulation of datasets, checks of values, stats, and transformations

Credit: 7-Second Riddles
#automation #excel #maths #science https://t.co/nPHEL9XAfY
    **URL:** [https://twitter.com/pascal_bornet/status/1558769559934586881](https://twitter.com/pascal_bornet/status/1558769559934586881)

---
