[{"id": "1575789286338748417", "created_at": "2022-09-30 13:06:57 +03:00", "full_text": "12 Sites to Practice Coding with Exercises ✨😍\n\n🔹 codewars . com\n🔹 leetcode . com\n🔹 hackerrank . com\n🔹 topcoder . com\n🔹 exercism . org\n🔹 coderbyte . com\n🔹 codingame . com\n🔹 codechef . com\n🔹 projecteuler . net\n🔹 edabit . com\n🔹 codeforces. com\n🔹 hackerearth. com", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 6326, "retweet_count": 1738, "bookmark_count": 5043, "quote_count": 15, "reply_count": 106, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1575789286338748417", "metadata": {"__typename": "Tweet", "rest_id": "1575789286338748417", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjgwNDQ3MjAxNjk3NzcxNTI2", "rest_id": "1280447201697771526", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1526471322762715136/w25QtNnl_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 07 10:23:10 +0000 2020", "name": "Madza 👨‍💻⚡", "screen_name": "<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Tech writer @logrocket, @sitepointdotcom and @refine_dev 📚\nBuilt DevTunes FM (1.4M+ listens) and DevQuizzes (900K+ answers) 🚀", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "bio.link/madza", "expanded_url": "http://bio.link/madza", "url": "https://t.co/38yzpZRftM", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 113224, "followers_count": 122645, "friends_count": 462, "has_custom_timelines": true, "is_translator": false, "listed_count": 1866, "media_count": 5217, "normal_followers_count": 122645, "pinned_tweet_ids_str": ["1928051243102130540"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1280447201697771526/1669392807", "profile_interstitial_type": "", "statuses_count": 39254, "translator_type": "none", "url": "https://t.co/38yzpZRftM", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "All my work 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1575789286338748417"], "editable_until_msecs": "1664534217000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5043, "bookmarked": true, "created_at": "Fri Sep 30 10:06:57 +0000 2022", "conversation_id_str": "1575789286338748417", "display_text_range": [0, 259], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 6326, "favorited": false, "full_text": "12 Sites to Practice Coding with Exercises ✨😍\n\n🔹 codewars . com\n🔹 leetcode . com\n🔹 hackerrank . com\n🔹 topcoder . com\n🔹 exercism . org\n🔹 coderbyte . com\n🔹 codingame . com\n🔹 codechef . com\n🔹 projecteuler . net\n🔹 edabit . com\n🔹 codeforces. com\n🔹 hackerearth. com", "is_quote_status": false, "lang": "en", "quote_count": 15, "reply_count": 106, "retweet_count": 1738, "retweeted": false, "user_id_str": "1280447201697771526", "id_str": "1575789286338748417"}, "twe_private_fields": {"created_at": 1664532417000, "updated_at": 1748554235792, "media_count": 0}}}, {"id": "1576036697074061313", "created_at": "2022-10-01 05:30:04 +03:00", "full_text": "It's 1st of October today.\nYou have 3 more months to crush SQL before 2022 ends.\n\nA Zero to SQL 90 Days Roadmap 📆", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 7177, "retweet_count": 1921, "bookmark_count": 5931, "quote_count": 44, "reply_count": 152, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1576036697074061313", "metadata": {"__typename": "Tweet", "rest_id": "1576036697074061313", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzAwODA1MDkzMjEwNTU0MzY4", "rest_id": "1300805093210554368", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1421773130419236867/4_S-_L2i_normal.jpg"}, "core": {"created_at": "Tue Sep 01 14:38:15 +0000 2020", "name": "Analytical Aakriti | The SQL Gal", "screen_name": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Open for DevRel roles!\nData Nerd | Technical Writer | SQL Obsessed | Python Possessed | Let's dig into data the simple, effective and no nonsense way 📊", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/aakriti_sharma", "expanded_url": "https://linktr.ee/aakriti_sharma", "url": "https://t.co/m4DVhDKg9b", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1962, "followers_count": 72439, "friends_count": 159, "has_custom_timelines": true, "is_translator": false, "listed_count": 656, "media_count": 318, "normal_followers_count": 72439, "pinned_tweet_ids_str": ["1565581836629458944"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1300805093210554368/1665297269", "profile_interstitial_type": "", "statuses_count": 2649, "translator_type": "none", "url": "https://t.co/m4DVhDKg9b", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Mumbai, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460810126042882049", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1576036697074061313"], "editable_until_msecs": "1664593204000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5931, "bookmarked": true, "created_at": "Sat Oct 01 02:30:04 +0000 2022", "conversation_id_str": "1576036697074061313", "display_text_range": [0, 113], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 7177, "favorited": false, "full_text": "It's 1st of October today.\nYou have 3 more months to crush SQL before 2022 ends.\n\nA Zero to SQL 90 Days Roadmap 📆", "is_quote_status": false, "lang": "en", "quote_count": 44, "reply_count": 152, "retweet_count": 1921, "retweeted": false, "user_id_str": "1300805093210554368", "id_str": "1576036697074061313"}, "twe_private_fields": {"created_at": 1664591404000, "updated_at": 1748554235792, "media_count": 0}}}]