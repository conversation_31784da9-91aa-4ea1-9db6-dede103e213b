[{"id": "1563759281177915392", "created_at": "2022-08-28 08:24:00 +03:00", "full_text": "Learn for Free.\n\n1. HTML ➩ https://t.co/jzoRx6AZil\n2. CSS ➩ https://t.co/MCQl9wASvZ\n3. Git ➩ https://t.co/80tP825Er5\n5. JavaScript ➩ https://t.co/rrDhUN6xYN\n6. React ➩ https://t.co/yHTPKtePur\n7. Interview ➩ https://t.co/4F2nkAZEkL\n\nCrack a Job.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4374, "retweet_count": 1560, "bookmark_count": 3097, "quote_count": 26, "reply_count": 91, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1563759281177915392", "metadata": {"__typename": "Tweet", "rest_id": "1563759281177915392", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTAzODE0ODM0OTc4NDIyNzg0", "rest_id": "1503814834978422784", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1649631155371790336/ky3SAkHj_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Mar 15 19:26:23 +0000 2022", "name": "codemarch", "screen_name": "codemarch"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Tech edu reimagined🎓 • Empowering through coding👩‍💻👨‍💻, development & personal branding • Bridging the gap to dream jobs", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 13895, "followers_count": 61092, "friends_count": 195, "has_custom_timelines": true, "is_translator": false, "listed_count": 532, "media_count": 3749, "normal_followers_count": 61092, "pinned_tweet_ids_str": ["1851183846005620841"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1503814834978422784/1682137696", "profile_interstitial_type": "", "statuses_count": 12366, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1563759281177915392"], "editable_until_msecs": "1661666040000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3097, "bookmarked": true, "created_at": "Sun Aug 28 05:24:00 +0000 2022", "conversation_id_str": "1563759281177915392", "display_text_range": [0, 244], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "javascript.info", "expanded_url": "http://javascript.info", "url": "https://t.co/rrDhUN6xYN", "indices": [133, 156]}, {"display_url": "learngitbranching.js.org", "expanded_url": "http://learngitbranching.js.org", "url": "https://t.co/80tP825Er5", "indices": [93, 116]}, {"display_url": "codecademy.com", "expanded_url": "http://codecademy.com", "url": "https://t.co/jzoRx6AZil", "indices": [27, 50]}, {"display_url": "web.dev", "expanded_url": "http://web.dev", "url": "https://t.co/MCQl9wASvZ", "indices": [60, 83]}, {"display_url": "reactjs.org", "expanded_url": "http://reactjs.org", "url": "https://t.co/yHTPKtePur", "indices": [168, 191]}, {"display_url": "interviewbit.com", "expanded_url": "http://interviewbit.com", "url": "https://t.co/4F2nkAZEkL", "indices": [207, 230]}], "user_mentions": []}, "favorite_count": 4374, "favorited": false, "full_text": "Learn for Free.\n\n1. HTML ➩ https://t.co/jzoRx6AZil\n2. CSS ➩ https://t.co/MCQl9wASvZ\n3. Git ➩ https://t.co/80tP825Er5\n5. JavaScript ➩ https://t.co/rrDhUN6xYN\n6. React ➩ https://t.co/yHTPKtePur\n7. Interview ➩ https://t.co/4F2nkAZEkL\n\nCrack a Job.", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 26, "reply_count": 91, "retweet_count": 1560, "retweeted": false, "user_id_str": "1503814834978422784", "id_str": "1563759281177915392"}, "twe_private_fields": {"created_at": 1661664240000, "updated_at": 1748554240582, "media_count": 0}}}, {"id": "1563986267951480836", "created_at": "2022-08-28 23:25:58 +03:00", "full_text": "100+ Free Books on #MachineLearning, Web Development, Data, #ComputerScience, #Algorithms, Data Structures, and more — big list compiled by @NainaChaturved8 \n—————\n#BigData #DataScience #AI #Coding #Python #Database\nhttps://t.co/TbJbVKiNwc", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 901, "retweet_count": 291, "bookmark_count": 647, "quote_count": 1, "reply_count": 13, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1563986267951480836", "metadata": {"__typename": "Tweet", "rest_id": "1563986267951480836", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1MzQ1NjM5NzY=", "rest_id": "534563976", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1112733580948635648/s-8d1avb_normal.jpg"}, "core": {"created_at": "Fri Mar 23 16:35:17 +0000 2012", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Advisor to startups. Freelancer. Founder of @LeadershipData. Global Speaker. Top influencer #BigData #DataScience #AI #IoT #ML #B2B. PhD Astrophysics @Caltech", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/kirkdborne", "expanded_url": "http://www.linkedin.com/in/kirkdborne", "url": "https://t.co/g46xALu4Eu", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 238745, "followers_count": 463503, "friends_count": 5532, "has_custom_timelines": true, "is_translator": false, "listed_count": 10132, "media_count": 94041, "normal_followers_count": 463503, "pinned_tweet_ids_str": ["1913816024513732984"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/534563976/1706648943", "profile_interstitial_type": "", "statuses_count": 184820, "translator_type": "none", "url": "https://t.co/g46xALu4Eu", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Maryland, USA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1494394757388378113", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************", "venmo_handle": "kirk-borne"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/TbJbVKiNwc", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.82308", "type": "STRING"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 150, "width": 200, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=280x150"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Because education should be free…( download today!!)", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "medium.datadriveninvestor.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 400, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 450, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "895255995961663492", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 75, "width": 100, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=100x100"}, "type": "IMAGE"}}, {"key": "app_num_ratings", "value": {"string_value": "148,423", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 450, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 450, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "medium.datadriveninvestor.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "app_name", "value": {"string_value": "Medium: Read & Write Stories", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 193, "green": 198, "red": 199}, "percentage": 51.64}, {"rgb": {"blue": 74, "green": 122, "red": 164}, "percentage": 18.95}, {"rgb": {"blue": 45, "green": 46, "red": 45}, "percentage": 15.49}, {"rgb": {"blue": 133, "green": 123, "red": 106}, "percentage": 2.24}, {"rgb": {"blue": 35, "green": 99, "red": 151}, "percentage": 2.03}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "90+ Free ML, Web Development, Data, Computer Science Books for All — Part 1", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 193, "green": 198, "red": 199}, "percentage": 51.64}, {"rgb": {"blue": 74, "green": 122, "red": 164}, "percentage": 18.95}, {"rgb": {"blue": 45, "green": 46, "red": 45}, "percentage": 15.49}, {"rgb": {"blue": 133, "green": 123, "red": 106}, "percentage": 2.24}, {"rgb": {"blue": 35, "green": 99, "red": 151}, "percentage": 2.03}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 450, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 193, "green": 198, "red": 199}, "percentage": 51.64}, {"rgb": {"blue": 74, "green": 122, "red": 164}, "percentage": 18.95}, {"rgb": {"blue": 45, "green": 46, "red": 45}, "percentage": 15.49}, {"rgb": {"blue": 133, "green": 123, "red": 106}, "percentage": 2.24}, {"rgb": {"blue": 35, "green": 99, "red": 151}, "percentage": 2.03}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 450, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/TbJbVKiNwc", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 450, "width": 600, "url": "https://pbs.twimg.com/card_img/1926241089360400384/6jS7y0dI?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/TbJbVKiNwc", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjo4OTUyNTU5OTU5NjE2NjM0OTI=", "rest_id": "895255995961663492", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/976315989993308161/Bobgl6Ag_normal.jpg"}, "core": {"created_at": "Wed Aug 09 12:10:37 +0000 2017", "name": "DataDrivenInvestor", "screen_name": "DDInvestorHQ"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "empowerment with data, knowledge, and expertise\n#tech #finance #entrepreneurship", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "datadriveninvestor.com", "expanded_url": "https://www.datadriveninvestor.com", "url": "https://t.co/GFOkBXzUXC", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1547, "followers_count": 2826, "friends_count": 865, "has_custom_timelines": true, "is_translator": false, "listed_count": 54, "media_count": 866, "normal_followers_count": 2826, "pinned_tweet_ids_str": ["1002112304845152256"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/895255995961663492/1502281250", "profile_interstitial_type": "", "statuses_count": 3021, "translator_type": "none", "url": "https://t.co/GFOkBXzUXC", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1690128357147775061", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1563986267951480836"], "editable_until_msecs": "1661720158000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 647, "bookmarked": true, "created_at": "Sun Aug 28 20:25:58 +0000 2022", "conversation_id_str": "1563986267951480836", "display_text_range": [0, 239], "entities": {"hashtags": [{"indices": [19, 35], "text": "MachineLearning"}, {"indices": [60, 76], "text": "ComputerScience"}, {"indices": [78, 89], "text": "Algorithms"}, {"indices": [164, 172], "text": "BigData"}, {"indices": [173, 185], "text": "DataScience"}, {"indices": [186, 189], "text": "AI"}, {"indices": [190, 197], "text": "Coding"}, {"indices": [198, 205], "text": "Python"}, {"indices": [206, 215], "text": "Database"}], "symbols": [], "timestamps": [], "urls": [{"display_url": "medium.datadriveninvestor.com/90-free-ml-web…", "expanded_url": "https://medium.datadriveninvestor.com/90-free-ml-web-development-data-computer-science-books-for-all-part-1-a83ed888cef5", "url": "https://t.co/TbJbVKiNwc", "indices": [216, 239]}], "user_mentions": [{"id_str": "1229991659812704257", "name": "<PERSON><PERSON>", "screen_name": "NainaChaturved8", "indices": [140, 156]}]}, "favorite_count": 901, "favorited": false, "full_text": "100+ Free Books on #MachineLearning, Web Development, Data, #ComputerScience, #Algorithms, Data Structures, and more — big list compiled by @NainaChaturved8 \n—————\n#BigData #DataScience #AI #Coding #Python #Database\nhttps://t.co/TbJbVKiNwc", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 13, "retweet_count": 291, "retweeted": false, "user_id_str": "534563976", "id_str": "1563986267951480836"}, "twe_private_fields": {"created_at": 1661718358000, "updated_at": 1748554240582, "media_count": 0}}}]