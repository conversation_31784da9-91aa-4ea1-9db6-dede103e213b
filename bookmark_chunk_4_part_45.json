[{"id": "1742000696013029853", "created_at": "2024-01-02 04:51:45 +03:00", "full_text": "these songs turn 10 this year  https://t.co/iCmQ8uOWlU", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 10009, "retweet_count": 1830, "bookmark_count": 635, "quote_count": 575, "reply_count": 109, "views_count": 526262, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1742000696013029853", "metadata": {"__typename": "Tweet", "rest_id": "1742000696013029853", "core": {"user_results": {"result": {"__typename": "User", "id": "********************", "rest_id": "**********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1315008572204421121/5FFyUXqf_normal.jpg"}, "core": {"created_at": "Sat Jan 04 21:43:04 +0000 2014", "name": "2000s", "screen_name": "PopCulture2000s"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "i don’t think 2000s pop culture can ever be topped✨", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "instagram.com/popculture.200…", "expanded_url": "https://www.instagram.com/popculture.2000s?igsh=cWhvc3dreDJvbXF3", "url": "https://t.co/hzw355bsnA", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 13783, "followers_count": 2268627, "friends_count": 789, "has_custom_timelines": true, "is_translator": false, "listed_count": 2780, "media_count": 9133, "normal_followers_count": 2268627, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/**********/**********", "profile_interstitial_type": "", "statuses_count": 14709, "translator_type": "regular", "url": "https://t.co/hzw355bsnA", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "personal account!"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1742000696013029853"], "editable_until_msecs": "*************", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "526262", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 635, "bookmarked": true, "created_at": "<PERSON><PERSON> Jan 02 01:51:45 +0000 2024", "conversation_id_str": "1742000696013029853", "display_text_range": [0, 54], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/pcmedia2000s/s…", "expanded_url": "https://x.com/pcmedia2000s/status/1741999887892295901/video/1", "url": "https://t.co/iCmQ8uOWlU", "indices": [31, 54]}], "user_mentions": []}, "favorite_count": 10009, "favorited": false, "full_text": "these songs turn 10 this year  https://t.co/iCmQ8uOWlU", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 575, "reply_count": 109, "retweet_count": 1830, "retweeted": false, "user_id_str": "**********", "id_str": "1742000696013029853"}, "twe_private_fields": {"created_at": 1704160305000, "updated_at": 1748554186108, "media_count": 0}}}, {"id": "1742643131797864463", "created_at": "2024-01-03 23:24:34 +03:00", "full_text": "Kudos to @oppo/@oneplus for building their own screen mirroring service that supports both Google Cast and Miracast.", "media": [{"type": "photo", "url": "https://t.co/msNQvW1dvb", "thumbnail": "https://pbs.twimg.com/media/GC8cZZSXMAAMSvM?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GC8cZZSXMAAMSvM?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": "1742639555147153805", "favorite_count": 249, "retweet_count": 5, "bookmark_count": 9, "quote_count": 2, "reply_count": 5, "views_count": 15750, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1742643131797864463", "metadata": {"__typename": "Tweet", "rest_id": "1742643131797864463", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MDgzOTI3NTE2Mjg4NzM3MzY=", "rest_id": "808392751628873736", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1795585926304784384/CBaEtw09_normal.jpg"}, "core": {"created_at": "Mon Dec 12 19:27:06 +0000 2016", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The best source for Android OS news. \n\nEditor-at-large @AndroidAuth, Co-host @AndroidFaithful.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expanded_url": "https://linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://t.co/1I4ZpxSLyG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16539, "followers_count": 68160, "friends_count": 523, "has_custom_timelines": true, "is_translator": false, "listed_count": 464, "media_count": 5574, "normal_followers_count": 68160, "pinned_tweet_ids_str": ["1840914465404923985"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/808392751628873736/1716936020", "profile_interstitial_type": "", "statuses_count": 27411, "translator_type": "none", "url": "https://t.co/1I4ZpxSLyG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1469919091762864128", "professional_type": "Creator", "category": [{"id": 955, "name": "Journalist", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "patreon_handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1742643131797864463"], "editable_until_msecs": "1704317074000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "15750", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": false, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3NDI2NDMxMzE3MDE0MzY0MTc=", "text": "Kudos to @oppo/@oneplus for building their own screen mirroring service that supports both Google Cast and Miracast.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "960653964", "name": "OPPO", "screen_name": "oppo", "indices": [9, 14]}, {"id_str": "2196922086", "name": "OnePlus", "screen_name": "oneplus", "indices": [15, 23]}]}, "richtext": {"richtext_tags": [{"from_index": 103, "to_index": 106, "richtext_types": ["Italic"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "quoted_status_result": {"result": {"__typename": "Tweet", "rest_id": "1742639555147153805", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MDgzOTI3NTE2Mjg4NzM3MzY=", "rest_id": "808392751628873736", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1795585926304784384/CBaEtw09_normal.jpg"}, "core": {"created_at": "Mon Dec 12 19:27:06 +0000 2016", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The best source for Android OS news. \n\nEditor-at-large @AndroidAuth, Co-host @AndroidFaithful.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expanded_url": "https://linktr.ee/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://t.co/1I4ZpxSLyG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16539, "followers_count": 68160, "friends_count": 523, "has_custom_timelines": true, "is_translator": false, "listed_count": 464, "media_count": 5574, "normal_followers_count": 68160, "pinned_tweet_ids_str": ["1840914465404923985"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/808392751628873736/1716936020", "profile_interstitial_type": "", "statuses_count": 27411, "translator_type": "none", "url": "https://t.co/1I4ZpxSLyG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1469919091762864128", "professional_type": "Creator", "category": [{"id": 955, "name": "Journalist", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "patreon_handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1742639555147153805"], "editable_until_msecs": "1704316221000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "35090", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3NDI2Mzk1NTUwMjU1MDIyMDk=", "text": "I wish smartphone and TV vendors would play nice and just...support both Google Cast and Miracast.\n\nWe had a New Year's party at my folks' place, so I wanted to cast a simple countdown page we could all count down with. \n\nI was using a Samsung Galaxy Z Fold 5 and the TV I wanted to mirror to was a Sony TV running Android TV. \n\nGalaxy devices support Samsung's Smart View feature, which only works with Miracast-enabled devices. Sony's Android TV devices don't support Miracast, but they do support Google Cast. Samsung removed/hid the \"Cast\" page provided by Google Play Services, plus Smart View isn't compatible (yet) with Google Cast, so there's no built-in Google Cast mirroring service on Samsung devices. \n\nFortunately, the Google Home app still has a \"cast my screen\" button that you can use to mirror your Galaxy device's screen to a Cast-enabled device. \n\nSmart View is preparing to add Google Cast support, but you have to access a hidden developer option menu to enable it. That developer option menu, however, is no longer accessible in version 8.2.22.24 of the app. Hopefully Samsung rolls this feature out soon!", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 85, "to_index": 88, "richtext_types": ["Italic"]}]}, "media": {"inline_media": [{"media_id": "1742638096317906944", "index": 866}]}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 18, "bookmarked": false, "created_at": "Wed Jan 03 20:10:21 +0000 2024", "conversation_id_str": "1742639555147153805", "display_text_range": [0, 279], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/4wxy4bzZ7R", "expanded_url": "https://x.com/MishaalRahman/status/1742639555147153805/photo/1", "id_str": "1742638096317906944", "indices": [280, 303], "media_key": "3_1742638096317906944", "media_url_https": "https://pbs.twimg.com/media/GC8YHZ8XgAAVSf6.jpg", "type": "photo", "url": "https://t.co/4wxy4bzZ7R", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1363, "w": 2048, "resize": "fit"}, "medium": {"h": 798, "w": 1200, "resize": "fit"}, "small": {"h": 452, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2555, "width": 3840, "focus_rects": [{"x": 0, "y": 405, "w": 3840, "h": 2150}, {"x": 643, "y": 0, "w": 2555, "h": 2555}, {"x": 800, "y": 0, "w": 2241, "h": 2555}, {"x": 1281, "y": 0, "w": 1278, "h": 2555}, {"x": 0, "y": 0, "w": 3840, "h": 2555}]}, "media_results": {"result": {"media_key": "3_1742638096317906944"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/4wxy4bzZ7R", "expanded_url": "https://x.com/MishaalRahman/status/1742639555147153805/photo/1", "id_str": "1742638096317906944", "indices": [280, 303], "media_key": "3_1742638096317906944", "media_url_https": "https://pbs.twimg.com/media/GC8YHZ8XgAAVSf6.jpg", "type": "photo", "url": "https://t.co/4wxy4bzZ7R", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1363, "w": 2048, "resize": "fit"}, "medium": {"h": 798, "w": 1200, "resize": "fit"}, "small": {"h": 452, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2555, "width": 3840, "focus_rects": [{"x": 0, "y": 405, "w": 3840, "h": 2150}, {"x": 643, "y": 0, "w": 2555, "h": 2555}, {"x": 800, "y": 0, "w": 2241, "h": 2555}, {"x": 1281, "y": 0, "w": 1278, "h": 2555}, {"x": 0, "y": 0, "w": 3840, "h": 2555}]}, "media_results": {"result": {"media_key": "3_1742638096317906944"}}}]}, "favorite_count": 297, "favorited": false, "full_text": "I wish smartphone and TV vendors would play nice and just...support both Google Cast and Miracast.\n\nWe had a New Year's party at my folks' place, so I wanted to cast a simple countdown page we could all count down with. \n\nI was using a Samsung Galaxy Z Fold 5 and the TV I wanted https://t.co/4wxy4bzZ7R", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 13, "retweet_count": 5, "retweeted": false, "user_id_str": "808392751628873736", "id_str": "1742639555147153805"}}}, "legacy": {"bookmark_count": 9, "bookmarked": true, "created_at": "Wed Jan 03 20:24:34 +0000 2024", "conversation_id_str": "1742643131797864463", "display_text_range": [0, 116], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/msNQvW1dvb", "expanded_url": "https://x.com/MishaalRahman/status/1742643131797864463/photo/1", "id_str": "1742642803425882112", "indices": [117, 140], "media_key": "3_1742642803425882112", "media_url_https": "https://pbs.twimg.com/media/GC8cZZSXMAAMSvM.jpg", "type": "photo", "url": "https://t.co/msNQvW1dvb", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1904, "resize": "fit"}, "medium": {"h": 1200, "w": 1115, "resize": "fit"}, "small": {"h": 680, "w": 632, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2440, "width": 2268, "focus_rects": [{"x": 0, "y": 646, "w": 2268, "h": 1270}, {"x": 0, "y": 147, "w": 2268, "h": 2268}, {"x": 0, "y": 0, "w": 2140, "h": 2440}, {"x": 61, "y": 0, "w": 1220, "h": 2440}, {"x": 0, "y": 0, "w": 2268, "h": 2440}]}, "media_results": {"result": {"media_key": "3_1742642803425882112"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "960653964", "name": "OPPO", "screen_name": "oppo", "indices": [9, 14]}, {"id_str": "2196922086", "name": "OnePlus", "screen_name": "oneplus", "indices": [15, 23]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/msNQvW1dvb", "expanded_url": "https://x.com/MishaalRahman/status/1742643131797864463/photo/1", "id_str": "1742642803425882112", "indices": [117, 140], "media_key": "3_1742642803425882112", "media_url_https": "https://pbs.twimg.com/media/GC8cZZSXMAAMSvM.jpg", "type": "photo", "url": "https://t.co/msNQvW1dvb", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1904, "resize": "fit"}, "medium": {"h": 1200, "w": 1115, "resize": "fit"}, "small": {"h": 680, "w": 632, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2440, "width": 2268, "focus_rects": [{"x": 0, "y": 646, "w": 2268, "h": 1270}, {"x": 0, "y": 147, "w": 2268, "h": 2268}, {"x": 0, "y": 0, "w": 2140, "h": 2440}, {"x": 61, "y": 0, "w": 1220, "h": 2440}, {"x": 0, "y": 0, "w": 2268, "h": 2440}]}, "media_results": {"result": {"media_key": "3_1742642803425882112"}}}]}, "favorite_count": 249, "favorited": false, "full_text": "Kudos to @oppo/@oneplus for building their own screen mirroring service that supports both Google Cast and Miracast. https://t.co/msNQvW1dvb", "is_quote_status": true, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "quoted_status_id_str": "1742639555147153805", "quoted_status_permalink": {"url": "https://t.co/tmT7rt67rB", "expanded": "https://twitter.com/MishaalRahman/status/1742639555147153805", "display": "x.com/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/…"}, "reply_count": 5, "retweet_count": 5, "retweeted": false, "user_id_str": "808392751628873736", "id_str": "1742643131797864463"}, "twe_private_fields": {"created_at": 1704313474000, "updated_at": 1748554178748, "media_count": 1}}}]