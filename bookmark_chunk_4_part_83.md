---
Tweet URL: https://twitter.com/Malcoreio/status/1836825756170866872
Tweet ID: 1836825756170866872
Author: @Malcoreio
---

Today we are releasing our FREE: "Introduction to x86 Assembly" educational course!

This FREE course covers registers, the stack, writing code, and compiling that code!

With a detailed breakdown on registers, sections, and the stack this FREE course is perfect!

Links👇 [https://x.com/Malcoreio/status/1836825756170866872/photo/1](https://t.co/ND26QIxhtl)

---
Media: Image - https://pbs.twimg.com/media/GX20c-xWgAAiTF-.png
Category: Technology / Programming / Assembly / x86 / Tutorial / Course

---

---
Tweet URL: https://twitter.com/ariG23498/status/1839000148715901188
Tweet ID: 1839000148715901188
Author: @ariG23498
---

My favourite bit about the Llama 3.2 release is the small models. Both 1B and 3B despite being quite small are very capable.

While there are benchmarks that prove my point, I took them on a spin for something totally different, assisted decoding.

[1/N] 🧵⤵️ [https://x.com/ariG23498/status/1839000148715901188/photo/1](https://t.co/U8PU22dO5l)

---
Media: Image - https://pbs.twimg.com/media/GYVwy36WoAA1J7M.png
Category: Technology / AI / Machine Learning / Llama / Thread
