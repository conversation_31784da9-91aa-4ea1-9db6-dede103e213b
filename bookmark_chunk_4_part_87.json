[{"id": "1839688684422983902", "created_at": "2024-09-27 18:28:56 +03:00", "full_text": "Check it out here:\n\nhttps://t.co/bhCgcjSu4W", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1839688569901719698", "retweeted_status": null, "quoted_status": null, "favorite_count": 33, "retweet_count": 6, "bookmark_count": 29, "quote_count": 0, "reply_count": 0, "views_count": 1869, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1839688684422983902", "metadata": {"__typename": "Tweet", "rest_id": "1839688684422983902", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NzQ5ODc1MTI4NTAxMjg4OTc=", "rest_id": "874987512850128897", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1509901130670747666/JFlrSzB4_normal.jpg"}, "core": {"created_at": "Wed Jun 14 13:50:54 +0000 2017", "name": "<PERSON><PERSON><PERSON><PERSON> (VB) Srivastav", "screen_name": "reach_vb"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "chief get-shit-done officer @huggingface | F1 fan | Here for @at_sofdog’s wisdom | *opinions my own", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "vaibhavs10.github.io", "expanded_url": "http://vaibhavs10.github.io", "url": "https://t.co/83qEAS8N0w", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 14300, "followers_count": 30767, "friends_count": 314, "has_custom_timelines": true, "is_translator": false, "listed_count": 667, "media_count": 1544, "normal_followers_count": 30767, "pinned_tweet_ids_str": ["1749130868533207403"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/874987512850128897/1651638851", "profile_interstitial_type": "", "statuses_count": 8839, "translator_type": "none", "url": "https://t.co/83qEAS8N0w", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "nvidia-smi"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1465412846041321480", "professional_type": "Creator", "category": [{"id": 1055, "name": "Software developer/Programmer/Software engineer", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "**********************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/bhCgcjSu4W", "legacy": {"binding_values": [{"key": "description", "value": {"string_value": "Contribute to Vaibhavs10/gpu-poor-llm-notebooks development by creating an account on GitHub.", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "********", "path": []}}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "Contribute to Vaibhavs10/gpu-poor-llm-notebooks development by creating an account on GitHub.", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "Contribute to Vaibhavs10/gpu-poor-llm-notebooks development by creating an account on GitHub.", "type": "STRING"}}, {"key": "title", "value": {"string_value": "gpu-poor-llm-notebooks/llama3_2_3b_colab.ipynb at main · Vaibhavs10/gpu-poor-llm-notebooks", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/bhCgcjSu4W", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/bhCgcjSu4W", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620091, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17801, "media_count": 2646, "normal_followers_count": 2620091, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/********/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1839688684422983902"], "editable_until_msecs": "1727454536000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1869", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 29, "bookmarked": true, "created_at": "Fri Sep 27 15:28:56 +0000 2024", "conversation_id_str": "1839688569901719698", "display_text_range": [0, 43], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/Vaibhavs10/gpu…", "expanded_url": "https://github.com/Vaibhavs10/gpu-poor-llm-notebooks/blob/main/llama3_2_3b_colab.ipynb", "url": "https://t.co/bhCgcjSu4W", "indices": [20, 43]}], "user_mentions": []}, "favorite_count": 33, "favorited": false, "full_text": "Check it out here:\n\nhttps://t.co/bhCgcjSu4W", "in_reply_to_screen_name": "reach_vb", "in_reply_to_status_id_str": "1839688569901719698", "in_reply_to_user_id_str": "874987512850128897", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 0, "retweet_count": 6, "retweeted": false, "user_id_str": "874987512850128897", "id_str": "1839688684422983902"}, "twe_private_fields": {"created_at": 1727450936000, "updated_at": 1748554144882, "media_count": 0}}}, {"id": "1839881289312149549", "created_at": "2024-09-28 07:14:17 +03:00", "full_text": "https://t.co/2Gn3fdtE39", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1032, "retweet_count": 75, "bookmark_count": 969, "quote_count": 9, "reply_count": 44, "views_count": 151440, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1839881289312149549", "metadata": {"__typename": "Tweet", "rest_id": "1839881289312149549", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMDY5NTE1Mg==", "rest_id": "10695152", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1894074698708680704/RkWx2Fr8_normal.jpg"}, "core": {"created_at": "Thu Nov 29 02:19:16 +0000 2007", "name": "<PERSON>", "screen_name": "<PERSON><PERSON>e"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Software performance expert. Ranked in the top 2% of scientists globally (Stanford/Elsevier 2024) and among GitHub's top 1000 developers.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "lemire.me/en/", "expanded_url": "http://lemire.me/en/", "url": "https://t.co/JvW7S51diM", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 25926, "followers_count": 27406, "friends_count": 1800, "has_custom_timelines": true, "is_translator": false, "listed_count": 0, "media_count": 2683, "normal_followers_count": 27406, "pinned_tweet_ids_str": ["1927479846499708934"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/10695152/1674590192", "profile_interstitial_type": "", "statuses_count": 31624, "translator_type": "none", "url": "https://t.co/JvW7S51diM", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Montreal, Quebec"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1518630277253378052", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1839881289312149549"], "editable_until_msecs": "1727500457000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "151440", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "article": {"article_results": {"result": {"rest_id": "1839879712706842624", "id": "QXJ0aWNsZUVudGl0eToxODM5ODc5NzEyNzA2ODQyNjI0", "title": "It is never too late to write your own C/C++ command-line utilities", "preview_text": "Recently, I received an email from an engineer at a prominent company who shared how he managed to save his employer tens of thousands of dollars annually by developing a custom command-line utility", "cover_media": {"id": "QXBpTWVkaWE6DAAFCgABGYiRDt/WAAAKAAIAAAAAAKMx8AAA", "media_key": "3_1839879940851761152", "media_id": "1839879940851761152", "media_info": {"__typename": "ApiImage", "original_img_height": 410, "original_img_width": 1024, "original_img_url": "https://pbs.twimg.com/media/GYiRDt_WAAAGXNM.jpg", "color_info": {"palette": [{"percentage": 68.77, "rgb": {"blue": 50, "green": 36, "red": 18}}, {"percentage": 9.91, "rgb": {"blue": 223, "green": 216, "red": 123}}, {"percentage": 7.46, "rgb": {"blue": 141, "green": 125, "red": 16}}, {"percentage": 6.05, "rgb": {"blue": 136, "green": 116, "red": 67}}, {"percentage": 3.16, "rgb": {"blue": 45, "green": 65, "red": 25}}]}}}, "lifecycle_state": {"modified_at_secs": 1727554694}, "metadata": {"first_published_at_secs": 1727496857}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 969, "bookmarked": true, "created_at": "Sat Sep 28 04:14:17 +0000 2024", "conversation_id_str": "1839881289312149549", "display_text_range": [0, 23], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/i/article/1839…", "expanded_url": "http://x.com/i/article/1839879712706842624", "url": "https://t.co/2Gn3fdtE39", "indices": [0, 23]}], "user_mentions": []}, "favorite_count": 1032, "favorited": false, "full_text": "https://t.co/2Gn3fdtE39", "is_quote_status": false, "lang": "zxx", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 9, "reply_count": 44, "retweet_count": 75, "retweeted": false, "user_id_str": "10695152", "id_str": "1839881289312149549"}, "twe_private_fields": {"created_at": 1727496857000, "updated_at": 1748554144882, "media_count": 0}}}]