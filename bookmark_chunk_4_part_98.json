[{"id": "1845107517678027199", "created_at": "2024-10-12 17:21:27 +03:00", "full_text": "> Linear Algebra by Prof <PERSON>\n> Probability Theory Prof <PERSON>\n> Information Theory by <PERSON>\n> Optimization theory by Stanford University \n> Machine learning by <PERSON>(Stanford course)\n\nThis is all you need to master ML. Coursework + assignments + course projects and you’ll be better than most people at ML.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3958, "retweet_count": 477, "bookmark_count": 7697, "quote_count": 16, "reply_count": 34, "views_count": 259135, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1845107517678027199", "metadata": {"__typename": "Tweet", "rest_id": "1845107517678027199", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3NzI1MTQwMTUzMTg3OTQyNDA=", "rest_id": "772514015318794240", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1755986144737828864/wIVGha3H_normal.jpg"}, "core": {"created_at": "Sun Sep 04 19:17:48 +0000 2016", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "drummatick"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Building @kodomamo_JP \n\nPresently focusing on LLMs and AI Agents\n\nML, Backend and Flutter | Prev - Yahoo, Nokia, Rapyuta, and couple startups | @iitdelhi’20", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/saurabh-kum…", "expanded_url": "https://www.linkedin.com/in/saurab<PERSON>-kumar-82417a137/", "url": "https://t.co/4E37k9tDiK", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 42380, "followers_count": 18565, "friends_count": 270, "has_custom_timelines": false, "is_translator": false, "listed_count": 74, "media_count": 1924, "normal_followers_count": 18565, "pinned_tweet_ids_str": ["1728438723275374760"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/772514015318794240/1737021941", "profile_interstitial_type": "", "statuses_count": 16316, "translator_type": "none", "url": "https://t.co/4E37k9tDiK", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Tokyo"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1643040129072922625", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1845107517678027199"], "editable_until_msecs": "1728746487000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "259135", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NDUxMDc1MTc2MTUxMTIxOTU=", "text": "> Linear Algebra by Prof <PERSON>\n> Probability Theory Prof <PERSON>\n> Information Theory by <PERSON>\n> Optimization theory by Stanford University \n> Machine learning by <PERSON>(Stanford course)\n\nThis is all you need to master ML. Coursework + assignments + course projects and you’ll be better than most people at ML.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 7697, "bookmarked": true, "created_at": "Sat Oct 12 14:21:27 +0000 2024", "conversation_id_str": "1845107517678027199", "display_text_range": [0, 291], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 3958, "favorited": false, "full_text": "&gt; Linear Algebra by Prof <PERSON>t; Probability Theory Prof <PERSON>gt; Information Theory by <PERSON>\n&<PERSON>t; Optimization theory by Stanford University \n&gt; Machine learning by <PERSON>(Stanford course)\n\nThis is all you need to master ML. Coursework + assignments +", "is_quote_status": false, "lang": "en", "quote_count": 16, "reply_count": 34, "retweet_count": 477, "retweeted": false, "user_id_str": "772514015318794240", "id_str": "1845107517678027199"}, "twe_private_fields": {"created_at": 1728742887000, "updated_at": 1748554137299, "media_count": 0}}}]