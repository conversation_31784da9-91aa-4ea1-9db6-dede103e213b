[{"id": "1558845973018906624", "created_at": "2022-08-14 19:00:16 +03:00", "full_text": "2022 Frontend security in 1 tweet:\n\n1. Use HTTPs\n2. Server render\n3. Store JWT/token in 'secure', 'samesite', 'httponly' cookie (prevent XSS).\n4. Declare content security policy (prevent XSS)\n5. Regen cookies when user auths (prevent session fixation)\n6. Set short cookie lifetime", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5571, "retweet_count": 908, "bookmark_count": 3472, "quote_count": 15, "reply_count": 74, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1558845973018906624", "metadata": {"__typename": "Tweet", "rest_id": "1558845973018906624", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxOTI2ODMyMQ==", "rest_id": "19268321", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1869130199045906432/PUA1SYIL_normal.jpg"}, "core": {"created_at": "Wed Jan 21 00:57:19 +0000 2009", "name": "Cory <PERSON>", "screen_name": "housecor"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "I talk code. \n\nCourses: https://t.co/D5emROQa4J and https://t.co/6L1fD898mh \nYouTube: https://t.co/pWIz4BMXsc\nConsulting: https://t.co/Qfp4Tfp3jf  ⚛️", "entities": {"description": {"urls": [{"display_url": "pluralsight.com", "expanded_url": "http://pluralsight.com", "url": "https://t.co/D5emROQa4J", "indices": [24, 47]}, {"display_url": "dometrain.com", "expanded_url": "http://dometrain.com", "url": "https://t.co/6L1fD898mh", "indices": [52, 75]}, {"display_url": "youtube.com/@housecor", "expanded_url": "http://youtube.com/@housecor", "url": "https://t.co/pWIz4BMXsc", "indices": [86, 109]}, {"display_url": "reactjsconsulting.com", "expanded_url": "http://reactjsconsulting.com", "url": "https://t.co/Qfp4Tfp3jf", "indices": [122, 145]}]}, "url": {"urls": [{"display_url": "reactjsconsulting.com", "expanded_url": "http://www.reactjsconsulting.com", "url": "https://t.co/RdOgJq49Uz", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 21841, "followers_count": 135824, "friends_count": 758, "has_custom_timelines": true, "is_translator": false, "listed_count": 1707, "media_count": 2195, "normal_followers_count": 135824, "pinned_tweet_ids_str": ["1437765667906854915"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/19268321/1727876163", "profile_interstitial_type": "", "statuses_count": 39238, "translator_type": "none", "url": "https://t.co/RdOgJq49Uz", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Kansas City"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1637505487422861313", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1558845973018906624"], "editable_until_msecs": "1660494616000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3472, "bookmarked": true, "created_at": "Sun Aug 14 16:00:16 +0000 2022", "conversation_id_str": "1558845973018906624", "display_text_range": [0, 280], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 5571, "favorited": false, "full_text": "2022 Frontend security in 1 tweet:\n\n1. Use HTTPs\n2. Server render\n3. Store JWT/token in 'secure', 'samesite', 'httponly' cookie (prevent XSS).\n4. Declare content security policy (prevent XSS)\n5. Regen cookies when user auths (prevent session fixation)\n6. Set short cookie lifetime", "is_quote_status": false, "lang": "en", "quote_count": 15, "reply_count": 74, "retweet_count": 908, "retweeted": false, "user_id_str": "19268321", "id_str": "1558845973018906624"}, "twe_private_fields": {"created_at": 1660492816000, "updated_at": 1748554247988, "media_count": 0}}}, {"id": "1558943259073839107", "created_at": "2022-08-15 01:26:51 +03:00", "full_text": "#Python Cheatsheet: 14 #coding interview questions — from https://t.co/kHEzYiSYYq\n————\n#100DaysOfCode #BigData #DataScience #AI #MachineLearning #DataScientist #job https://t.co/DviQImBPy8", "media": [{"type": "photo", "url": "https://t.co/DviQImBPy8", "thumbnail": "https://pbs.twimg.com/media/FaJ6oNXWAAEgGMZ?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FaJ6oNXWAAEgGMZ?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1335, "retweet_count": 319, "bookmark_count": 812, "quote_count": 5, "reply_count": 19, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1558943259073839107", "metadata": {"__typename": "Tweet", "rest_id": "1558943259073839107", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1MzQ1NjM5NzY=", "rest_id": "534563976", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1112733580948635648/s-8d1avb_normal.jpg"}, "core": {"created_at": "Fri Mar 23 16:35:17 +0000 2012", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Advisor to startups. Freelancer. Founder of @LeadershipData. Global Speaker. Top influencer #BigData #DataScience #AI #IoT #ML #B2B. PhD Astrophysics @Caltech", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/kirkdborne", "expanded_url": "http://www.linkedin.com/in/kirkdborne", "url": "https://t.co/g46xALu4Eu", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 238745, "followers_count": 463503, "friends_count": 5532, "has_custom_timelines": true, "is_translator": false, "listed_count": 10132, "media_count": 94041, "normal_followers_count": 463503, "pinned_tweet_ids_str": ["1913816024513732984"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/534563976/1706648943", "profile_interstitial_type": "", "statuses_count": 184820, "translator_type": "none", "url": "https://t.co/g46xALu4Eu", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Maryland, USA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1494394757388378113", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************", "venmo_handle": "kirk-borne"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1558943259073839107"], "editable_until_msecs": "1660517811000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 812, "bookmarked": true, "created_at": "Sun Aug 14 22:26:51 +0000 2022", "conversation_id_str": "1558943259073839107", "display_text_range": [0, 164], "entities": {"hashtags": [{"indices": [0, 7], "text": "Python"}, {"indices": [23, 30], "text": "coding"}, {"indices": [87, 101], "text": "100DaysOfCode"}, {"indices": [102, 110], "text": "BigData"}, {"indices": [111, 123], "text": "DataScience"}, {"indices": [124, 127], "text": "AI"}, {"indices": [128, 144], "text": "MachineLearning"}, {"indices": [145, 159], "text": "DataScientist"}, {"indices": [160, 164], "text": "job"}], "media": [{"display_url": "pic.x.com/DviQImBPy8", "expanded_url": "https://x.com/KirkDBorne/status/1558943259073839107/photo/1", "id_str": "1558943252224540673", "indices": [165, 188], "media_key": "3_1558943252224540673", "media_url_https": "https://pbs.twimg.com/media/FaJ6oNXWAAEgGMZ.jpg", "type": "photo", "url": "https://t.co/DviQImBPy8", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1390, "w": 1074, "resize": "fit"}, "medium": {"h": 1200, "w": 927, "resize": "fit"}, "small": {"h": 680, "w": 525, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1390, "width": 1074, "focus_rects": [{"x": 0, "y": 0, "w": 1074, "h": 601}, {"x": 0, "y": 0, "w": 1074, "h": 1074}, {"x": 0, "y": 0, "w": 1074, "h": 1224}, {"x": 0, "y": 0, "w": 695, "h": 1390}, {"x": 0, "y": 0, "w": 1074, "h": 1390}]}, "media_results": {"result": {"media_key": "3_1558943252224540673"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "bit.ly/free-python-co…", "expanded_url": "http://bit.ly/free-python-course", "url": "https://t.co/kHEzYiSYYq", "indices": [58, 81]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/DviQImBPy8", "expanded_url": "https://x.com/KirkDBorne/status/1558943259073839107/photo/1", "id_str": "1558943252224540673", "indices": [165, 188], "media_key": "3_1558943252224540673", "media_url_https": "https://pbs.twimg.com/media/FaJ6oNXWAAEgGMZ.jpg", "type": "photo", "url": "https://t.co/DviQImBPy8", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1390, "w": 1074, "resize": "fit"}, "medium": {"h": 1200, "w": 927, "resize": "fit"}, "small": {"h": 680, "w": 525, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1390, "width": 1074, "focus_rects": [{"x": 0, "y": 0, "w": 1074, "h": 601}, {"x": 0, "y": 0, "w": 1074, "h": 1074}, {"x": 0, "y": 0, "w": 1074, "h": 1224}, {"x": 0, "y": 0, "w": 695, "h": 1390}, {"x": 0, "y": 0, "w": 1074, "h": 1390}]}, "media_results": {"result": {"media_key": "3_1558943252224540673"}}}]}, "favorite_count": 1335, "favorited": false, "full_text": "#Python Cheatsheet: 14 #coding interview questions — from https://t.co/kHEzYiSYYq\n————\n#100DaysOfCode #BigData #DataScience #AI #MachineLearning #DataScientist #job https://t.co/DviQImBPy8", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 5, "reply_count": 19, "retweet_count": 319, "retweeted": false, "user_id_str": "534563976", "id_str": "1558943259073839107"}, "twe_private_fields": {"created_at": 1660516011000, "updated_at": 1748554247988, "media_count": 1}}}]