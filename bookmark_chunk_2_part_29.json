[{"id": "1451225095116972036", "created_at": "2021-10-21 19:33:19 +03:00", "full_text": "Name an animated movie everyone should watch at least once… 💭", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4056, "retweet_count": 378, "bookmark_count": 534, "quote_count": 1151, "reply_count": 2626, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1451225095116972036", "metadata": {"__typename": "Tweet", "rest_id": "1451225095116972036", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NTAyMzQyMw==", "rest_id": "95023423", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1603579996064227328/TAcR_zuP_normal.jpg"}, "core": {"created_at": "Sun Dec 06 16:07:01 +0000 2009", "name": "UberFacts", "screen_name": "UberFacts"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The most unimportant things you’ll never need to know 💥", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "instagram.com/uberfacts/", "expanded_url": "https://www.instagram.com/uberfacts/", "url": "https://t.co/uVFxvFrYJP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1162, "followers_count": 12907351, "friends_count": 1, "has_custom_timelines": true, "is_translator": false, "listed_count": 14680, "media_count": 27707, "normal_followers_count": 12907351, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/95023423/1671158239", "profile_interstitial_type": "", "statuses_count": 234950, "translator_type": "regular", "url": "https://t.co/uVFxvFrYJP", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "🌎"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1460999005266059264", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1451225095116972036"], "editable_until_msecs": "1634835799791", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 534, "bookmarked": true, "created_at": "Thu Oct 21 16:33:19 +0000 2021", "conversation_id_str": "1451225095116972036", "display_text_range": [0, 61], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 4056, "favorited": false, "full_text": "Name an animated movie everyone should watch at least once… 💭", "is_quote_status": false, "lang": "en", "quote_count": 1151, "reply_count": 2626, "retweet_count": 378, "retweeted": false, "user_id_str": "95023423", "id_str": "1451225095116972036"}, "twe_private_fields": {"created_at": ***********00, "updated_at": 1748554391966, "media_count": 0}}}, {"id": "1452864098769666052", "created_at": "2021-10-26 08:06:08 +03:00", "full_text": "1. Read and write more.\n2. Don't hesitate to admit when you're wrong.\n3. Be comfortable changing your opinion.\n4. Find a mentor.\n5. Stay teachable.\n6. Make mistakes and learn.\n7. Don't get offended easily.\n8. Ask questions.\n9. Spend time with nature.\n10. Stay humble. https://t.co/yOKaDxla98", "media": [{"type": "photo", "url": "https://t.co/yOKaDxla98", "thumbnail": "https://pbs.twimg.com/media/FCmcMqxVEAMO3YT?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FCmcMqxVEAMO3YT?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 16230, "retweet_count": 4443, "bookmark_count": 1238, "quote_count": 228, "reply_count": 80, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1452864098769666052", "metadata": {"__typename": "Tweet", "rest_id": "1452864098769666052", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MjM1MTg4OTQxODI4NDY0NjQ=", "rest_id": "823518894182846464", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1511990640753618949/rZMGSR26_normal.jpg"}, "core": {"created_at": "Mon Jan 23 13:12:59 +0000 2017", "name": "Prof<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "A universe of atoms, an atom in the universe. Tribute to the great explainer. Tweets about Science and Wisdom. Portrait by <PERSON><PERSON>.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 6171, "followers_count": 1349822, "friends_count": 0, "has_custom_timelines": true, "is_translator": false, "listed_count": 6469, "media_count": 1290, "normal_followers_count": 1349822, "pinned_tweet_ids_str": ["1690017913644675073"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/823518894182846464/1563454062", "profile_interstitial_type": "", "statuses_count": 2879, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1455731659798503432", "professional_type": "Creator", "category": [{"id": 144, "name": "Education", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1452864098769666052"], "editable_until_msecs": "1635226568713", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1238, "bookmarked": true, "created_at": "<PERSON>e Oct 26 05:06:08 +0000 2021", "conversation_id_str": "1452864098769666052", "display_text_range": [0, 267], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/yOKaDxla98", "expanded_url": "https://x.com/ProfFeynman/status/1452864098769666052/photo/1", "id_str": "1452864096257249283", "indices": [268, 291], "media_key": "3_1452864096257249283", "media_url_https": "https://pbs.twimg.com/media/FCmcMqxVEAMO3YT.jpg", "type": "photo", "url": "https://t.co/yOKaDxla98", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}, "medium": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}, "small": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}, "orig": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}}, "sizes": {"large": {"h": 562, "w": 450, "resize": "fit"}, "medium": {"h": 562, "w": 450, "resize": "fit"}, "small": {"h": 562, "w": 450, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 562, "width": 450, "focus_rects": [{"x": 0, "y": 56, "w": 450, "h": 252}, {"x": 0, "y": 0, "w": 450, "h": 450}, {"x": 0, "y": 0, "w": 450, "h": 513}, {"x": 98, "y": 0, "w": 281, "h": 562}, {"x": 0, "y": 0, "w": 450, "h": 562}]}, "media_results": {"result": {"media_key": "3_1452864096257249283"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/yOKaDxla98", "expanded_url": "https://x.com/ProfFeynman/status/1452864098769666052/photo/1", "id_str": "1452864096257249283", "indices": [268, 291], "media_key": "3_1452864096257249283", "media_url_https": "https://pbs.twimg.com/media/FCmcMqxVEAMO3YT.jpg", "type": "photo", "url": "https://t.co/yOKaDxla98", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}, "medium": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}, "small": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}, "orig": {"faces": [{"x": 162, "y": 118, "h": 111, "w": 111}]}}, "sizes": {"large": {"h": 562, "w": 450, "resize": "fit"}, "medium": {"h": 562, "w": 450, "resize": "fit"}, "small": {"h": 562, "w": 450, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 562, "width": 450, "focus_rects": [{"x": 0, "y": 56, "w": 450, "h": 252}, {"x": 0, "y": 0, "w": 450, "h": 450}, {"x": 0, "y": 0, "w": 450, "h": 513}, {"x": 98, "y": 0, "w": 281, "h": 562}, {"x": 0, "y": 0, "w": 450, "h": 562}]}, "media_results": {"result": {"media_key": "3_1452864096257249283"}}}]}, "favorite_count": 16230, "favorited": false, "full_text": "1. Read and write more.\n2. Don't hesitate to admit when you're wrong.\n3. Be comfortable changing your opinion.\n4. Find a mentor.\n5. Stay teachable.\n6. Make mistakes and learn.\n7. Don't get offended easily.\n8. Ask questions.\n9. Spend time with nature.\n10. Stay humble. https://t.co/yOKaDxla98", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 228, "reply_count": 80, "retweet_count": 4443, "retweeted": false, "user_id_str": "823518894182846464", "id_str": "1452864098769666052"}, "twe_private_fields": {"created_at": 1635224768000, "updated_at": 1748554391966, "media_count": 1}}}]