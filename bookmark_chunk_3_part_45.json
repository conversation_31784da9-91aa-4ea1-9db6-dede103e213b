[{"id": "1558946101570461696", "created_at": "2022-08-15 01:38:09 +03:00", "full_text": "[Free 689-page PDF Download eBook]\n\nIntroduction to #Probability for Data Science: https://t.co/L51j3udY5j by @stanley_h_chan \n—————\n#StatisticalLiteracy #Mathematics #AI #BigData #MachineLearning #DataScience #DataScientists #DataLiteracy #Statistics https://t.co/ReN3M6rqvl", "media": [{"type": "photo", "url": "https://t.co/ReN3M6rqvl", "thumbnail": "https://pbs.twimg.com/media/FaJ9Nt1XoAE8fyE?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FaJ9Nt1XoAE8fyE?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 596, "retweet_count": 139, "bookmark_count": 357, "quote_count": 6, "reply_count": 9, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1558946101570461696", "metadata": {"__typename": "Tweet", "rest_id": "1558946101570461696", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1MzQ1NjM5NzY=", "rest_id": "534563976", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1112733580948635648/s-8d1avb_normal.jpg"}, "core": {"created_at": "Fri Mar 23 16:35:17 +0000 2012", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Advisor to startups. Freelancer. Founder of @LeadershipData. Global Speaker. Top influencer #BigData #DataScience #AI #IoT #ML #B2B. PhD Astrophysics @Caltech", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/kirkdborne", "expanded_url": "http://www.linkedin.com/in/kirkdborne", "url": "https://t.co/g46xALu4Eu", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 238745, "followers_count": 463503, "friends_count": 5532, "has_custom_timelines": true, "is_translator": false, "listed_count": 10132, "media_count": 94041, "normal_followers_count": 463503, "pinned_tweet_ids_str": ["1913816024513732984"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/534563976/1706648943", "profile_interstitial_type": "", "statuses_count": 184820, "translator_type": "none", "url": "https://t.co/g46xALu4Eu", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Maryland, USA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1494394757388378113", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************", "venmo_handle": "kirk-borne"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1558946101570461696"], "editable_until_msecs": "1660518489000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 357, "bookmarked": true, "created_at": "Sun Aug 14 22:38:09 +0000 2022", "conversation_id_str": "1558946101570461696", "display_text_range": [0, 251], "entities": {"hashtags": [{"indices": [52, 64], "text": "Probability"}, {"indices": [133, 153], "text": "StatisticalLiteracy"}, {"indices": [154, 166], "text": "Mathematics"}, {"indices": [167, 170], "text": "AI"}, {"indices": [171, 179], "text": "BigData"}, {"indices": [180, 196], "text": "MachineLearning"}, {"indices": [197, 209], "text": "DataScience"}, {"indices": [210, 225], "text": "DataScientists"}, {"indices": [226, 239], "text": "DataLiteracy"}, {"indices": [240, 251], "text": "Statistics"}], "media": [{"display_url": "pic.x.com/ReN3M6rqvl", "expanded_url": "https://x.com/KirkDBorne/status/1558946101570461696/photo/1", "id_str": "1558946095618826241", "indices": [252, 275], "media_key": "3_1558946095618826241", "media_url_https": "https://pbs.twimg.com/media/FaJ9Nt1XoAE8fyE.jpg", "type": "photo", "url": "https://t.co/ReN3M6rqvl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1432, "w": 1074, "resize": "fit"}, "medium": {"h": 1200, "w": 900, "resize": "fit"}, "small": {"h": 680, "w": 510, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1432, "width": 1074, "focus_rects": [{"x": 0, "y": 237, "w": 1074, "h": 601}, {"x": 0, "y": 0, "w": 1074, "h": 1074}, {"x": 0, "y": 0, "w": 1074, "h": 1224}, {"x": 107, "y": 0, "w": 716, "h": 1432}, {"x": 0, "y": 0, "w": 1074, "h": 1432}]}, "media_results": {"result": {"media_key": "3_1558946095618826241"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "probability4datascience.com", "expanded_url": "http://probability4datascience.com", "url": "https://t.co/L51j3udY5j", "indices": [83, 106]}], "user_mentions": [{"id_str": "1373472847750893569", "name": "<PERSON>", "screen_name": "stanley_h_chan", "indices": [110, 125]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/ReN3M6rqvl", "expanded_url": "https://x.com/KirkDBorne/status/1558946101570461696/photo/1", "id_str": "1558946095618826241", "indices": [252, 275], "media_key": "3_1558946095618826241", "media_url_https": "https://pbs.twimg.com/media/FaJ9Nt1XoAE8fyE.jpg", "type": "photo", "url": "https://t.co/ReN3M6rqvl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1432, "w": 1074, "resize": "fit"}, "medium": {"h": 1200, "w": 900, "resize": "fit"}, "small": {"h": 680, "w": 510, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1432, "width": 1074, "focus_rects": [{"x": 0, "y": 237, "w": 1074, "h": 601}, {"x": 0, "y": 0, "w": 1074, "h": 1074}, {"x": 0, "y": 0, "w": 1074, "h": 1224}, {"x": 107, "y": 0, "w": 716, "h": 1432}, {"x": 0, "y": 0, "w": 1074, "h": 1432}]}, "media_results": {"result": {"media_key": "3_1558946095618826241"}}}]}, "favorite_count": 596, "favorited": false, "full_text": "[Free 689-page PDF Download eBook]\n\nIntroduction to #Probability for Data Science: https://t.co/L51j3udY5j by @stanley_h_chan \n—————\n#StatisticalLiteracy #Mathematics #AI #BigData #MachineLearning #DataScience #DataScientists #DataLiteracy #Statistics https://t.co/ReN3M6rqvl", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 9, "retweet_count": 139, "retweeted": false, "user_id_str": "534563976", "id_str": "1558946101570461696"}, "twe_private_fields": {"created_at": 1660516689000, "updated_at": 1748554247988, "media_count": 1}}}, {"id": "1559309339373932544", "created_at": "2022-08-16 01:41:31 +03:00", "full_text": "9 stretches you can do every day. https://t.co/t6QwmBN9rT", "media": [{"type": "video", "url": "https://t.co/t6QwmBN9rT", "thumbnail": "https://pbs.twimg.com/amplify_video_thumb/1558785805434425344/img/E2MXt2NrfIciiUFQ.jpg?name=thumb", "original": "https://video.twimg.com/amplify_video/1558785805434425344/vid/576x1024/wEKUCcsq3swzNtqU.mp4?tag=14"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 167530, "retweet_count": 37462, "bookmark_count": 70880, "quote_count": 945, "reply_count": 960, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1559309339373932544", "metadata": {"__typename": "Tweet", "rest_id": "1559309339373932544", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDQ0NzYwMjI3NTc2ODgxMTU2", "rest_id": "1444760227576881156", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1592626494546796545/YxyPborr_normal.jpg"}, "core": {"created_at": "Sun Oct 03 20:24:36 +0000 2021", "name": "HOW THINGS WORK", "screen_name": "HowThingsWork_"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The Official How Things Work page including Tech, AI & loads more. Also all the best News & Viral content from around the globe.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "onlysearch.com", "expanded_url": "http://onlysearch.com", "url": "https://t.co/gGc0nkirbj", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 22845, "followers_count": 2392431, "friends_count": 7509, "has_custom_timelines": true, "is_translator": false, "listed_count": 7033, "media_count": 7114, "normal_followers_count": 2392431, "pinned_tweet_ids_str": ["1927835147849597192"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1444760227576881156/1727288400", "profile_interstitial_type": "", "statuses_count": 12551, "translator_type": "none", "url": "https://t.co/gGc0nkirbj", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "<EMAIL>"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1590388789792342016", "professional_type": "Business", "category": [{"id": 580, "name": "Media & News Company", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1559309339373932544"], "editable_until_msecs": "1660605091000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 70880, "bookmarked": true, "created_at": "Mon Aug 15 22:41:31 +0000 2022", "conversation_id_str": "1559309339373932544", "display_text_range": [0, 57], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/t6QwmBN9rT", "expanded_url": "https://x.com/EgzersizGunlugu/status/1558786066018082817/video/1", "id_str": "1558785805434425344", "indices": [34, 57], "media_key": "13_1558785805434425344", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1558785805434425344/img/E2MXt2NrfIciiUFQ.jpg", "source_status_id_str": "1558786066018082817", "source_user_id_str": "1446856012921716736", "type": "video", "url": "https://t.co/t6QwmBN9rT", "additional_media_info": {"title": "", "description": "", "embeddable": true, "monetizable": false, "source_user": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDQ2ODU2MDEyOTIxNzE2NzM2", "rest_id": "1446856012921716736", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1446858159180681216/k7ob3VWx_normal.jpg"}, "core": {"created_at": "Sat Oct 09 15:12:23 +0000 2021", "name": "Egzersiz Günlüğü", "screen_name": "EgzersizGunlugu"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Egzersiz videoları", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 5082, "followers_count": 23849, "friends_count": 5, "has_custom_timelines": false, "is_translator": false, "listed_count": 98, "media_count": 512, "normal_followers_count": 23849, "pinned_tweet_ids_str": ["1549979187099869184"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1446856012921716736/1633792850", "profile_interstitial_type": "", "statuses_count": 4640, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1492085493500108804", "professional_type": "Creator", "category": [{"id": 800, "name": "Sports, Fitness & Recreation", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1024, "w": 576, "resize": "fit"}, "medium": {"h": 1024, "w": 576, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1024, "width": 576, "focus_rects": []}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 37600, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1558785805434425344/pl/11GobMiYAXBlm5S6.m3u8?tag=14"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1558785805434425344/vid/320x568/h63NpdgmTh5rl0qV.mp4?tag=14"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1558785805434425344/vid/480x852/pFjRjdg5QFlevw5e.mp4?tag=14"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1558785805434425344/vid/576x1024/wEKUCcsq3swzNtqU.mp4?tag=14"}]}, "media_results": {"result": {"media_key": "13_1558785805434425344"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/t6QwmBN9rT", "expanded_url": "https://x.com/EgzersizGunlugu/status/1558786066018082817/video/1", "id_str": "1558785805434425344", "indices": [34, 57], "media_key": "13_1558785805434425344", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1558785805434425344/img/E2MXt2NrfIciiUFQ.jpg", "source_status_id_str": "1558786066018082817", "source_user_id_str": "1446856012921716736", "type": "video", "url": "https://t.co/t6QwmBN9rT", "additional_media_info": {"title": "", "description": "", "embeddable": true, "monetizable": false, "source_user": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDQ2ODU2MDEyOTIxNzE2NzM2", "rest_id": "1446856012921716736", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1446858159180681216/k7ob3VWx_normal.jpg"}, "core": {"created_at": "Sat Oct 09 15:12:23 +0000 2021", "name": "Egzersiz Günlüğü", "screen_name": "EgzersizGunlugu"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Egzersiz videoları", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 5082, "followers_count": 23849, "friends_count": 5, "has_custom_timelines": false, "is_translator": false, "listed_count": 98, "media_count": 512, "normal_followers_count": 23849, "pinned_tweet_ids_str": ["1549979187099869184"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1446856012921716736/1633792850", "profile_interstitial_type": "", "statuses_count": 4640, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1492085493500108804", "professional_type": "Creator", "category": [{"id": 800, "name": "Sports, Fitness & Recreation", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1024, "w": 576, "resize": "fit"}, "medium": {"h": 1024, "w": 576, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1024, "width": 576, "focus_rects": []}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 37600, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1558785805434425344/pl/11GobMiYAXBlm5S6.m3u8?tag=14"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1558785805434425344/vid/320x568/h63NpdgmTh5rl0qV.mp4?tag=14"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1558785805434425344/vid/480x852/pFjRjdg5QFlevw5e.mp4?tag=14"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1558785805434425344/vid/576x1024/wEKUCcsq3swzNtqU.mp4?tag=14"}]}, "media_results": {"result": {"media_key": "13_1558785805434425344"}}}]}, "favorite_count": 167530, "favorited": false, "full_text": "9 stretches you can do every day. https://t.co/t6QwmBN9rT", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 945, "reply_count": 960, "retweet_count": 37462, "retweeted": false, "user_id_str": "1444760227576881156", "id_str": "1559309339373932544"}, "twe_private_fields": {"created_at": 1660603291000, "updated_at": 1748554247988, "media_count": 1}}}]