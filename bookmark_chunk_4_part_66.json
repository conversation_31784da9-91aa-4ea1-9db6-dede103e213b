[{"id": "1799853027261874570", "created_at": "2024-06-09 20:16:16 +03:00", "full_text": "Want to paste your entire codebase to <PERSON>tg<PERSON>, <PERSON> or your local LLM of choice including folder structure? Use the small CLI tools code2prompt! Have been using it for some time and it works very well! https://t.co/6pbXe2mNln", "media": [{"type": "photo", "url": "https://t.co/6pbXe2mNln", "thumbnail": "https://pbs.twimg.com/media/GPpcydMWkAA0fIm?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GPpcydMWkAA0fIm?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 878, "retweet_count": 124, "bookmark_count": 1634, "quote_count": 3, "reply_count": 23, "views_count": 135077, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1799853027261874570", "metadata": {"__typename": "Tweet", "rest_id": "1799853027261874570", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5Njk1OTg2MDM4NjkyODIzMDU=", "rest_id": "969598603869282305", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1796086907975131136/RMp-gyuN_normal.jpg"}, "core": {"created_at": "Fri Mar 02 15:41:36 +0000 2018", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "I love technology and innovation and no rabbit hole is too deep for me", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "outatime-podcast.de", "expanded_url": "https://www.outatime-podcast.de/", "url": "https://t.co/s0R2JsjHFb", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 18266, "followers_count": 865, "friends_count": 563, "has_custom_timelines": false, "is_translator": false, "listed_count": 17, "media_count": 758, "normal_followers_count": 865, "pinned_tweet_ids_str": ["1727272177211441429"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/969598603869282305/1705939960", "profile_interstitial_type": "", "statuses_count": 3981, "translator_type": "none", "url": "https://t.co/s0R2JsjHFb", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Germany"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1739267196226805771", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1799853027261874570"], "editable_until_msecs": "1717956976000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "135077", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1634, "bookmarked": true, "created_at": "Sun Jun 09 17:16:16 +0000 2024", "conversation_id_str": "1799853027261874570", "display_text_range": [0, 203], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/6pbXe2mNln", "expanded_url": "https://x.com/<PERSON><PERSON><PERSON><PERSON>/status/1799853027261874570/photo/1", "id_str": "1799853022987587584", "indices": [204, 227], "media_key": "3_1799853022987587584", "media_url_https": "https://pbs.twimg.com/media/GPpcydMWkAA0fIm.jpg", "type": "photo", "url": "https://t.co/6pbXe2mNln", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 575, "y": 34, "h": 63, "w": 63}]}, "medium": {"faces": [{"x": 575, "y": 34, "h": 63, "w": 63}]}, "small": {"faces": [{"x": 331, "y": 19, "h": 36, "w": 36}]}, "orig": {"faces": [{"x": 575, "y": 34, "h": 63, "w": 63}]}}, "sizes": {"large": {"h": 730, "w": 1179, "resize": "fit"}, "medium": {"h": 730, "w": 1179, "resize": "fit"}, "small": {"h": 421, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 730, "width": 1179, "focus_rects": [{"x": 0, "y": 0, "w": 1179, "h": 660}, {"x": 0, "y": 0, "w": 730, "h": 730}, {"x": 0, "y": 0, "w": 640, "h": 730}, {"x": 24, "y": 0, "w": 365, "h": 730}, {"x": 0, "y": 0, "w": 1179, "h": 730}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1799853022987587584"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/6pbXe2mNln", "expanded_url": "https://x.com/<PERSON><PERSON><PERSON><PERSON>/status/1799853027261874570/photo/1", "id_str": "1799853022987587584", "indices": [204, 227], "media_key": "3_1799853022987587584", "media_url_https": "https://pbs.twimg.com/media/GPpcydMWkAA0fIm.jpg", "type": "photo", "url": "https://t.co/6pbXe2mNln", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 575, "y": 34, "h": 63, "w": 63}]}, "medium": {"faces": [{"x": 575, "y": 34, "h": 63, "w": 63}]}, "small": {"faces": [{"x": 331, "y": 19, "h": 36, "w": 36}]}, "orig": {"faces": [{"x": 575, "y": 34, "h": 63, "w": 63}]}}, "sizes": {"large": {"h": 730, "w": 1179, "resize": "fit"}, "medium": {"h": 730, "w": 1179, "resize": "fit"}, "small": {"h": 421, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 730, "width": 1179, "focus_rects": [{"x": 0, "y": 0, "w": 1179, "h": 660}, {"x": 0, "y": 0, "w": 730, "h": 730}, {"x": 0, "y": 0, "w": 640, "h": 730}, {"x": 24, "y": 0, "w": 365, "h": 730}, {"x": 0, "y": 0, "w": 1179, "h": 730}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1799853022987587584"}}}]}, "favorite_count": 878, "favorited": false, "full_text": "Want to paste your entire codebase to <PERSON>tg<PERSON>, <PERSON> or your local LLM of choice including folder structure? Use the small CLI tools code2prompt! Have been using it for some time and it works very well! https://t.co/6pbXe2mNln", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 3, "reply_count": 23, "retweet_count": 124, "retweeted": false, "user_id_str": "969598603869282305", "id_str": "1799853027261874570"}, "twe_private_fields": {"created_at": 1717953376000, "updated_at": 1748554161183, "media_count": 1}}}, {"id": "1799949853289804266", "created_at": "2024-06-10 02:41:01 +03:00", "full_text": "📽️ New 4 hour (lol) video lecture on YouTube:\n\"Let’s reproduce GPT-2 (124M)\"\nhttps://t.co/QTUdu8b0qh\n\nThe video ended up so long because it is... comprehensive: we start with empty file and end up with a GPT-2 (124M) model:\n- first we build the GPT-2 network \n- then we optimize it to train very fast\n- then we set up the training run optimization and hyperparameters by referencing GPT-2 and GPT-3 papers\n- then we bring up model evaluation, and \n- then cross our fingers and go to sleep. \nIn the morning we look through the results and enjoy amusing model generations. Our \"overnight\" run even gets very close to the GPT-3 (124M) model. This video builds on the Zero To Hero series and at times references previous videos. You could also see this video as building my nanoGPT repo, which by the end is about 90% similar.\n\nGithub. The associated GitHub repo contains the full commit history so you can step through all of the code changes in the video, step by step.\nhttps://t.co/BOzkxQ8at2\n\nChapters.\nOn a high level Section 1 is building up the network, a lot of this might be review. Section 2 is making the training fast. Section 3 is setting up the run. Section 4 is the results. In more detail:\n00:00:00 intro: Let’s reproduce GPT-2 (124M)\n00:03:39 exploring the GPT-2 (124M) OpenAI checkpoint\n00:13:47 SECTION 1: implementing the GPT-2 nn.Module\n00:28:08 loading the huggingface/GPT-2 parameters\n00:31:00 implementing the forward pass to get logits\n00:33:31 sampling init, prefix tokens, tokenization\n00:37:02 sampling loop\n00:41:47 sample, auto-detect the device\n00:45:50 let’s train: data batches (B,T) → logits (B,T,C)\n00:52:53 cross entropy loss\n00:56:42 optimization loop: overfit a single batch\n01:02:00 data loader lite\n01:06:14 parameter sharing wte and lm_head\n01:13:47 model initialization: std 0.02, residual init\n01:22:18 SECTION 2: Let’s make it fast. GPUs, mixed precision, 1000ms\n01:28:14 Tensor Cores, timing the code, TF32 precision, 333ms\n01:39:38 float16, gradient scalers, bfloat16, 300ms\n01:48:15 torch.compile, Python overhead, kernel fusion, 130ms\n02:00:18 flash attention, 96ms\n02:06:54 nice/ugly numbers. vocab size 50257 → 50304, 93ms\n02:14:55 SECTION 3: hyperpamaters, AdamW, gradient clipping\n02:21:06 learning rate scheduler: warmup + cosine decay\n02:26:21 batch size schedule, weight decay, FusedAdamW, 90ms\n02:34:09 gradient accumulation\n02:46:52 distributed data parallel (DDP)\n03:10:21 datasets used in GPT-2, GPT-3, FineWeb (EDU)\n03:23:10 validation data split, validation loss, sampling revive\n03:28:23 evaluation: HellaSwag, starting the run\n03:43:05 SECTION 4: results in the morning! GPT-2, GPT-3 repro\n03:56:21 shoutout to llm.c, equivalent but faster code in raw C/CUDA\n03:59:39 summary, phew, build-nanogpt github repo", "media": [{"type": "photo", "url": "https://t.co/NDqTrLbrO4", "thumbnail": "https://pbs.twimg.com/media/GPqzr1lbAAANObP?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GPqzr1lbAAANObP?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 15739, "retweet_count": 2256, "bookmark_count": 10572, "quote_count": 414, "reply_count": 422, "views_count": 1516666, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1799949853289804266", "metadata": {"__typename": "Tweet", "rest_id": "1799949853289804266", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMzgzNjYyOQ==", "rest_id": "33836629", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1296667294148382721/9Pr6XrPB_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Apr 21 06:49:15 +0000 2009", "name": "<PERSON><PERSON>", "screen_name": "karp<PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Building @EurekaLabsAI. Previously Director of AI @ Tesla, founding team @ OpenAI, CS231n/PhD @ Stanford. I like to train large deep neural nets 🧠🤖💥", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "karpathy.ai", "expanded_url": "https://karpathy.ai", "url": "https://t.co/0EcFthjJXM", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 18015, "followers_count": 1284120, "friends_count": 977, "has_custom_timelines": true, "is_translator": false, "listed_count": 16303, "media_count": 795, "normal_followers_count": 1284120, "pinned_tweet_ids_str": ["1617979122625712128"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/33836629/1407117611", "profile_interstitial_type": "", "statuses_count": 9539, "translator_type": "none", "url": "https://t.co/0EcFthjJXM", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Stanford"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1799949853289804266"], "editable_until_msecs": "1717980061000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1516666", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3OTk5NDk4NTI5Nzk0NDE2NjQ=", "text": "📽️ New 4 hour (lol) video lecture on YouTube:\n\"Let’s reproduce GPT-2 (124M)\"\nhttps://t.co/QTUdu8b0qh\n\nThe video ended up so long because it is... comprehensive: we start with empty file and end up with a GPT-2 (124M) model:\n- first we build the GPT-2 network \n- then we optimize it to train very fast\n- then we set up the training run optimization and hyperparameters by referencing GPT-2 and GPT-3 papers\n- then we bring up model evaluation, and \n- then cross our fingers and go to sleep. \nIn the morning we look through the results and enjoy amusing model generations. Our \"overnight\" run even gets very close to the GPT-3 (124M) model. This video builds on the Zero To Hero series and at times references previous videos. You could also see this video as building my nanoGPT repo, which by the end is about 90% similar.\n\nGithub. The associated GitHub repo contains the full commit history so you can step through all of the code changes in the video, step by step.\nhttps://t.co/BOzkxQ8at2\n\nChapters.\nOn a high level Section 1 is building up the network, a lot of this might be review. Section 2 is making the training fast. Section 3 is setting up the run. Section 4 is the results. In more detail:\n00:00:00 intro: Let’s reproduce GPT-2 (124M)\n00:03:39 exploring the GPT-2 (124M) OpenAI checkpoint\n00:13:47 SECTION 1: implementing the GPT-2 nn.Module\n00:28:08 loading the huggingface/GPT-2 parameters\n00:31:00 implementing the forward pass to get logits\n00:33:31 sampling init, prefix tokens, tokenization\n00:37:02 sampling loop\n00:41:47 sample, auto-detect the device\n00:45:50 let’s train: data batches (B,T) → logits (B,T,C)\n00:52:53 cross entropy loss\n00:56:42 optimization loop: overfit a single batch\n01:02:00 data loader lite\n01:06:14 parameter sharing wte and lm_head\n01:13:47 model initialization: std 0.02, residual init\n01:22:18 SECTION 2: Let’s make it fast. GPUs, mixed precision, 1000ms\n01:28:14 Tensor Cores, timing the code, TF32 precision, 333ms\n01:39:38 float16, gradient scalers, bfloat16, 300ms\n01:48:15 torch.compile, Python overhead, kernel fusion, 130ms\n02:00:18 flash attention, 96ms\n02:06:54 nice/ugly numbers. vocab size 50257 → 50304, 93ms\n02:14:55 SECTION 3: hyperpamaters, AdamW, gradient clipping\n02:21:06 learning rate scheduler: warmup + cosine decay\n02:26:21 batch size schedule, weight decay, FusedAdamW, 90ms\n02:34:09 gradient accumulation\n02:46:52 distributed data parallel (DDP)\n03:10:21 datasets used in GPT-2, GPT-3, FineWeb (EDU)\n03:23:10 validation data split, validation loss, sampling revive\n03:28:23 evaluation: HellaSwag, starting the run\n03:43:05 SECTION 4: results in the morning! GPT-2, GPT-3 repro\n03:56:21 shoutout to llm.c, equivalent but faster code in raw C/CUDA\n03:59:39 summary, phew, build-nanogpt github repo", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "youtu.be/l8pRSuU81PU", "expanded_url": "https://youtu.be/l8pRSuU81PU", "url": "https://t.co/QTUdu8b0qh", "indices": [77, 100]}, {"display_url": "github.com/karpathy/build…", "expanded_url": "https://github.com/karpathy/build-nanogpt", "url": "https://t.co/BOzkxQ8at2", "indices": [968, 991]}], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 47, "to_index": 77, "richtext_types": ["Bold"]}, {"from_index": 825, "to_index": 833, "richtext_types": ["Bold"]}, {"from_index": 994, "to_index": 1003, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 10572, "bookmarked": true, "created_at": "Sun Jun 09 23:41:01 +0000 2024", "conversation_id_str": "1799949853289804266", "display_text_range": [0, 278], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/NDqTrLbrO4", "expanded_url": "https://x.com/karpathy/status/1799949853289804266/photo/1", "id_str": "1799948566787719168", "indices": [279, 302], "media_key": "3_1799948566787719168", "media_url_https": "https://pbs.twimg.com/media/GPqzr1lbAAANObP.jpg", "type": "photo", "url": "https://t.co/NDqTrLbrO4", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 986, "y": 383, "h": 175, "w": 175}]}, "medium": {"faces": [{"x": 924, "y": 359, "h": 164, "w": 164}]}, "small": {"faces": [{"x": 523, "y": 203, "h": 92, "w": 92}]}, "orig": {"faces": [{"x": 986, "y": 383, "h": 175, "w": 175}]}}, "sizes": {"large": {"h": 720, "w": 1280, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 720, "width": 1280, "focus_rects": [{"x": 0, "y": 0, "w": 1280, "h": 717}, {"x": 56, "y": 0, "w": 720, "h": 720}, {"x": 100, "y": 0, "w": 632, "h": 720}, {"x": 236, "y": 0, "w": 360, "h": 720}, {"x": 0, "y": 0, "w": 1280, "h": 720}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1799948566787719168"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/l8pRSuU81PU", "expanded_url": "https://youtu.be/l8pRSuU81PU", "url": "https://t.co/NMIVD1V6zr", "indices": [77, 100]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/NDqTrLbrO4", "expanded_url": "https://x.com/karpathy/status/1799949853289804266/photo/1", "id_str": "1799948566787719168", "indices": [279, 302], "media_key": "3_1799948566787719168", "media_url_https": "https://pbs.twimg.com/media/GPqzr1lbAAANObP.jpg", "type": "photo", "url": "https://t.co/NDqTrLbrO4", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 986, "y": 383, "h": 175, "w": 175}]}, "medium": {"faces": [{"x": 924, "y": 359, "h": 164, "w": 164}]}, "small": {"faces": [{"x": 523, "y": 203, "h": 92, "w": 92}]}, "orig": {"faces": [{"x": 986, "y": 383, "h": 175, "w": 175}]}}, "sizes": {"large": {"h": 720, "w": 1280, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 720, "width": 1280, "focus_rects": [{"x": 0, "y": 0, "w": 1280, "h": 717}, {"x": 56, "y": 0, "w": 720, "h": 720}, {"x": 100, "y": 0, "w": 632, "h": 720}, {"x": 236, "y": 0, "w": 360, "h": 720}, {"x": 0, "y": 0, "w": 1280, "h": 720}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1799948566787719168"}}}]}, "favorite_count": 15739, "favorited": false, "full_text": "📽️ New 4 hour (lol) video lecture on YouTube:\n\"Let’s reproduce GPT-2 (124M)\"\nhttps://t.co/NMIVD1V6zr\n\nThe video ended up so long because it is... comprehensive: we start with empty file and end up with a GPT-2 (124M) model:\n- first we build the GPT-2 network \n- then we optimize https://t.co/NDqTrLbrO4", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 414, "reply_count": 422, "retweet_count": 2256, "retweeted": false, "user_id_str": "33836629", "id_str": "1799949853289804266"}, "twe_private_fields": {"created_at": 1717976461000, "updated_at": 1748554161183, "media_count": 1}}}]