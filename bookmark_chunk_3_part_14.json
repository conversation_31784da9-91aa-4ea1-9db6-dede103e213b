[{"id": "1532630375683399680", "created_at": "2022-06-03 10:48:51 +03:00", "full_text": "Twitter’s advanced search is super underrated!\n\nFor example:\n\nSay you want to find a roadmap for learning Python, JavaScript, web3, etc.\n\nThen, try a query like:\n\npython roadmap min_faves:100 since:2022-01-01\n\nThis way, you can find the most popular roadmaps published this year.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 731, "retweet_count": 134, "bookmark_count": 321, "quote_count": 8, "reply_count": 16, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532630375683399680", "metadata": {"__typename": "Tweet", "rest_id": "1532630375683399680", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMDEzNjk1ODE=", "rest_id": "*********", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/Sourcegraph", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1885750906899079169/p98Ru-Pl_bigger.jpg"}, "description": "Sourcegraph", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1732269446713217024/OrNokBIn_normal.jpg"}, "core": {"created_at": "Sun Jan 03 02:10:09 +0000 2010", "name": "<PERSON><PERSON> aka CS Dojo 📺🐦", "screen_name": "yk<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "- Developer Experience Manager @Sourcegraph & @AmpCode\n- CS Dojo on YT (1.9M+ subs)\n- Coding with AI, next-level: https://t.co/8xoqGiUxV4", "entities": {"description": {"urls": [{"display_url": "agenticcoding.substack.com", "expanded_url": "https://agenticcoding.substack.com/", "url": "https://t.co/8xoqGiUxV4", "indices": [114, 137]}]}, "url": {"urls": [{"display_url": "agenticcoding.substack.com", "expanded_url": "https://agenticcoding.substack.com/", "url": "https://t.co/8xoqGiUxV4", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 46943, "followers_count": 149236, "friends_count": 474, "has_custom_timelines": true, "is_translator": false, "listed_count": 749, "media_count": 526, "normal_followers_count": 149236, "pinned_tweet_ids_str": ["1924539279147196572"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/*********/1734243728", "profile_interstitial_type": "", "statuses_count": 12633, "translator_type": "none", "url": "https://t.co/8xoqGiUxV4", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Sunny Francisco & Raincouver"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532630375683399680"], "editable_until_msecs": "1654244331000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 321, "bookmarked": true, "created_at": "Fri Jun 03 07:48:51 +0000 2022", "conversation_id_str": "1532630375683399680", "display_text_range": [0, 279], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 731, "favorited": false, "full_text": "Twitter’s advanced search is super underrated!\n\nFor example:\n\nSay you want to find a roadmap for learning Python, JavaScript, web3, etc.\n\nThen, try a query like:\n\npython roadmap min_faves:100 since:2022-01-01\n\nThis way, you can find the most popular roadmaps published this year.", "is_quote_status": false, "lang": "en", "quote_count": 8, "reply_count": 16, "retweet_count": 134, "retweeted": false, "user_id_str": "*********", "id_str": "1532630375683399680"}, "twe_private_fields": {"created_at": *************, "updated_at": *************, "media_count": 0}}}, {"id": "1532661541056782337", "created_at": "2022-06-03 12:52:41 +03:00", "full_text": "A guide to the FPL Community\nHey so I'm back looking to help new accounts in the FPL Community. I'm providing an updated list of some great accounts for any new account to look at. This will hopefully help them to learn who does what and interact with them too so lets go..(1/18)", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 231, "retweet_count": 48, "bookmark_count": 44, "quote_count": 26, "reply_count": 31, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532661541056782337", "metadata": {"__typename": "Tweet", "rest_id": "1532661541056782337", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTI2Mzc4MjU=", "rest_id": "*********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1910027185219379200/1m_uVVkd_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Jun 07 12:51:32 +0000 2011", "name": "<PERSON>", "screen_name": "DanDave1989"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Birmingham City Fan. Chapter Lead for @FPLMeets Birmingham. Organiser of World Cups. FPL 23/24 36k. Gaffr 23/24 65. SDT 23/24 175. SkyFF 23/24 261", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 47176, "followers_count": 6147, "friends_count": 3114, "has_custom_timelines": true, "is_translator": false, "listed_count": 89, "media_count": 3390, "normal_followers_count": 6147, "pinned_tweet_ids_str": ["1532661541056782337"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/*********/1692307899", "profile_interstitial_type": "", "statuses_count": 34967, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Birmingham"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532661541056782337"], "editable_until_msecs": "1654251761000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 44, "bookmarked": true, "created_at": "Fri Jun 03 09:52:41 +0000 2022", "conversation_id_str": "1532661541056782337", "display_text_range": [0, 279], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 231, "favorited": false, "full_text": "A guide to the FPL Community\nHey so I'm back looking to help new accounts in the FPL Community. I'm providing an updated list of some great accounts for any new account to look at. This will hopefully help them to learn who does what and interact with them too so lets go..(1/18)", "is_quote_status": false, "lang": "en", "quote_count": 26, "reply_count": 31, "retweet_count": 48, "retweeted": false, "user_id_str": "*********", "id_str": "1532661541056782337"}, "twe_private_fields": {"created_at": *************, "updated_at": *************, "media_count": 0}}}]