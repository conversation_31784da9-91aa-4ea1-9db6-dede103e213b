[{"id": "1378238559698759681", "created_at": "2021-04-03 09:50:53 +03:00", "full_text": "Wazza ladies and gentlemen.....\n\n https://t.co/jAYmJphc0t", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3402, "retweet_count": 732, "bookmark_count": 119, "quote_count": 286, "reply_count": 59, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1378238559698759681", "metadata": {"__typename": "Tweet", "rest_id": "1378238559698759681", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzM4NTI4NzQxNjEwMzYwODMy", "rest_id": "1338528741610360832", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1895945064649531392/tZkjrr4F_normal.jpg"}, "core": {"created_at": "Mon Dec 14 16:58:27 +0000 2020", "name": "Cantona Collars AKA Larry", "screen_name": "Cantona_Collars"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I generally post nostalgic #MUFC stuff. My football opinion is as often wrong as it is correct. I'm partial to a political debate.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 188567, "followers_count": 31904, "friends_count": 1019, "has_custom_timelines": true, "is_translator": false, "listed_count": 91, "media_count": 15049, "normal_followers_count": 31904, "pinned_tweet_ids_str": ["1903019761044222082"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1338528741610360832/1718133669", "profile_interstitial_type": "", "statuses_count": 60190, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1378238559698759681"], "editable_until_msecs": "1617434453723", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 119, "bookmarked": true, "created_at": "Sat Apr 03 06:50:53 +0000 2021", "conversation_id_str": "1378238559698759681", "display_text_range": [0, 57], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/LancsOG/status…", "expanded_url": "https://x.com/LancsOG/status/1278313781848834055/video/1", "url": "https://t.co/jAYmJphc0t", "indices": [34, 57]}], "user_mentions": []}, "favorite_count": 3402, "favorited": false, "full_text": "Wazza ladies and gentlemen.....\n\n https://t.co/jAYmJphc0t", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 286, "reply_count": 59, "retweet_count": 732, "retweeted": false, "user_id_str": "1338528741610360832", "id_str": "1378238559698759681"}, "twe_private_fields": {"created_at": 1617432653000, "updated_at": 1748554432616, "media_count": 0}}}, {"id": "1378289133488062465", "created_at": "2021-04-03 13:11:51 +03:00", "full_text": "How to analyze Excel Data with Pivot Tables. \nFull Video #Subscribetomychannel https://t.co/KV2XM2bbfd https://t.co/LLiaWwPQVK", "media": [{"type": "video", "url": "https://t.co/LLiaWwPQVK", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1378288911265505281/pu/img/5z2pNNhy_67dpaje.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/1280x684/A8A13cKK1Kl0rw8Y.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 734, "retweet_count": 318, "bookmark_count": 220, "quote_count": 7, "reply_count": 21, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1378289133488062465", "metadata": {"__typename": "Tweet", "rest_id": "1378289133488062465", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MDM2Mzg0Ng==", "rest_id": "70363846", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1832822001397903361/OHb1DTyU_normal.jpg"}, "core": {"created_at": "Mon Aug 31 10:44:16 +0000 2009", "name": "Maestro", "screen_name": "ExcelMaestro"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 5568, "followers_count": 12920, "friends_count": 5260, "has_custom_timelines": true, "is_translator": false, "listed_count": 228, "media_count": 2518, "normal_followers_count": 12920, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/70363846/1678634584", "profile_interstitial_type": "", "statuses_count": 30432, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Gikambura, Kenya"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1462832106187411456", "professional_type": "Creator", "category": [{"id": 477, "name": "Professional Services", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "patreon_handle": "ExcelMaestro"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1378289133488062465"], "editable_until_msecs": "1617446511454", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 220, "bookmarked": true, "created_at": "Sat Apr 03 10:11:51 +0000 2021", "conversation_id_str": "1378289133488062465", "display_text_range": [0, 102], "entities": {"hashtags": [{"indices": [57, 78], "text": "Subscribetomychannel"}], "media": [{"display_url": "pic.x.com/LLiaWwPQVK", "expanded_url": "https://x.com/ExcelMaestro/status/1378289133488062465/video/1", "id_str": "1378288911265505281", "indices": [103, 126], "media_key": "7_1378288911265505281", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1378288911265505281/pu/img/5z2pNNhy_67dpaje.jpg", "type": "video", "url": "https://t.co/LLiaWwPQVK", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 684, "w": 1280, "resize": "fit"}, "medium": {"h": 641, "w": 1200, "resize": "fit"}, "small": {"h": 363, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 684, "width": 1280, "focus_rects": []}, "video_info": {"aspect_ratio": [320, 171], "duration_millis": 139967, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/pl/GSeoTJ2JthSeHmJq.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/504x270/dELBtXyPKa3ex_Ru.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/672x360/jF9IceDKcdaEHQN9.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/1280x684/A8A13cKK1Kl0rw8Y.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1378288911265505281"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/oDj4qGBKD5o", "expanded_url": "https://youtu.be/oDj4qGBKD5o", "url": "https://t.co/KV2XM2bbfd", "indices": [79, 102]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/LLiaWwPQVK", "expanded_url": "https://x.com/ExcelMaestro/status/1378289133488062465/video/1", "id_str": "1378288911265505281", "indices": [103, 126], "media_key": "7_1378288911265505281", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1378288911265505281/pu/img/5z2pNNhy_67dpaje.jpg", "type": "video", "url": "https://t.co/LLiaWwPQVK", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 684, "w": 1280, "resize": "fit"}, "medium": {"h": 641, "w": 1200, "resize": "fit"}, "small": {"h": 363, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 684, "width": 1280, "focus_rects": []}, "video_info": {"aspect_ratio": [320, 171], "duration_millis": 139967, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/pl/GSeoTJ2JthSeHmJq.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/504x270/dELBtXyPKa3ex_Ru.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/672x360/jF9IceDKcdaEHQN9.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1378288911265505281/pu/vid/1280x684/A8A13cKK1Kl0rw8Y.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1378288911265505281"}}}]}, "favorite_count": 734, "favorited": false, "full_text": "How to analyze Excel Data with Pivot Tables. \nFull Video #Subscribetomychannel https://t.co/KV2XM2bbfd https://t.co/LLiaWwPQVK", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "place": {"bounding_box": {"coordinates": [[[36.6645733, -1.3890532], [36.6645733, -1.1606735], [37.0626672, -1.1606735], [37.0626672, -1.3890532], [36.6645733, -1.3890532]]], "type": "Polygon"}, "country": "Kenya", "country_code": "KE", "full_name": "Nairobi, Kenya", "name": "Nairobi", "id": "01e215db7136a37e", "place_type": "city", "url": "https://api.twitter.com/1.1/geo/id/01e215db7136a37e.json"}, "quote_count": 7, "reply_count": 21, "retweet_count": 318, "retweeted": false, "user_id_str": "70363846", "id_str": "1378289133488062465"}, "twe_private_fields": {"created_at": 1617444711000, "updated_at": 1748554432616, "media_count": 1}}}]