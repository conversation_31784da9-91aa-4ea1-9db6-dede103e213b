[{"id": "1416041352396255233", "created_at": "2021-07-16 17:25:41 +03:00", "full_text": "My journey so far.. Thank you @TPTFootball @PlayersTribune \n\nhttps://t.co/V2ervRrhJ2\n\nhttps://t.co/EWy7gjnlfm https://t.co/VNizbFzBuf", "media": [{"type": "photo", "url": "https://t.co/VNizbFzBuf", "thumbnail": "https://pbs.twimg.com/media/E6bKGqNXIAQS_6E?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/E6bKGqNXIAQS_6E?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5411, "retweet_count": 457, "bookmark_count": 51, "quote_count": 54, "reply_count": 79, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1416041352396255233", "metadata": {"__typename": "Tweet", "rest_id": "1416041352396255233", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTE1NjM0NTM2MTY1OTUzNTM3", "rest_id": "1115634536165953537", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1432053580408709130/iNEZ2U2Q_normal.jpg"}, "core": {"created_at": "Tue Apr 09 15:16:15 +0000 2019", "name": "<PERSON>", "screen_name": "awbissaka"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "🔴 Footballer @ManUtd and @Adidas athlete ⚽️ Enquiries: <EMAIL>", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 113, "followers_count": 1131631, "friends_count": 32, "has_custom_timelines": true, "is_translator": false, "listed_count": 1454, "media_count": 121, "normal_followers_count": 1131631, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1115634536165953537/1629103718", "profile_interstitial_type": "", "statuses_count": 172, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Manchester, England"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1416041352396255233"], "editable_until_msecs": "1626447341844", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 51, "bookmarked": true, "created_at": "Fri Jul 16 14:25:41 +0000 2021", "conversation_id_str": "1416041352396255233", "display_text_range": [0, 109], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/VNizbFzBuf", "expanded_url": "https://x.com/awbissaka/status/1416041352396255233/photo/1", "id_str": "1416041348612956164", "indices": [110, 133], "media_key": "3_1416041348612956164", "media_url_https": "https://pbs.twimg.com/media/E6bKGqNXIAQS_6E.jpg", "type": "photo", "url": "https://t.co/VNizbFzBuf", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 407, "y": 229, "h": 266, "w": 266}]}, "medium": {"faces": [{"x": 407, "y": 229, "h": 266, "w": 266}]}, "small": {"faces": [{"x": 256, "y": 144, "h": 167, "w": 167}]}, "orig": {"faces": [{"x": 407, "y": 229, "h": 266, "w": 266}]}}, "sizes": {"large": {"h": 1080, "w": 1080, "resize": "fit"}, "medium": {"h": 1080, "w": 1080, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 1080, "focus_rects": [{"x": 0, "y": 475, "w": 1080, "h": 605}, {"x": 0, "y": 0, "w": 1080, "h": 1080}, {"x": 40, "y": 0, "w": 947, "h": 1080}, {"x": 243, "y": 0, "w": 540, "h": 1080}, {"x": 0, "y": 0, "w": 1080, "h": 1080}]}, "media_results": {"result": {"media_key": "3_1416041348612956164"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtube.com/watch?v=tYwCJ2…", "expanded_url": "https://www.youtube.com/watch?v=tYwCJ2BLYC0", "url": "https://t.co/V2ervRrhJ2", "indices": [61, 84]}, {"display_url": "theplayerstribune.com/posts/aaron-wa…", "expanded_url": "https://www.theplayerstribune.com/posts/aaron-wan-bissaka-premier-league-manchester-united-soccer", "url": "https://t.co/EWy7gjnlfm", "indices": [86, 109]}], "user_mentions": [{"id_str": "977271553871278080", "name": "Players' Tribune Football", "screen_name": "TPTFootball", "indices": [30, 42]}, {"id_str": "415605847", "name": "The Players’ Tribune", "screen_name": "PlayersTribune", "indices": [43, 58]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/VNizbFzBuf", "expanded_url": "https://x.com/awbissaka/status/1416041352396255233/photo/1", "id_str": "1416041348612956164", "indices": [110, 133], "media_key": "3_1416041348612956164", "media_url_https": "https://pbs.twimg.com/media/E6bKGqNXIAQS_6E.jpg", "type": "photo", "url": "https://t.co/VNizbFzBuf", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 407, "y": 229, "h": 266, "w": 266}]}, "medium": {"faces": [{"x": 407, "y": 229, "h": 266, "w": 266}]}, "small": {"faces": [{"x": 256, "y": 144, "h": 167, "w": 167}]}, "orig": {"faces": [{"x": 407, "y": 229, "h": 266, "w": 266}]}}, "sizes": {"large": {"h": 1080, "w": 1080, "resize": "fit"}, "medium": {"h": 1080, "w": 1080, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 1080, "focus_rects": [{"x": 0, "y": 475, "w": 1080, "h": 605}, {"x": 0, "y": 0, "w": 1080, "h": 1080}, {"x": 40, "y": 0, "w": 947, "h": 1080}, {"x": 243, "y": 0, "w": 540, "h": 1080}, {"x": 0, "y": 0, "w": 1080, "h": 1080}]}, "media_results": {"result": {"media_key": "3_1416041348612956164"}}}]}, "favorite_count": 5411, "favorited": false, "full_text": "My journey so far.. Thank you @TPTFootball @PlayersTribune \n\nhttps://t.co/V2ervRrhJ2\n\nhttps://t.co/EWy7gjnlfm https://t.co/VNizbFzBuf", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 54, "reply_count": 79, "retweet_count": 457, "retweeted": false, "user_id_str": "1115634536165953537", "id_str": "1416041352396255233"}, "twe_private_fields": {"created_at": 1626445541000, "updated_at": 1748554397393, "media_count": 1}}}, {"id": "1416459775563292674", "created_at": "2021-07-17 21:08:21 +03:00", "full_text": "Do you want to make nightingale/pizza charts but don't know how to code. I have created a web app that plots a basic version of the nightingale chart. The web app is made using streamlit and uses mplsoccer for making the chart.\n\nhttps://t.co/0nAWNQoB5e https://t.co/4N8BwOEUTI", "media": [{"type": "photo", "url": "https://t.co/4N8BwOEUTI", "thumbnail": "https://pbs.twimg.com/media/E6hGmLKUcAEUcgw?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/E6hGmLKUcAEUcgw?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 255, "retweet_count": 54, "bookmark_count": 129, "quote_count": 8, "reply_count": 18, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1416459775563292674", "metadata": {"__typename": "Tweet", "rest_id": "1416459775563292674", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjM3MDc1NTY5NjYzMDc0MzA0", "rest_id": "1237075569663074304", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1851862887859630080/DXM1b6pu_normal.jpg"}, "core": {"created_at": "Mon Mar 09 18:00:02 +0000 2020", "name": "An<PERSON><PERSON>", "screen_name": "slothfulwave612"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "🦥", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pin.it/6BNxjNuUI", "expanded_url": "https://pin.it/6BNxjNuUI", "url": "https://t.co/BNQPXgB6mP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 244937, "followers_count": 2022, "friends_count": 438, "has_custom_timelines": true, "is_translator": false, "listed_count": 40, "media_count": 221, "normal_followers_count": 2022, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1237075569663074304/1705940436", "profile_interstitial_type": "", "statuses_count": 10292, "translator_type": "none", "url": "https://t.co/BNQPXgB6mP", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1416459775563292674"], "editable_until_msecs": "1626547101701", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 129, "bookmarked": true, "created_at": "Sat Jul 17 18:08:21 +0000 2021", "conversation_id_str": "1416459775563292674", "display_text_range": [0, 252], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/4N8BwOEUTI", "expanded_url": "https://x.com/slothfulwave612/status/1416459775563292674/photo/1", "id_str": "1416459704453066753", "indices": [253, 276], "media_key": "3_1416459704453066753", "media_url_https": "https://pbs.twimg.com/media/E6hGmLKUcAEUcgw.jpg", "type": "photo", "url": "https://t.co/4N8BwOEUTI", "ext_media_availability": {"status": "Available"}, "features": {"all": {"tags": [{"user_id": "1012486837384835072", "name": "Streamlit", "screen_name": "streamlit", "type": "user"}, {"user_id": "839501726738755584", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "user"}]}, "large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1678, "w": 1460, "resize": "fit"}, "medium": {"h": 1200, "w": 1044, "resize": "fit"}, "small": {"h": 680, "w": 592, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1678, "width": 1460, "focus_rects": [{"x": 0, "y": 0, "w": 1460, "h": 818}, {"x": 0, "y": 0, "w": 1460, "h": 1460}, {"x": 0, "y": 0, "w": 1460, "h": 1664}, {"x": 126, "y": 0, "w": 839, "h": 1678}, {"x": 0, "y": 0, "w": 1460, "h": 1678}]}, "media_results": {"result": {"media_key": "3_1416459704453066753"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "…htingale-charts-plotter.herokuapp.com", "expanded_url": "https://nightingale-charts-plotter.herokuapp.com/", "url": "https://t.co/0nAWNQoB5e", "indices": [229, 252]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/4N8BwOEUTI", "expanded_url": "https://x.com/slothfulwave612/status/1416459775563292674/photo/1", "id_str": "1416459704453066753", "indices": [253, 276], "media_key": "3_1416459704453066753", "media_url_https": "https://pbs.twimg.com/media/E6hGmLKUcAEUcgw.jpg", "type": "photo", "url": "https://t.co/4N8BwOEUTI", "ext_media_availability": {"status": "Available"}, "features": {"all": {"tags": [{"user_id": "1012486837384835072", "name": "Streamlit", "screen_name": "streamlit", "type": "user"}, {"user_id": "839501726738755584", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "user"}]}, "large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1678, "w": 1460, "resize": "fit"}, "medium": {"h": 1200, "w": 1044, "resize": "fit"}, "small": {"h": 680, "w": 592, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1678, "width": 1460, "focus_rects": [{"x": 0, "y": 0, "w": 1460, "h": 818}, {"x": 0, "y": 0, "w": 1460, "h": 1460}, {"x": 0, "y": 0, "w": 1460, "h": 1664}, {"x": 126, "y": 0, "w": 839, "h": 1678}, {"x": 0, "y": 0, "w": 1460, "h": 1678}]}, "media_results": {"result": {"media_key": "3_1416459704453066753"}}}]}, "favorite_count": 255, "favorited": false, "full_text": "Do you want to make nightingale/pizza charts but don't know how to code. I have created a web app that plots a basic version of the nightingale chart. The web app is made using streamlit and uses mplsoccer for making the chart.\n\nhttps://t.co/0nAWNQoB5e https://t.co/4N8BwOEUTI", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 8, "reply_count": 18, "retweet_count": 54, "retweeted": false, "user_id_str": "1237075569663074304", "id_str": "1416459775563292674"}, "twe_private_fields": {"created_at": 1626545301000, "updated_at": 1748554397393, "media_count": 1}}}]