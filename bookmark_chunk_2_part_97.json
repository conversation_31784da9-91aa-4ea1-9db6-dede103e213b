[{"id": "1527280221887426561", "created_at": "2022-05-19 16:29:15 +03:00", "full_text": "Some of my handmade JavaScript notes and cheat sheets that can help you: 🧵👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2907, "retweet_count": 755, "bookmark_count": 1991, "quote_count": 23, "reply_count": 103, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1527280221887426561", "metadata": {"__typename": "Tweet", "rest_id": "1527280221887426561", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTYxNTU1OTI1NzgxNTg1OTI=", "rest_id": "856155592578158592", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1612507803842932736/PLiJMD_l_normal.jpg"}, "core": {"created_at": "Sun Apr 23 14:39:34 +0000 2017", "name": "<PERSON><PERSON><PERSON>", "screen_name": "Prathku<PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I talk about web and social • DevRel @APILayer • Building https://t.co/niju9j3UA2 & https://t.co/TxBXHrPKDu • Prev @Rapid_API @HyperspaceAI", "entities": {"description": {"urls": [{"display_url": "TrioTech.Dev", "expanded_url": "http://TrioTech.Dev", "url": "https://t.co/niju9j3UA2", "indices": [58, 81]}, {"display_url": "RoastProfile.Social", "expanded_url": "http://RoastProfile.Social", "url": "https://t.co/TxBXHrPKDu", "indices": [84, 107]}]}, "url": {"urls": [{"display_url": "PrathamKumar.com", "expanded_url": "https://www.PrathamKumar.com", "url": "https://t.co/gGjMe9QuIY", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 148812, "followers_count": 435982, "friends_count": 891, "has_custom_timelines": true, "is_translator": false, "listed_count": 8454, "media_count": 5769, "normal_followers_count": 435982, "pinned_tweet_ids_str": ["1917933782612496654"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/856155592578158592/1696346737", "profile_interstitial_type": "", "statuses_count": 34425, "translator_type": "none", "url": "https://t.co/gGjMe9QuIY", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "New Delhi, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1473320674487721988", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "", "patreon_handle": ""}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1527280221887426561"], "editable_until_msecs": "1652968755000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1991, "bookmarked": true, "created_at": "Thu May 19 13:29:15 +0000 2022", "conversation_id_str": "1527280221887426561", "display_text_range": [0, 75], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2907, "favorited": false, "full_text": "Some of my handmade JavaScript notes and cheat sheets that can help you: 🧵👇", "is_quote_status": false, "lang": "en", "quote_count": 23, "reply_count": 103, "retweet_count": 755, "retweeted": false, "user_id_str": "856155592578158592", "id_str": "1527280221887426561"}, "twe_private_fields": {"created_at": 1652966955000, "updated_at": 1748554346018, "media_count": 0}}}, {"id": "1527331273941094402", "created_at": "2022-05-19 19:52:06 +03:00", "full_text": "My biggest project is finally here!\nI made an app that employs Machine Learning to group and find players with similar profiles.\n\nhttps://t.co/m4yhib1XvD https://t.co/P9E4BSmVw3", "media": [{"type": "video", "url": "https://t.co/P9E4BSmVw3", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1527312777509224448/pu/img/q5bA84q5xTUzJs2d.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/1540x720/Qfmwvu0dMXwbombg.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 481, "retweet_count": 98, "bookmark_count": 242, "quote_count": 11, "reply_count": 33, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1527331273941094402", "metadata": {"__typename": "Tweet", "rest_id": "1527331273941094402", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMTg4MzQyMDgw", "rest_id": "2188342080", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1396894839388532739/w6xaDDpz_normal.jpg"}, "core": {"created_at": "Mon Nov 11 13:00:22 +0000 2013", "name": "<PERSON><PERSON><PERSON>", "screen_name": "Anuraag027"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Football fan.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "thebeautifulgamereviewed.blogspot.com", "expanded_url": "https://thebeautifulgamereviewed.blogspot.com/", "url": "https://t.co/jl3iAxu8UA", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 123403, "followers_count": 1993, "friends_count": 669, "has_custom_timelines": true, "is_translator": false, "listed_count": 30, "media_count": 376, "normal_followers_count": 1993, "pinned_tweet_ids_str": ["1673343317356978176"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2188342080/1551373217", "profile_interstitial_type": "", "statuses_count": 12359, "translator_type": "none", "url": "https://t.co/jl3iAxu8UA", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Austin, TX"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1527331273941094402"], "editable_until_msecs": "1652980926000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 242, "bookmarked": true, "created_at": "Thu May 19 16:52:06 +0000 2022", "conversation_id_str": "1527331273941094402", "display_text_range": [0, 153], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/P9E4BSmVw3", "expanded_url": "https://x.com/Anuraag027/status/1527331273941094402/video/1", "id_str": "1527312777509224448", "indices": [154, 177], "media_key": "7_1527312777509224448", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1527312777509224448/pu/img/q5bA84q5xTUzJs2d.jpg", "type": "video", "url": "https://t.co/P9E4BSmVw3", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 896, "w": 1918, "resize": "fit"}, "medium": {"h": 561, "w": 1200, "resize": "fit"}, "small": {"h": 318, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 896, "width": 1918, "focus_rects": []}, "video_info": {"aspect_ratio": [137, 64], "duration_millis": 100033, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/pl/OvzDlmZniizKlMCX.m3u8?tag=12&v=cfc"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/576x270/bix6XaqzPXMAx3w8.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/770x360/9_9aMYpBhLhUqsih.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/1540x720/Qfmwvu0dMXwbombg.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1527312777509224448"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "share.streamlit.io/anuraag027/str…", "expanded_url": "https://share.streamlit.io/anuraag027/streamlitproject/main/streamlit1.py", "url": "https://t.co/m4yhib1XvD", "indices": [130, 153]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/P9E4BSmVw3", "expanded_url": "https://x.com/Anuraag027/status/1527331273941094402/video/1", "id_str": "1527312777509224448", "indices": [154, 177], "media_key": "7_1527312777509224448", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1527312777509224448/pu/img/q5bA84q5xTUzJs2d.jpg", "type": "video", "url": "https://t.co/P9E4BSmVw3", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 896, "w": 1918, "resize": "fit"}, "medium": {"h": 561, "w": 1200, "resize": "fit"}, "small": {"h": 318, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 896, "width": 1918, "focus_rects": []}, "video_info": {"aspect_ratio": [137, 64], "duration_millis": 100033, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/pl/OvzDlmZniizKlMCX.m3u8?tag=12&v=cfc"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/576x270/bix6XaqzPXMAx3w8.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/770x360/9_9aMYpBhLhUqsih.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1527312777509224448/pu/vid/1540x720/Qfmwvu0dMXwbombg.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1527312777509224448"}}}]}, "favorite_count": 481, "favorited": false, "full_text": "My biggest project is finally here!\nI made an app that employs Machine Learning to group and find players with similar profiles.\n\nhttps://t.co/m4yhib1XvD https://t.co/P9E4BSmVw3", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 11, "reply_count": 33, "retweet_count": 98, "retweeted": false, "user_id_str": "2188342080", "id_str": "1527331273941094402"}, "twe_private_fields": {"created_at": 1652979126000, "updated_at": 1748554346018, "media_count": 1}}}]