[{"id": "1830961494047252731", "created_at": "2024-09-03 16:30:12 +03:00", "full_text": "Speech recognition with word level timestamps https://t.co/joQHib7M6B", "media": [{"type": "photo", "url": "https://t.co/joQHib7M6B", "thumbnail": "https://pbs.twimg.com/media/GWjhixjX0AAeTTa?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GWjhixjX0AAeTTa?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2971, "retweet_count": 273, "bookmark_count": 2901, "quote_count": 13, "reply_count": 45, "views_count": 328139, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1830961494047252731", "metadata": {"__typename": "Tweet", "rest_id": "1830961494047252731", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzI2MTgwNzU2MzEwMzMxMzk5", "rest_id": "1326180756310331399", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1905090420142379008/Ydq5So7B_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Nov 10 15:13:32 +0000 2020", "name": "<PERSON>", "screen_name": "tom_doerr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Follow for posts about GitHub repos, DSPy, and agents\nSubscribe for top posts\nDM to share your AI project (Due to volume of DMs I'll prioritize subscribers)", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "tom-doerr.github.io/repo_posts/", "expanded_url": "https://tom-doerr.github.io/repo_posts/", "url": "https://t.co/WreHYiW9xe", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 52917, "followers_count": 84560, "friends_count": 2135, "has_custom_timelines": true, "is_translator": false, "listed_count": 808, "media_count": 6792, "normal_followers_count": 84560, "pinned_tweet_ids_str": ["1886002942962024635"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1326180756310331399/1721336045", "profile_interstitial_type": "", "statuses_count": 20559, "translator_type": "none", "url": "https://t.co/WreHYiW9xe", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1830961494047252731"], "editable_until_msecs": "1725373812000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "328139", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2901, "bookmarked": true, "created_at": "Tue Sep 03 13:30:12 +0000 2024", "conversation_id_str": "1830961494047252731", "display_text_range": [0, 45], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/joQHib7M6B", "expanded_url": "https://x.com/tom_doerr/status/1830961494047252731/photo/1", "id_str": "1830961236059607040", "indices": [46, 69], "media_key": "3_1830961236059607040", "media_url_https": "https://pbs.twimg.com/media/GWjhixjX0AAeTTa.png", "type": "photo", "url": "https://t.co/joQHib7M6B", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 671, "y": 118, "h": 176, "w": 176}]}, "medium": {"faces": [{"x": 671, "y": 118, "h": 176, "w": 176}]}, "small": {"faces": [{"x": 529, "y": 93, "h": 138, "w": 138}]}, "orig": {"faces": [{"x": 671, "y": 118, "h": 176, "w": 176}]}}, "sizes": {"large": {"h": 519, "w": 862, "resize": "fit"}, "medium": {"h": 519, "w": 862, "resize": "fit"}, "small": {"h": 409, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 519, "width": 862, "focus_rects": [{"x": 0, "y": 36, "w": 862, "h": 483}, {"x": 0, "y": 0, "w": 519, "h": 519}, {"x": 0, "y": 0, "w": 455, "h": 519}, {"x": 0, "y": 0, "w": 260, "h": 519}, {"x": 0, "y": 0, "w": 862, "h": 519}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1830961236059607040"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/joQHib7M6B", "expanded_url": "https://x.com/tom_doerr/status/1830961494047252731/photo/1", "id_str": "1830961236059607040", "indices": [46, 69], "media_key": "3_1830961236059607040", "media_url_https": "https://pbs.twimg.com/media/GWjhixjX0AAeTTa.png", "type": "photo", "url": "https://t.co/joQHib7M6B", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 671, "y": 118, "h": 176, "w": 176}]}, "medium": {"faces": [{"x": 671, "y": 118, "h": 176, "w": 176}]}, "small": {"faces": [{"x": 529, "y": 93, "h": 138, "w": 138}]}, "orig": {"faces": [{"x": 671, "y": 118, "h": 176, "w": 176}]}}, "sizes": {"large": {"h": 519, "w": 862, "resize": "fit"}, "medium": {"h": 519, "w": 862, "resize": "fit"}, "small": {"h": 409, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 519, "width": 862, "focus_rects": [{"x": 0, "y": 36, "w": 862, "h": 483}, {"x": 0, "y": 0, "w": 519, "h": 519}, {"x": 0, "y": 0, "w": 455, "h": 519}, {"x": 0, "y": 0, "w": 260, "h": 519}, {"x": 0, "y": 0, "w": 862, "h": 519}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1830961236059607040"}}}]}, "favorite_count": 2971, "favorited": false, "full_text": "Speech recognition with word level timestamps https://t.co/joQHib7M6B", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 13, "reply_count": 45, "retweet_count": 273, "retweeted": false, "user_id_str": "1326180756310331399", "id_str": "1830961494047252731"}, "twe_private_fields": {"created_at": 1725370212000, "updated_at": 1748554149587, "media_count": 1}}}, {"id": "1831767033509240892", "created_at": "2024-09-05 21:51:08 +03:00", "full_text": "This model is quite fun to use and insanely powerful.\n\nPlease check it out — with the right prompting, it’s an absolute beast for many use-cases.\n\nDemo here: https://t.co/gBWVmYSTMI", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1831767031735374222", "retweeted_status": null, "quoted_status": null, "favorite_count": 408, "retweet_count": 27, "bookmark_count": 220, "quote_count": 4, "reply_count": 16, "views_count": 61749, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1831767033509240892", "metadata": {"__typename": "Tweet", "rest_id": "1831767033509240892", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTk0ODg5MzE3Mzg4Mzc0MDE2", "rest_id": "1194889317388374016", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1490950574090571778/BtgOaqUP_normal.jpg"}, "core": {"created_at": "Thu Nov 14 08:06:43 +0000 2019", "name": "<PERSON>", "screen_name": "matts<PERSON>er_"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "CEO @HyperWriteAI, @OthersideAI. Try https://t.co/z3rZszeXxZ — AI photoshop, https://t.co/PSUlubx5bb — Github for prompts, https://t.co/euMCmq4CR0 — superagent", "entities": {"description": {"urls": [{"display_url": "PicPrompter.com", "expanded_url": "https://PicPrompter.com", "url": "https://t.co/z3rZszeXxZ", "indices": [37, 60]}, {"display_url": "ShumerPrompt.com", "expanded_url": "https://ShumerPrompt.com", "url": "https://t.co/PSUlubx5bb", "indices": [77, 100]}, {"display_url": "TokenMonster.ai", "expanded_url": "https://TokenMonster.ai", "url": "https://t.co/euMCmq4CR0", "indices": [123, 146]}]}, "url": {"urls": [{"display_url": "hyperwriteai.com", "expanded_url": "https://www.hyperwriteai.com/", "url": "https://t.co/UKjp4pdVJt", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8202, "followers_count": 88748, "friends_count": 1433, "has_custom_timelines": true, "is_translator": false, "listed_count": 1960, "media_count": 698, "normal_followers_count": 88748, "pinned_tweet_ids_str": ["1646234077798727686"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1194889317388374016/1605142094", "profile_interstitial_type": "", "statuses_count": 7556, "translator_type": "none", "url": "https://t.co/UKjp4pdVJt", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "The Otherside / NYC"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1504611049990893599", "professional_type": "Creator", "category": [{"id": 958, "name": "Entrepreneur", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false, "muting": true}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1831767033509240892"], "editable_until_msecs": "1725565868000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "61749", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 220, "bookmarked": true, "created_at": "Thu Sep 05 18:51:08 +0000 2024", "conversation_id_str": "1831767014341538166", "display_text_range": [0, 181], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "…-playground-production.up.railway.app", "expanded_url": "https://reflection-playground-production.up.railway.app/", "url": "https://t.co/gBWVmYSTMI", "indices": [158, 181]}], "user_mentions": []}, "favorite_count": 408, "favorited": false, "full_text": "This model is quite fun to use and insanely powerful.\n\nPlease check it out — with the right prompting, it’s an absolute beast for many use-cases.\n\nDemo here: https://t.co/gBWVmYSTMI", "in_reply_to_screen_name": "matts<PERSON>er_", "in_reply_to_status_id_str": "1831767031735374222", "in_reply_to_user_id_str": "1194889317388374016", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 4, "reply_count": 16, "retweet_count": 27, "retweeted": false, "user_id_str": "1194889317388374016", "id_str": "1831767033509240892"}, "twe_private_fields": {"created_at": 1725562268000, "updated_at": 1748554149587, "media_count": 0}}}]