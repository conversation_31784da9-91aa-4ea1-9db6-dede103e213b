[{"id": "1549431129714970624", "created_at": "2022-07-19 19:29:03 +03:00", "full_text": "🚨 Guide to Sports Analytics 📊\n\nIn addition to 200+ R resources, new sports-focused Python links are now included! Over 100+ python tutorials, 30+ packages, 25+ accounts to follow, 10 cheatsheets, and several free books &amp; blogs now all in one place !! ⬇️\n\nhttps://t.co/Y4il0Rxqcc https://t.co/e1yUfqv8fw", "media": [{"type": "photo", "url": "https://t.co/e1yUfqv8fw", "thumbnail": "https://pbs.twimg.com/media/FYCvZdnUIAIwCxD?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FYCvZdnUIAIwCxD?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2937, "retweet_count": 407, "bookmark_count": 2185, "quote_count": 35, "reply_count": 45, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1549431129714970624", "metadata": {"__typename": "Tweet", "rest_id": "1549431129714970624", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDQ4Njk1Njk0", "rest_id": "**********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915539958460256256/hsMa5Vbt_normal.jpg"}, "core": {"created_at": "Wed May 22 11:33:08 +0000 2013", "name": "<PERSON>", "screen_name": "DSamangy"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Basketball Analytics Coordinator - @PelicansNBA // Formerly: @RazorbackMBB 🐗 & @Cuse_MBB 🍊 // Sport Analytics ‘22 @SyracuseU // @ChelseaFC fan", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/domsamangy", "expanded_url": "https://linktr.ee/domsamangy", "url": "https://t.co/8PiZi2Mdfn", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7652, "followers_count": 10270, "friends_count": 909, "has_custom_timelines": true, "is_translator": false, "listed_count": 175, "media_count": 711, "normal_followers_count": 10270, "pinned_tweet_ids_str": ["1836155364259078250"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/**********/1733812662", "profile_interstitial_type": "", "statuses_count": 4610, "translator_type": "none", "url": "https://t.co/8PiZi2Mdfn", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Pittsburgh, PA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1549431129714970624"], "editable_until_msecs": "1658249943000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2185, "bookmarked": true, "created_at": "<PERSON><PERSON> 19 16:29:03 +0000 2022", "conversation_id_str": "1549431129714970624", "display_text_range": [0, 282], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/e1yUfqv8fw", "expanded_url": "https://x.com/DSamangy/status/1549431129714970624/photo/1", "id_str": "1549431123796566018", "indices": [283, 306], "media_key": "3_1549431123796566018", "media_url_https": "https://pbs.twimg.com/media/FYCvZdnUIAIwCxD.jpg", "type": "photo", "url": "https://t.co/e1yUfqv8fw", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1120, "w": 2048, "resize": "fit"}, "medium": {"h": 656, "w": 1200, "resize": "fit"}, "small": {"h": 372, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1120, "width": 2048, "focus_rects": [{"x": 0, "y": 0, "w": 2000, "h": 1120}, {"x": 0, "y": 0, "w": 1120, "h": 1120}, {"x": 0, "y": 0, "w": 982, "h": 1120}, {"x": 0, "y": 0, "w": 560, "h": 1120}, {"x": 0, "y": 0, "w": 2048, "h": 1120}]}, "media_results": {"result": {"media_key": "3_1549431123796566018"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "docs.google.com/spreadsheets/d…", "expanded_url": "https://docs.google.com/spreadsheets/d/16Xvhl7fCKEs1JTr-VXPZDmctO2gq4TcmuNmAhoHQQs0/edit#gid=1488324227", "url": "https://t.co/Y4il0Rxqcc", "indices": [259, 282]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/e1yUfqv8fw", "expanded_url": "https://x.com/DSamangy/status/1549431129714970624/photo/1", "id_str": "1549431123796566018", "indices": [283, 306], "media_key": "3_1549431123796566018", "media_url_https": "https://pbs.twimg.com/media/FYCvZdnUIAIwCxD.jpg", "type": "photo", "url": "https://t.co/e1yUfqv8fw", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1120, "w": 2048, "resize": "fit"}, "medium": {"h": 656, "w": 1200, "resize": "fit"}, "small": {"h": 372, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1120, "width": 2048, "focus_rects": [{"x": 0, "y": 0, "w": 2000, "h": 1120}, {"x": 0, "y": 0, "w": 1120, "h": 1120}, {"x": 0, "y": 0, "w": 982, "h": 1120}, {"x": 0, "y": 0, "w": 560, "h": 1120}, {"x": 0, "y": 0, "w": 2048, "h": 1120}]}, "media_results": {"result": {"media_key": "3_1549431123796566018"}}}]}, "favorite_count": 2937, "favorited": false, "full_text": "🚨 Guide to Sports Analytics 📊\n\nIn addition to 200+ R resources, new sports-focused Python links are now included! Over 100+ python tutorials, 30+ packages, 25+ accounts to follow, 10 cheatsheets, and several free books &amp; blogs now all in one place !! ⬇️\n\nhttps://t.co/Y4il0Rxqcc https://t.co/e1yUfqv8fw", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 35, "reply_count": 45, "retweet_count": 407, "retweeted": false, "user_id_str": "**********", "id_str": "1549431129714970624"}, "twe_private_fields": {"created_at": *************, "updated_at": *************, "media_count": 1}}}, {"id": "1549794477631488000", "created_at": "2022-07-20 19:32:51 +03:00", "full_text": "@coinflip_fpl PPG from previous seasons isn't recorded on the FPL website afaik.\n\nGenerally I use https://t.co/piBhxmRtOj as my guide, but I think you might be looking for something that doesn't exist 🤔🥝.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1549791153578541060", "retweeted_status": null, "quoted_status": null, "favorite_count": 1, "retweet_count": 0, "bookmark_count": 1, "quote_count": 0, "reply_count": 1, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1549794477631488000", "metadata": {"__typename": "Tweet", "rest_id": "1549794477631488000", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjc2NDY4MTI0MDExOTQ1OTg0", "rest_id": "1276468124011945984", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1741051922767855616/GML7gvd7_normal.jpg"}, "core": {"created_at": "Fri Jun 26 10:51:38 +0000 2020", "name": "The FPL Kiwi", "screen_name": "theFPLkiwi"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Former member of the analytic illuminati, FPL-modelling 🥝 //\nPeaked in 21/22 1.8k, MD 61st //\n24/25 AE100 qualifier, ID 11699", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 46184, "followers_count": 6215, "friends_count": 716, "has_custom_timelines": true, "is_translator": false, "listed_count": 152, "media_count": 2082, "normal_followers_count": 6215, "pinned_tweet_ids_str": ["1427544173134163974"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1276468124011945984/1593289075", "profile_interstitial_type": "", "statuses_count": 11955, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1549794477631488000"], "editable_until_msecs": "1658336571000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1, "bookmarked": true, "created_at": "Wed Jul 20 16:32:51 +0000 2022", "conversation_id_str": "1549790816717217792", "display_text_range": [14, 204], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "medium.com/@frenzelts/fan…", "expanded_url": "https://medium.com/@frenzelts/fantasy-premier-league-api-endpoints-a-detailed-guide-acbd5598eb19", "url": "https://t.co/piBhxmRtOj", "indices": [98, 121]}], "user_mentions": []}, "favorite_count": 1, "favorited": false, "full_text": "@coinflip_fpl PPG from previous seasons isn't recorded on the FPL website afaik.\n\nGenerally I use https://t.co/piBhxmRtOj as my guide, but I think you might be looking for something that doesn't exist 🤔🥝.", "in_reply_to_screen_name": "coinflipgoesbrr", "in_reply_to_status_id_str": "1549791153578541060", "in_reply_to_user_id_str": "1175709723506675713", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "1276468124011945984", "id_str": "1549794477631488000"}, "twe_private_fields": {"created_at": 1658334771000, "updated_at": 1748554255574, "media_count": 0}}}]