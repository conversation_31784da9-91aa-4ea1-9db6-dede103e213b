[{"id": "1460893668919988228", "created_at": "2021-11-17 11:52:47 +03:00", "full_text": "Ok what’s next, I’m done learning web development? Of course the next thing is getting jobs and pay day, and you can get them below \n\n&gt;&gt; upwork .com\n&gt;&gt; millo. co\n&gt;&gt; codingninjas .com\n&gt;&gt; solidgigs .com \n&gt;&gt; gun .up\n&gt;&gt; jobs .metafilter .com\n&gt;&gt; peopleperhour .com\n\nShare this 👍", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 511, "retweet_count": 353, "bookmark_count": 295, "quote_count": 2, "reply_count": 15, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1460893668919988228", "metadata": {"__typename": "Tweet", "rest_id": "1460893668919988228", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NjI4NTQwNDU1NTQ3NjE3Mjg=", "rest_id": "962854045554761728", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1782189450371481600/l3c_DbTT_normal.jpg"}, "core": {"created_at": "Mon Feb 12 01:01:08 +0000 2018", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Tech, Politics, MUFC, CEO @swiftspeedapp, Built @aptlearn_io", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "akinolaakeem.com", "expanded_url": "https://akinolaakeem.com/", "url": "https://t.co/NjU38NhC5s", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 448602, "followers_count": 187045, "friends_count": 205, "has_custom_timelines": true, "is_translator": false, "listed_count": 409, "media_count": 1464, "normal_followers_count": 187045, "pinned_tweet_ids_str": ["1876192117367943526"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/962854045554761728/1735674003", "profile_interstitial_type": "", "statuses_count": 124016, "translator_type": "none", "url": "https://t.co/NjU38NhC5s", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Lagos, Nigeria"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1461266259945926662", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1460893668919988228"], "editable_until_msecs": "1637140967419", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 295, "bookmarked": true, "created_at": "Wed Nov 17 08:52:47 +0000 2021", "conversation_id_str": "1460893668919988228", "display_text_range": [0, 315], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 511, "favorited": false, "full_text": "Ok what’s next, I’m done learning web development? Of course the next thing is getting jobs and pay day, and you can get them below \n\n&gt;&gt; upwork .com\n&gt;&gt; millo. co\n&gt;&gt; codingninjas .com\n&gt;&gt; solidgigs .com \n&gt;&gt; gun .up\n&gt;&gt; jobs .metafilter .com\n&gt;&gt; peopleperhour .com\n\nShare this 👍", "is_quote_status": false, "lang": "en", "quote_count": 2, "reply_count": 15, "retweet_count": 353, "retweeted": false, "user_id_str": "962854045554761728", "id_str": "1460893668919988228"}, "twe_private_fields": {"created_at": 1637139167000, "updated_at": 1748554384387, "media_count": 0}}}, {"id": "1462027566693371910", "created_at": "2021-11-20 14:58:29 +03:00", "full_text": "Learn Web Development for free👇\n\nNotes📃\n→ w3 schools\n→ Mdn docs\n→ geeksforgeek\n\nYouTube🎥\n→ Traversy media\n→ Web dev simplified\n→ JavaScript Mastery\n\nWebsite🌐\n→ Freecodecamp\n→ Codecademy\n→ SoloLearn\n\nPractice💻\n→ Frontend Mentor\n→ TestDome\n→ Create Projects", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2944, "retweet_count": 862, "bookmark_count": 1562, "quote_count": 16, "reply_count": 42, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1462027566693371910", "metadata": {"__typename": "Tweet", "rest_id": "1462027566693371910", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTYxNTU1OTI1NzgxNTg1OTI=", "rest_id": "856155592578158592", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1612507803842932736/PLiJMD_l_normal.jpg"}, "core": {"created_at": "Sun Apr 23 14:39:34 +0000 2017", "name": "<PERSON><PERSON><PERSON>", "screen_name": "Prathku<PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I talk about web and social • DevRel @APILayer • Building https://t.co/niju9j3UA2 & https://t.co/TxBXHrPKDu • Prev @Rapid_API @HyperspaceAI", "entities": {"description": {"urls": [{"display_url": "TrioTech.Dev", "expanded_url": "http://TrioTech.Dev", "url": "https://t.co/niju9j3UA2", "indices": [58, 81]}, {"display_url": "RoastProfile.Social", "expanded_url": "http://RoastProfile.Social", "url": "https://t.co/TxBXHrPKDu", "indices": [84, 107]}]}, "url": {"urls": [{"display_url": "PrathamKumar.com", "expanded_url": "https://www.PrathamKumar.com", "url": "https://t.co/gGjMe9QuIY", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 148812, "followers_count": 435982, "friends_count": 891, "has_custom_timelines": true, "is_translator": false, "listed_count": 8454, "media_count": 5769, "normal_followers_count": 435982, "pinned_tweet_ids_str": ["1917933782612496654"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/856155592578158592/1696346737", "profile_interstitial_type": "", "statuses_count": 34425, "translator_type": "none", "url": "https://t.co/gGjMe9QuIY", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "New Delhi, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1473320674487721988", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "", "patreon_handle": ""}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1462027566693371910"], "editable_until_msecs": "1637411309715", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://tweethunter.io\" rel=\"nofollow\">Tweet Hunter Pro</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1562, "bookmarked": true, "created_at": "Sat Nov 20 11:58:29 +0000 2021", "conversation_id_str": "1462027566693371910", "display_text_range": [0, 255], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2944, "favorited": false, "full_text": "Learn Web Development for free👇\n\nNotes📃\n→ w3 schools\n→ Mdn docs\n→ geeksforgeek\n\nYouTube🎥\n→ Traversy media\n→ Web dev simplified\n→ JavaScript Mastery\n\nWebsite🌐\n→ Freecodecamp\n→ Codecademy\n→ SoloLearn\n\nPractice💻\n→ Frontend Mentor\n→ TestDome\n→ Create Projects", "is_quote_status": false, "lang": "en", "quote_count": 16, "reply_count": 42, "retweet_count": 862, "retweeted": false, "user_id_str": "856155592578158592", "id_str": "1462027566693371910"}, "twe_private_fields": {"created_at": 1637409509000, "updated_at": 1748554384387, "media_count": 0}}}]