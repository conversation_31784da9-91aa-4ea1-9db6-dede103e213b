# Bookmarks from bookmark_chunk_3_part_02.json

## Category: Technology / Web Development / Tools / Chrome Extensions / Productivity / Resources

*   **Author:** <PERSON> (@mdjunaidap)
    **Date:** 2022-05-24 15:40:09 +03:00
    **Content:** 17 Top Best Chrome Extensions For Web Developers.

    Thread 
    **URL:** [https://twitter.com/mdjunaidap/status/1529079807711096832](https://twitter.com/mdjunaidap/status/1529079807711096832)

---

## Category: Education / Technology / Programming / JavaScript / TypeScript / ECMAScript / Books / Free Resources / Online Learning

*   **Author:** <PERSON> (@rauschma)
    **Date:** 2022-05-24 15:53:41 +03:00
    **Content:** My books are free to read online:

    JavaScript for impatient programmers: Learn ECMAScript 2022 via book, exercises, quizzes
    https://t.co/uXKdpnLngt

    Deep JavaScript: Go deeper
    https://t.co/tma31MtwA0

    Tackling TypeScript: From JS to TS
    https://t.co/yNjJugpoum https://t.co/1rZF0LDLuL
    **URL:** [https://twitter.com/rauschma/status/1529083214459023361](https://twitter.com/rauschma/status/1529083214459023361)
    **Linked Resources:**
    *   [JavaScript for impatient programmers (exploringjs.com)](https://exploringjs.com/impatient-js/)
    *   [Deep JavaScript (exploringjs.com)](https://exploringjs.com/deep-js/)
    *   [Tackling TypeScript (exploringjs.com)](https://exploringjs.com/tackling-ts/)
    **Image Alt Text:**
    *   Cover of the book JavaScript for impatient programmers (ES2022 edition) by Axel Rauschmayer. It shows a rhinoceros.
    *   Cover of the book Deep JavaScript by Axel Rauschmayer. It shows a freediver who swims under water and follows a rope down into the deep.
    *   Cover of the book Tackling TypeScript by Axel Rauschmayer. It shows a a pair of boxing gloves.

---
