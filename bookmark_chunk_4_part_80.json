[{"id": "1831814449344450887", "created_at": "2024-09-06 00:59:32 +03:00", "full_text": "F*ck it, all 900 <PERSON><PERSON><PERSON> career goals.\n\nEnjoy. 😮‍💨 https://t.co/6CDd8Mejug", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 130433, "retweet_count": 30867, "bookmark_count": 42092, "quote_count": 2931, "reply_count": 1272, "views_count": 10037702, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1831814449344450887", "metadata": {"__typename": "Tweet", "rest_id": "1831814449344450887", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzk1NDE5NjAwMTMxMzQ2NDMy", "rest_id": "1795419600131346432", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1806424949718138880/bySCOE36_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> May 28 11:39:33 +0000 2024", "name": "KM", "screen_name": "<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "@KMbappe and @RealMadrid fan account!", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 9296, "followers_count": 61455, "friends_count": 569, "has_custom_timelines": false, "is_translator": false, "listed_count": 73, "media_count": 2434, "normal_followers_count": 61455, "pinned_tweet_ids_str": ["1812890413700903423"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1795419600131346432/**********", "profile_interstitial_type": "", "statuses_count": 2931, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1831814449344450887"], "editable_until_msecs": "1725577172000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "10037702", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 42092, "bookmarked": true, "created_at": "Thu Sep 05 21:59:32 +0000 2024", "conversation_id_str": "1831814449344450887", "display_text_range": [0, 84], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/KylianMedia1/s…", "expanded_url": "https://x.com/KylianMedia1/status/1831812735765823741/video/1", "url": "https://t.co/6CDd8Mejug", "indices": [61, 84]}], "user_mentions": []}, "favorite_count": 130433, "favorited": false, "full_text": "F*ck it, all 900 <PERSON><PERSON><PERSON> career goals.\n\nEnjoy. 😮‍💨 https://t.co/6CDd8Mejug", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2931, "reply_count": 1272, "retweet_count": 30867, "retweeted": false, "user_id_str": "1795419600131346432", "id_str": "1831814449344450887"}, "twe_private_fields": {"created_at": 1725573572000, "updated_at": 1748554149587, "media_count": 0}}}, {"id": "1834208916747813183", "created_at": "2024-09-12 15:34:18 +03:00", "full_text": "Object oriented programming in Python, clearly explained:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1997, "retweet_count": 303, "bookmark_count": 3584, "quote_count": 10, "reply_count": 19, "views_count": 74, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1834208916747813183", "metadata": {"__typename": "Tweet", "rest_id": "1834208916747813183", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MDM2MDE5NzI=", "rest_id": "703601972", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1578327351544360960/YFpWSWIX_normal.jpg"}, "core": {"created_at": "Wed Jul 18 18:58:39 +0000 2012", "name": "<PERSON><PERSON><PERSON> 🚀", "screen_name": "akshay_pachaar"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Simplifying LLMs, AI Agents, RAGs and Machine Learning for you! • Co-founder @dailydoseofds_• BITS Pilani • 3 Patents • ex-AI Engineer @ LightningAI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "join.dailydoseofds.com", "expanded_url": "http://join.dailydoseofds.com", "url": "https://t.co/TLsKA1fohN", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 20351, "followers_count": 206136, "friends_count": 470, "has_custom_timelines": true, "is_translator": false, "listed_count": 2743, "media_count": 3895, "normal_followers_count": 206136, "pinned_tweet_ids_str": ["1724322731376918560"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/703601972/1733646485", "profile_interstitial_type": "", "statuses_count": 17580, "translator_type": "none", "url": "https://t.co/TLsKA1fohN", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Learn AI Engineering 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1476572433905717251", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1834208916747813183"], "editable_until_msecs": "1726148058000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "74", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3584, "bookmarked": true, "created_at": "Thu Sep 12 12:34:18 +0000 2024", "conversation_id_str": "1834208916747813183", "display_text_range": [0, 57], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1997, "favorited": false, "full_text": "Object oriented programming in Python, clearly explained:", "is_quote_status": false, "lang": "en", "quote_count": 10, "reply_count": 19, "retweet_count": 303, "retweeted": false, "user_id_str": "703601972", "id_str": "1834208916747813183"}, "twe_private_fields": {"created_at": 1726144458000, "updated_at": 1748554149587, "media_count": 0}}}]