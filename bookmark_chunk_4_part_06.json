[{"id": "1608447829680455687", "created_at": "2022-12-29 16:00:20 +03:00", "full_text": "If you are a Python developer, pay special attention to the following libraries:\n\n• NumPy: Scientific computing\n• Pandas: Data analysis and manipulation\n• Seaborn: Data visualization\n\nIf you want to go into machine learning, your experience here will give you a head start.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2040, "retweet_count": 371, "bookmark_count": 670, "quote_count": 6, "reply_count": 34, "views_count": 211104, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1608447829680455687", "metadata": {"__typename": "Tweet", "rest_id": "1608447829680455687", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODczNzAzOQ==", "rest_id": "18737039", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1581385027757264898/j5GjtUiq_normal.jpg"}, "core": {"created_at": "Wed Jan 07 20:35:01 +0000 2009", "name": "Santiago", "screen_name": "svpino"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Computer scientist. I teach hard-core AI/ML Engineering at https://t.co/THCAAZcBMu. YouTube: https://t.co/pROi08OZYJ", "entities": {"description": {"urls": [{"display_url": "ml.school", "expanded_url": "https://ml.school", "url": "https://t.co/THCAAZcBMu", "indices": [59, 82]}, {"display_url": "youtube.com/@underfitted", "expanded_url": "https://youtube.com/@underfitted", "url": "https://t.co/pROi08OZYJ", "indices": [93, 116]}]}, "url": {"urls": [{"display_url": "svpino.com/collaborations", "expanded_url": "https://www.svpino.com/collaborations", "url": "https://t.co/Tap9QFPUsU", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 57039, "followers_count": 409732, "friends_count": 504, "has_custom_timelines": true, "is_translator": false, "listed_count": 6815, "media_count": 4088, "normal_followers_count": 409732, "pinned_tweet_ids_str": ["1610984481342771200"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/18737039/1691437734", "profile_interstitial_type": "", "statuses_count": 45441, "translator_type": "none", "url": "https://t.co/Tap9QFPUsU", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🇺🇸 Collaborations →"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1576908081350561792", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "slvpino"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1608447829680455687"], "editable_until_msecs": "1672320620000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "211104", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 670, "bookmarked": true, "created_at": "Thu Dec 29 13:00:20 +0000 2022", "conversation_id_str": "1608447829680455687", "display_text_range": [0, 273], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2040, "favorited": false, "full_text": "If you are a Python developer, pay special attention to the following libraries:\n\n• NumPy: Scientific computing\n• Pandas: Data analysis and manipulation\n• Seaborn: Data visualization\n\nIf you want to go into machine learning, your experience here will give you a head start.", "is_quote_status": false, "lang": "en", "quote_count": 6, "reply_count": 34, "retweet_count": 371, "retweeted": false, "user_id_str": "18737039", "id_str": "1608447829680455687"}, "twe_private_fields": {"created_at": 1672318820000, "updated_at": 1748554204513, "media_count": 0}}}, {"id": "1608462233536974849", "created_at": "2022-12-29 16:57:34 +03:00", "full_text": "AI tools that didn't exist one year ago:\n\nChatGPT\nWhisper\nGPT-3\nCodex\nGitHub Copilot\nInstructGPT\nText-to-product\nAI slides\nDALLE + API\nMidjourney\nStable Diffusion\nRunway videos\nEmail AI\nAI chrome extensions\nReplit Ghostwriter\nNo-code AI app builders\nBen's bites 😉\n\nwhat else?", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 35272, "retweet_count": 8084, "bookmark_count": 17345, "quote_count": 289, "reply_count": 637, "views_count": 3851157, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1608462233536974849", "metadata": {"__typename": "Tweet", "rest_id": "1608462233536974849", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1MzE3NTQ0MQ==", "rest_id": "53175441", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1878086921726943233/vOx1kjeP_normal.jpg"}, "core": {"created_at": "Thu Jul 02 20:20:46 +0000 2009", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "twin dad || tinkerer/investor https://t.co/iLpIJT2Vlg || founder makerpad (acq by zapier)", "entities": {"description": {"urls": [{"display_url": "bensbites.com", "expanded_url": "http://bensbites.com", "url": "https://t.co/iLpIJT2Vlg", "indices": [30, 53]}]}, "url": {"urls": [{"display_url": "bensbites.com", "expanded_url": "https://bensbites.com", "url": "https://t.co/8PA6dEGhGE", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 132464, "followers_count": 157878, "friends_count": 405, "has_custom_timelines": true, "is_translator": false, "listed_count": 3410, "media_count": 2161, "normal_followers_count": 157878, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_interstitial_type": "", "statuses_count": 26060, "translator_type": "none", "url": "https://t.co/8PA6dEGhGE", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "online"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1620181560820375552", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1608462233536974849"], "editable_until_msecs": "1672324054000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "3851157", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 17345, "bookmarked": true, "created_at": "Thu Dec 29 13:57:34 +0000 2022", "conversation_id_str": "1608462233536974849", "display_text_range": [0, 275], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 35272, "favorited": false, "full_text": "AI tools that didn't exist one year ago:\n\nChatGPT\nWhisper\nGPT-3\nCodex\nGitHub Copilot\nInstructGPT\nText-to-product\nAI slides\nDALLE + API\nMidjourney\nStable Diffusion\nRunway videos\nEmail AI\nAI chrome extensions\nReplit Ghostwriter\nNo-code AI app builders\nBen's bites 😉\n\nwhat else?", "is_quote_status": false, "lang": "en", "quote_count": 289, "reply_count": 637, "retweet_count": 8084, "retweeted": false, "user_id_str": "53175441", "id_str": "1608462233536974849"}, "twe_private_fields": {"created_at": 1672322254000, "updated_at": 1748554204513, "media_count": 0}}}]