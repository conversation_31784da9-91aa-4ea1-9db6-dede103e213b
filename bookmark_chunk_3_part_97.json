[{"id": "1601463649020821504", "created_at": "2022-12-10 09:27:42 +03:00", "full_text": "Learn Programming For Free👇 https://t.co/nhffZOviqy", "media": [{"type": "photo", "url": "https://t.co/nhffZOviqy", "thumbnail": "https://pbs.twimg.com/media/FjmKsp1WYAE-l9P?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FjmKsp1WYAE-l9P?format=jpg&name=orig", "ext_alt_text": "Learn Programming For Free"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3434, "retweet_count": 1078, "bookmark_count": 1218, "quote_count": 10, "reply_count": 64, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1601463649020821504", "metadata": {"__typename": "Tweet", "rest_id": "1601463649020821504", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTA5Njg1Nzc1MjU1ODA1OTU3", "rest_id": "1509685775255805957", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1573897372408901637/Q8g6SXFM_normal.jpg"}, "core": {"created_at": "Fri Apr 01 00:16:46 +0000 2022", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Co-Founder of Level Up Coding", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "blog.levelupcoding.com", "expanded_url": "https://blog.levelupcoding.com", "url": "https://t.co/xFTt5hE4XG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 36538, "followers_count": 139073, "friends_count": 301, "has_custom_timelines": true, "is_translator": false, "listed_count": 2832, "media_count": 865, "normal_followers_count": 139073, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1509685775255805957/1738560550", "profile_interstitial_type": "", "statuses_count": 10556, "translator_type": "none", "url": "https://t.co/xFTt5hE4XG", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Level Up Coding Newsletter →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1601463649020821504"], "editable_until_msecs": "1670655462000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1218, "bookmarked": true, "created_at": "Sat Dec 10 06:27:42 +0000 2022", "conversation_id_str": "1601463649020821504", "display_text_range": [0, 27], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/nhffZOviqy", "expanded_url": "https://x.com/ChrisStaud/status/1601463649020821504/photo/1", "ext_alt_text": "Learn Programming For Free", "id_str": "1601463642255417345", "indices": [28, 51], "media_key": "3_1601463642255417345", "media_url_https": "https://pbs.twimg.com/media/FjmKsp1WYAE-l9P.jpg", "type": "photo", "url": "https://t.co/nhffZOviqy", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 1043, "y": 315, "h": 531, "w": 531}, {"x": 21, "y": 1079, "h": 619, "w": 619}]}, "medium": {"faces": [{"x": 611, "y": 185, "h": 311, "w": 311}, {"x": 12, "y": 632, "h": 363, "w": 363}]}, "small": {"faces": [{"x": 346, "y": 104, "h": 176, "w": 176}, {"x": 7, "y": 358, "h": 205, "w": 205}]}, "orig": {"faces": [{"x": 1656, "y": 501, "h": 844, "w": 844}, {"x": 34, "y": 1713, "h": 983, "w": 983}]}}, "sizes": {"large": {"h": 2048, "w": 1890, "resize": "fit"}, "medium": {"h": 1200, "w": 1108, "resize": "fit"}, "small": {"h": 680, "w": 628, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 3250, "width": 3000, "focus_rects": [{"x": 0, "y": 216, "w": 3000, "h": 1680}, {"x": 0, "y": 0, "w": 3000, "h": 3000}, {"x": 75, "y": 0, "w": 2851, "h": 3250}, {"x": 688, "y": 0, "w": 1625, "h": 3250}, {"x": 0, "y": 0, "w": 3000, "h": 3250}]}, "media_results": {"result": {"media_key": "3_1601463642255417345"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/nhffZOviqy", "expanded_url": "https://x.com/ChrisStaud/status/1601463649020821504/photo/1", "ext_alt_text": "Learn Programming For Free", "id_str": "1601463642255417345", "indices": [28, 51], "media_key": "3_1601463642255417345", "media_url_https": "https://pbs.twimg.com/media/FjmKsp1WYAE-l9P.jpg", "type": "photo", "url": "https://t.co/nhffZOviqy", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 1043, "y": 315, "h": 531, "w": 531}, {"x": 21, "y": 1079, "h": 619, "w": 619}]}, "medium": {"faces": [{"x": 611, "y": 185, "h": 311, "w": 311}, {"x": 12, "y": 632, "h": 363, "w": 363}]}, "small": {"faces": [{"x": 346, "y": 104, "h": 176, "w": 176}, {"x": 7, "y": 358, "h": 205, "w": 205}]}, "orig": {"faces": [{"x": 1656, "y": 501, "h": 844, "w": 844}, {"x": 34, "y": 1713, "h": 983, "w": 983}]}}, "sizes": {"large": {"h": 2048, "w": 1890, "resize": "fit"}, "medium": {"h": 1200, "w": 1108, "resize": "fit"}, "small": {"h": 680, "w": 628, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 3250, "width": 3000, "focus_rects": [{"x": 0, "y": 216, "w": 3000, "h": 1680}, {"x": 0, "y": 0, "w": 3000, "h": 3000}, {"x": 75, "y": 0, "w": 2851, "h": 3250}, {"x": 688, "y": 0, "w": 1625, "h": 3250}, {"x": 0, "y": 0, "w": 3000, "h": 3250}]}, "media_results": {"result": {"media_key": "3_1601463642255417345"}}}]}, "favorite_count": 3434, "favorited": false, "full_text": "Learn Programming For Free👇 https://t.co/nhffZOviqy", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 10, "reply_count": 64, "retweet_count": 1078, "retweeted": false, "user_id_str": "1509685775255805957", "id_str": "1601463649020821504"}, "twe_private_fields": {"created_at": 1670653662000, "updated_at": 1748554206264, "media_count": 1}}}, {"id": "1603696103396085760", "created_at": "2022-12-16 13:18:40 +03:00", "full_text": "@Cheerio95460970 @nixcraft I wrote books on GNU grep, sed and awk (among others) with plenty of examples and exercises. Free to read online (https://t.co/zJjdRuVXjf).\n\nI'd also highly recommend maintaining a cheatsheet for your common use cases (would be quicker than searching docs, books, etc).", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 253, "retweet_count": 40, "bookmark_count": 91, "quote_count": 3, "reply_count": 8, "views_count": 8406, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1603696103396085760", "metadata": {"__typename": "Tweet", "rest_id": "1603696103396085760", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTgwNjE4OTUw", "rest_id": "3180618950", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1438031676395851777/1yFWNGl1_normal.jpg"}, "core": {"created_at": "Thu Apr 30 13:19:08 +0000 2015", "name": "<PERSON><PERSON><PERSON> 🌞", "screen_name": "learn_byexample"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "I tweet about Regex, Python, Linux CLI one-liners, Vim and interesting tech nuggets.\n\n✍ Author of several programming ebooks and devourer of fantasy books 🧙", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "learnbyexample.gumroad.com/l/all-books/Ha…", "expanded_url": "https://learnbyexample.gumroad.com/l/all-books/HappyPrice", "url": "https://t.co/f7wwjyLKgW", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 32820, "followers_count": 3483, "friends_count": 142, "has_custom_timelines": true, "is_translator": false, "listed_count": 102, "media_count": 766, "normal_followers_count": 3483, "pinned_tweet_ids_str": ["1927025325990355064"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/3180618950/1658987496", "profile_interstitial_type": "", "statuses_count": 13193, "translator_type": "none", "url": "https://t.co/f7wwjyLKgW", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/zJjdRuVXjf", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 200, "width": 400, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": ":notebook: Books, reference guides and resources on Regular Expressions, CLI one-liners, Scripting Languages and Vim. - learnbyexample/scripting_course", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 600, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=600x600"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 72, "width": 144, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": ":notebook: Books, reference guides and resources on Regular Expressions, CLI one-liners, Scripting Languages and Vim. - learnbyexample/scripting_course", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": ":notebook: Books, reference guides and resources on Regular Expressions, CLI one-liners, Scripting Languages and Vim. - learnbyexample/scripting_course", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 91.81}, {"rgb": {"blue": 75, "green": 158, "red": 25}, "percentage": 2.8}, {"rgb": {"blue": 81, "green": 224, "red": 137}, "percentage": 1.25}, {"rgb": {"blue": 51, "green": 47, "red": 39}, "percentage": 0.96}, {"rgb": {"blue": 152, "green": 237, "red": 185}, "percentage": 0.62}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "GitHub - learnbyexample/scripting_course: :notebook: Books, reference guides and resources on...", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 91.81}, {"rgb": {"blue": 75, "green": 158, "red": 25}, "percentage": 2.8}, {"rgb": {"blue": 81, "green": 224, "red": 137}, "percentage": 1.25}, {"rgb": {"blue": 51, "green": 47, "red": 39}, "percentage": 0.96}, {"rgb": {"blue": 152, "green": 237, "red": 185}, "percentage": 0.62}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 91.81}, {"rgb": {"blue": 75, "green": 158, "red": 25}, "percentage": 2.8}, {"rgb": {"blue": 81, "green": 224, "red": 137}, "percentage": 1.25}, {"rgb": {"blue": 51, "green": 47, "red": 39}, "percentage": 0.96}, {"rgb": {"blue": 152, "green": 237, "red": 185}, "percentage": 0.62}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/zJjdRuVXjf", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927874877287190529/jrFLbqf8?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/zJjdRuVXjf", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620142, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17800, "media_count": 2646, "normal_followers_count": 2620142, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1603696103396085760"], "editable_until_msecs": "1671187720000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "8406", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 91, "bookmarked": true, "created_at": "Fri Dec 16 10:18:40 +0000 2022", "conversation_id_str": "1603686468194824192", "display_text_range": [27, 296], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/learnbyexample…", "expanded_url": "https://github.com/learnbyexample/scripting_course#ebooks", "url": "https://t.co/zJjdRuVXjf", "indices": [141, 164]}], "user_mentions": [{"id_str": "17484680", "name": "nixCraft 🐧", "screen_name": "nixcraft", "indices": [17, 26]}]}, "favorite_count": 253, "favorited": false, "full_text": "@Cheerio95460970 @nixcraft I wrote books on GNU grep, sed and awk (among others) with plenty of examples and exercises. Free to read online (https://t.co/zJjdRuVXjf).\n\nI'd also highly recommend maintaining a cheatsheet for your common use cases (would be quicker than searching docs, books, etc).", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 3, "reply_count": 8, "retweet_count": 40, "retweeted": false, "user_id_str": "3180618950", "id_str": "1603696103396085760"}, "twe_private_fields": {"created_at": 1671185920000, "updated_at": 1748554206264, "media_count": 0}}}]