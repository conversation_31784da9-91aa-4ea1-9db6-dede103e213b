[{"id": "1532685767163641856", "created_at": "2022-06-03 14:28:57 +03:00", "full_text": "How did I learn JavaScript in a month and created a Blockchain from the ground up? 🚀\n\nI'm going to reveal my secrets🤩\n\nThread 🧵👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 663, "retweet_count": 171, "bookmark_count": 384, "quote_count": 4, "reply_count": 18, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532685767163641856", "metadata": {"__typename": "Tweet", "rest_id": "1532685767163641856", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDA3MTQxNDc0MDU2MTQ2OTQ2", "rest_id": "1407141474056146946", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1581466300886433793/8QRK8Ao9_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Jun 22 01:02:09 +0000 2021", "name": "<PERSON><PERSON> | nextbunny.co", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Building https://t.co/u9hrnWNBaa - #Nextjs drag & drop builder", "entities": {"description": {"urls": [{"display_url": "nextbunny.co", "expanded_url": "https://nextbunny.co", "url": "https://t.co/u9hrnWNBaa", "indices": [9, 32]}]}, "url": {"urls": [{"display_url": "m.youtube.com/@NextbunnyUI", "expanded_url": "https://m.youtube.com/@NextbunnyUI", "url": "https://t.co/Srrs7PpK7f", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 9316, "followers_count": 8677, "friends_count": 573, "has_custom_timelines": true, "is_translator": false, "listed_count": 170, "media_count": 861, "normal_followers_count": 8677, "pinned_tweet_ids_str": ["1925962883516571798"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1407141474056146946/1738123133", "profile_interstitial_type": "", "statuses_count": 6230, "translator_type": "none", "url": "https://t.co/Srrs7PpK7f", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Subscribe to stay tuned ⬇️"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1462214535079727111", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532685767163641856"], "editable_until_msecs": "1654257537000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 384, "bookmarked": true, "created_at": "Fri Jun 03 11:28:57 +0000 2022", "conversation_id_str": "1532685767163641856", "display_text_range": [0, 128], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 663, "favorited": false, "full_text": "How did I learn JavaScript in a month and created a Blockchain from the ground up? 🚀\n\nI'm going to reveal my secrets🤩\n\nThread 🧵👇", "is_quote_status": false, "lang": "en", "quote_count": 4, "reply_count": 18, "retweet_count": 171, "retweeted": false, "user_id_str": "1407141474056146946", "id_str": "1532685767163641856"}, "twe_private_fields": {"created_at": 1654255737000, "updated_at": 1748554267520, "media_count": 0}}}, {"id": "1533019943276453888", "created_at": "2022-06-04 12:36:51 +03:00", "full_text": "🔸Let's make a Sign-up/Sign-in Form Animation in 6 Simple Steps using CSS🔸\n\nA Thread🧵⬇️ https://t.co/cmJSkGr9kb", "media": [{"type": "video", "url": "https://t.co/cmJSkGr9kb", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1533014552828182528/pu/img/ldjALOLoFef8BCBr.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/1280x720/rU7hbFkn-WRp2CsG.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2482, "retweet_count": 581, "bookmark_count": 1199, "quote_count": 24, "reply_count": 113, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1533019943276453888", "metadata": {"__typename": "Tweet", "rest_id": "1533019943276453888", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjM0NTMzNTk3MTQ0NDgxNzkz", "rest_id": "1234533597144481793", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1536045260253515776/BNiSS_c1_normal.jpg"}, "core": {"created_at": "Mon Mar 02 17:38:52 +0000 2020", "name": "<PERSON><PERSON><PERSON>", "screen_name": "RitikaAgrawal08"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Full Stack Developer | Simplifying Web Development with articles and infographics ✨️ • DM for collaboration ✉️", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "folll.io/ritikaagrawal08", "expanded_url": "https://folll.io/ritikaagrawal08", "url": "https://t.co/TCYG1WPO6s", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91364, "followers_count": 12543, "friends_count": 532, "has_custom_timelines": true, "is_translator": false, "listed_count": 162, "media_count": 2172, "normal_followers_count": 12543, "pinned_tweet_ids_str": ["1928066295305752737"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1234533597144481793/1697438130", "profile_interstitial_type": "", "statuses_count": 19966, "translator_type": "none", "url": "https://t.co/TCYG1WPO6s", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Find me →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1533019943276453888"], "editable_until_msecs": "1654337211000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1199, "bookmarked": true, "created_at": "Sat Jun 04 09:36:51 +0000 2022", "conversation_id_str": "1533019943276453888", "display_text_range": [0, 86], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/cmJSkGr9kb", "expanded_url": "https://x.com/RitikaAgrawal08/status/1533019943276453888/video/1", "id_str": "1533014552828182528", "indices": [87, 110], "media_key": "7_1533014552828182528", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1533014552828182528/pu/img/ldjALOLoFef8BCBr.jpg", "type": "video", "url": "https://t.co/cmJSkGr9kb", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 864, "w": 1536, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 864, "width": 1536, "focus_rects": []}, "video_info": {"aspect_ratio": [16, 9], "duration_millis": 8600, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/pl/XNS_9poGpnReg7qx.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/480x270/e8ApnM8o-_evt5lj.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/640x360/s0HmL-U6eKk7Ia6x.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/1280x720/rU7hbFkn-WRp2CsG.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1533014552828182528"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/cmJSkGr9kb", "expanded_url": "https://x.com/RitikaAgrawal08/status/1533019943276453888/video/1", "id_str": "1533014552828182528", "indices": [87, 110], "media_key": "7_1533014552828182528", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1533014552828182528/pu/img/ldjALOLoFef8BCBr.jpg", "type": "video", "url": "https://t.co/cmJSkGr9kb", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 864, "w": 1536, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 864, "width": 1536, "focus_rects": []}, "video_info": {"aspect_ratio": [16, 9], "duration_millis": 8600, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/pl/XNS_9poGpnReg7qx.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/480x270/e8ApnM8o-_evt5lj.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/640x360/s0HmL-U6eKk7Ia6x.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1533014552828182528/pu/vid/1280x720/rU7hbFkn-WRp2CsG.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1533014552828182528"}}}]}, "favorite_count": 2482, "favorited": false, "full_text": "🔸Let's make a Sign-up/Sign-in Form Animation in 6 Simple Steps using CSS🔸\n\nA Thread🧵⬇️ https://t.co/cmJSkGr9kb", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 24, "reply_count": 113, "retweet_count": 581, "retweeted": false, "user_id_str": "1234533597144481793", "id_str": "1533019943276453888"}, "twe_private_fields": {"created_at": 1654335411000, "updated_at": 1748554267520, "media_count": 1}}}]