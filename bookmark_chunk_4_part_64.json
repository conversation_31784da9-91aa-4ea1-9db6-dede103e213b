[{"id": "1798576261432016978", "created_at": "2024-06-06 07:42:51 +03:00", "full_text": "Someone requested a new \"nano11\", not sure if anyone remembers it... But, it's basically half of a regular Windows 11 install size and removes features such as Windows Defender, Microsoft Edge and most of WinSxS folder.\nIntended for low end PCs.\n\nDownload: https://t.co/khKidixnOH https://t.co/XYUfImGVJS", "media": [{"type": "photo", "url": "https://t.co/XYUfImGVJS", "thumbnail": "https://pbs.twimg.com/media/GPXTk0jbcAA5ypX?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GPXTk0jbcAA5ypX?format=png&name=orig", "ext_alt_text": "Screenshot of nano11 based off Windows 11 Build 26227, showing 8 GB of disk space used."}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 310, "retweet_count": 30, "bookmark_count": 115, "quote_count": 6, "reply_count": 32, "views_count": 21080, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1798576261432016978", "metadata": {"__typename": "Tweet", "rest_id": "1798576261432016978", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTg1NzA4MDc2", "rest_id": "3185708076", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1741311490932523010/ACp5CwE1_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> May 05 01:50:07 +0000 2015", "name": "BobPony.com", "screen_name": "TheBobPony"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "all you’ll see is tech here, that’s all.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "BobPony.com", "expanded_url": "https://BobPony.com", "url": "https://t.co/1UIO8hjW2Z", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6, "followers_count": 11809, "friends_count": 7, "has_custom_timelines": true, "is_translator": false, "listed_count": 45, "media_count": 5273, "normal_followers_count": 11809, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/3185708076/1715666975", "profile_interstitial_type": "", "statuses_count": 11931, "translator_type": "none", "url": "https://t.co/1UIO8hjW2Z", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Right here, right now."}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1458079259944161290", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1798576261432016978"], "editable_until_msecs": "1717652571000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "21080", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com\" rel=\"nofollow\">Twitter Web Client</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 115, "bookmarked": true, "created_at": "Thu Jun 06 04:42:51 +0000 2024", "conversation_id_str": "1798576261432016978", "display_text_range": [0, 280], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/XYUfImGVJS", "expanded_url": "https://x.com/TheBobPony/status/1798576261432016978/photo/1", "ext_alt_text": "Screenshot of nano11 based off Windows 11 Build 26227, showing 8 GB of disk space used.", "id_str": "1798576255740375040", "indices": [281, 304], "media_key": "3_1798576255740375040", "media_url_https": "https://pbs.twimg.com/media/GPXTk0jbcAA5ypX.png", "type": "photo", "url": "https://t.co/XYUfImGVJS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 600, "w": 800, "resize": "fit"}, "medium": {"h": 600, "w": 800, "resize": "fit"}, "small": {"h": 510, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 600, "width": 800, "focus_rects": [{"x": 0, "y": 0, "w": 800, "h": 448}, {"x": 200, "y": 0, "w": 600, "h": 600}, {"x": 274, "y": 0, "w": 526, "h": 600}, {"x": 430, "y": 0, "w": 300, "h": 600}, {"x": 0, "y": 0, "w": 800, "h": 600}]}, "media_results": {"result": {"media_key": "3_1798576255740375040"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "mega.nz/file/fNlERKSZ#…", "expanded_url": "https://mega.nz/file/fNlERKSZ#I3SUbLlF0shqhWC8JLIR4v8JYDuCP45fifbAt1G2ruA", "url": "https://t.co/khKidixnOH", "indices": [257, 280]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/XYUfImGVJS", "expanded_url": "https://x.com/TheBobPony/status/1798576261432016978/photo/1", "ext_alt_text": "Screenshot of nano11 based off Windows 11 Build 26227, showing 8 GB of disk space used.", "id_str": "1798576255740375040", "indices": [281, 304], "media_key": "3_1798576255740375040", "media_url_https": "https://pbs.twimg.com/media/GPXTk0jbcAA5ypX.png", "type": "photo", "url": "https://t.co/XYUfImGVJS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 600, "w": 800, "resize": "fit"}, "medium": {"h": 600, "w": 800, "resize": "fit"}, "small": {"h": 510, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 600, "width": 800, "focus_rects": [{"x": 0, "y": 0, "w": 800, "h": 448}, {"x": 200, "y": 0, "w": 600, "h": 600}, {"x": 274, "y": 0, "w": 526, "h": 600}, {"x": 430, "y": 0, "w": 300, "h": 600}, {"x": 0, "y": 0, "w": 800, "h": 600}]}, "media_results": {"result": {"media_key": "3_1798576255740375040"}}}]}, "favorite_count": 310, "favorited": false, "full_text": "Someone requested a new \"nano11\", not sure if anyone remembers it... But, it's basically half of a regular Windows 11 install size and removes features such as Windows Defender, Microsoft Edge and most of WinSxS folder.\nIntended for low end PCs.\n\nDownload: https://t.co/khKidixnOH https://t.co/XYUfImGVJS", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 32, "retweet_count": 30, "retweeted": false, "user_id_str": "3185708076", "id_str": "1798576261432016978"}, "twe_private_fields": {"created_at": 1717648971000, "updated_at": 1748554172119, "media_count": 1}}}, {"id": "1799450111191495070", "created_at": "2024-06-08 17:35:13 +03:00", "full_text": "Build a LLM app with RAG to chat with PDF files using Gemini Flash in just 4 lines of Python code (step-by-step instructions):", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 276, "retweet_count": 48, "bookmark_count": 462, "quote_count": 0, "reply_count": 3, "views_count": 58207, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1799450111191495070", "metadata": {"__typename": "Tweet", "rest_id": "1799450111191495070", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDIwMjkwNTIyNzI4Mzk0NzU3", "rest_id": "1420290522728394757", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1777698745108606976/NH-IIitW_normal.jpg"}, "core": {"created_at": "Wed Jul 28 07:51:34 +0000 2021", "name": "<PERSON><PERSON><PERSON>", "screen_name": "Sumanth_077"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Simplifying LLMs, RAG, Machine Learning & AI Agents for you! • ML Developer Advocate • Shipping Open Source AI apps", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "aiengineering.beehiiv.com", "expanded_url": "http://aiengineering.beehiiv.com/", "url": "https://t.co/vuVWdaeVPk", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16058, "followers_count": 68725, "friends_count": 861, "has_custom_timelines": true, "is_translator": false, "listed_count": 1301, "media_count": 1383, "normal_followers_count": 68725, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1420290522728394757/1735031628", "profile_interstitial_type": "", "statuses_count": 9631, "translator_type": "none", "url": "https://t.co/vuVWdaeVPk", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "AI Engineering →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1517096801475522561", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1799450111191495070"], "editable_until_msecs": "1717860913000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "58207", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 462, "bookmarked": true, "created_at": "Sat Jun 08 14:35:13 +0000 2024", "conversation_id_str": "1799450111191495070", "display_text_range": [0, 126], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 276, "favorited": false, "full_text": "Build a LLM app with RAG to chat with PDF files using Gemini Flash in just 4 lines of Python code (step-by-step instructions):", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 3, "retweet_count": 48, "retweeted": false, "user_id_str": "1420290522728394757", "id_str": "1799450111191495070"}, "twe_private_fields": {"created_at": 1717857313000, "updated_at": 1748554172118, "media_count": 0}}}]