[{"id": "1562453579453300737", "created_at": "2022-08-24 17:55:36 +03:00", "full_text": "Learn from my favorite resources - \n\n1. Python - https://t.co/lB0cHFIAI9\n\n3. HTML - https://t.co/9P4Ei0NgGT\n\n4. CSS  -    https://t.co/naaWc7Q9T0\n\n5. React - https://t.co/Du0CPhGQBn\n\n6. Node - https://t.co/MO9KnPlOUF\n\n7. MERN <PERSON>ack - https://t.co/12YeUNtUBo", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4195, "retweet_count": 1466, "bookmark_count": 3608, "quote_count": 15, "reply_count": 77, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1562453579453300737", "metadata": {"__typename": "Tweet", "rest_id": "1562453579453300737", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMDIzNjU3NjgzOTk0NTMzODg5", "rest_id": "1023657683994533889", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1650848540732854275/gAno3VWD_normal.jpg"}, "core": {"created_at": "Sun Jul 29 19:53:05 +0000 2018", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "fahee<PERSON>_khan_dev"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Software Engineer\n\nFull Stack Developer. Technical Writer✍\nWorks with JS, React, Flutter & SQL.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "faheemkhan97.hashnode.dev", "expanded_url": "http://faheemkhan97.hashnode.dev", "url": "https://t.co/CzWMbBsn7p", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 39051, "followers_count": 22212, "friends_count": 763, "has_custom_timelines": true, "is_translator": false, "listed_count": 499, "media_count": 1002, "normal_followers_count": 22212, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1023657683994533889/1708078019", "profile_interstitial_type": "", "statuses_count": 23267, "translator_type": "none", "url": "https://t.co/CzWMbBsn7p", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Delhi / Bihar"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1562453579453300737"], "editable_until_msecs": "1661354736000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3608, "bookmarked": true, "created_at": "Wed Aug 24 14:55:36 +0000 2022", "conversation_id_str": "1562453579453300737", "display_text_range": [0, 257], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "shorturl.at/fimv1", "expanded_url": "http://shorturl.at/fimv1", "url": "https://t.co/MO9KnPlOUF", "indices": [193, 216]}, {"display_url": "shorturl.at/FJVX1", "expanded_url": "http://shorturl.at/FJVX1", "url": "https://t.co/12YeUNtUBo", "indices": [234, 257]}, {"display_url": "css-tricks.com/learn-css/", "expanded_url": "https://css-tricks.com/learn-css/", "url": "https://t.co/naaWc7Q9T0", "indices": [122, 145]}, {"display_url": "shorturl.at/enpX6", "expanded_url": "http://shorturl.at/enpX6", "url": "https://t.co/Du0CPhGQBn", "indices": [158, 181]}, {"display_url": "w3schools.com/html/", "expanded_url": "https://www.w3schools.com/html/", "url": "https://t.co/9P4Ei0NgGT", "indices": [84, 107]}, {"display_url": "shorturl.at/CMQR9", "expanded_url": "http://shorturl.at/CMQR9", "url": "https://t.co/lB0cHFIAI9", "indices": [49, 72]}], "user_mentions": []}, "favorite_count": 4195, "favorited": false, "full_text": "Learn from my favorite resources - \n\n1. Python - https://t.co/lB0cHFIAI9\n\n3. HTML - https://t.co/9P4Ei0NgGT\n\n4. CSS  -    https://t.co/naaWc7Q9T0\n\n5. React - https://t.co/Du0CPhGQBn\n\n6. Node - https://t.co/MO9KnPlOUF\n\n7. MERN <PERSON>ack - https://t.co/12YeUNtUBo", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 15, "reply_count": 77, "retweet_count": 1466, "retweeted": false, "user_id_str": "1023657683994533889", "id_str": "1562453579453300737"}, "twe_private_fields": {"created_at": 1661352936000, "updated_at": 1748554240582, "media_count": 0}}}, {"id": "1562483627384270853", "created_at": "2022-08-24 19:55:00 +03:00", "full_text": "Stop spreading Rangnick revisionism, he was a liar. https://t.co/yMV9RbYce6", "media": [{"type": "photo", "url": "https://t.co/yMV9RbYce6", "thumbnail": "https://pbs.twimg.com/media/Fa8Okx3WYAA2Ctk?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Fa8Okx3WYAA2Ctk?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": "1562459809118830596", "favorite_count": 118, "retweet_count": 30, "bookmark_count": 2, "quote_count": 0, "reply_count": 1, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1562483627384270853", "metadata": {"__typename": "Tweet", "rest_id": "1562483627384270853", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDE5MzA3MDUyMzMwMDk4Njk3", "rest_id": "1419307052330098697", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1445424338417631235/ecJjc4KM_normal.jpg"}, "core": {"created_at": "Sun Jul 25 14:42:36 +0000 2021", "name": "Man<PERSON>er", "screen_name": "__<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 286713, "followers_count": 2775, "friends_count": 294, "has_custom_timelines": true, "is_translator": false, "listed_count": 16, "media_count": 4224, "normal_followers_count": 2775, "pinned_tweet_ids_str": ["1763582942947815673"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1419307052330098697/1694368690", "profile_interstitial_type": "", "statuses_count": 38962, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "‎ "}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1562483627384270853"], "editable_until_msecs": "1661361900000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "quoted_status_result": {"result": {"__typename": "Tweet", "rest_id": "1562459809118830596", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjIyMjM5MzM3NTQ4NDM1NDU2", "rest_id": "1222239337548435456", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/TheHQQ", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1830390880861147137/xO64SwcY_bigger.jpg"}, "description": "The Headquarters", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1531275838364307456/c09EuRtq_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 28 19:25:50 +0000 2020", "name": "centredevils.", "screen_name": "centredevils"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "📲 An all-round coverage of Manchester United | Contact: <EMAIL> | @thehqq", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "centredevils.co.uk", "expanded_url": "http://centredevils.co.uk", "url": "https://t.co/XxTaGXuAv5", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 252, "followers_count": 502980, "friends_count": 129, "has_custom_timelines": true, "is_translator": false, "listed_count": 1294, "media_count": 30365, "normal_followers_count": 502980, "pinned_tweet_ids_str": ["1839756537562038697"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1222239337548435456/1734295868", "profile_interstitial_type": "", "statuses_count": 57842, "translator_type": "none", "url": "https://t.co/XxTaGXuAv5", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "London, England"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1474366476521930753", "professional_type": "Business", "category": [{"id": 1009, "name": "Community", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1562459809118830596"], "editable_until_msecs": "1661356222000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 8, "bookmarked": false, "created_at": "Wed Aug 24 15:20:22 +0000 2022", "conversation_id_str": "1562459809118830596", "display_text_range": [0, 194], "entities": {"hashtags": [{"indices": [48, 53], "text": "mufc"}], "media": [{"display_url": "pic.x.com/iHD36sIyR6", "expanded_url": "https://x.com/centredevils/status/1562459809118830596/photo/1", "id_str": "1562459805427580928", "indices": [195, 218], "media_key": "3_1562459805427580928", "media_url_https": "https://pbs.twimg.com/media/Fa746i5WQAABweP.jpg", "type": "photo", "url": "https://t.co/iHD36sIyR6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 52, "y": 463, "h": 133, "w": 133}, {"x": 179, "y": 159, "h": 402, "w": 402}]}, "medium": {"faces": [{"x": 52, "y": 463, "h": 133, "w": 133}, {"x": 179, "y": 159, "h": 402, "w": 402}]}, "small": {"faces": [{"x": 32, "y": 291, "h": 83, "w": 83}, {"x": 112, "y": 100, "h": 253, "w": 253}]}, "orig": {"faces": [{"x": 52, "y": 463, "h": 133, "w": 133}, {"x": 179, "y": 159, "h": 402, "w": 402}]}}, "sizes": {"large": {"h": 1080, "w": 865, "resize": "fit"}, "medium": {"h": 1080, "w": 865, "resize": "fit"}, "small": {"h": 680, "w": 545, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 865, "focus_rects": [{"x": 0, "y": 163, "w": 865, "h": 484}, {"x": 0, "y": 0, "w": 865, "h": 865}, {"x": 0, "y": 0, "w": 865, "h": 986}, {"x": 81, "y": 0, "w": 540, "h": 1080}, {"x": 0, "y": 0, "w": 865, "h": 1080}]}, "media_results": {"result": {"media_key": "3_1562459805427580928"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/iHD36sIyR6", "expanded_url": "https://x.com/centredevils/status/1562459809118830596/photo/1", "id_str": "1562459805427580928", "indices": [195, 218], "media_key": "3_1562459805427580928", "media_url_https": "https://pbs.twimg.com/media/Fa746i5WQAABweP.jpg", "type": "photo", "url": "https://t.co/iHD36sIyR6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 52, "y": 463, "h": 133, "w": 133}, {"x": 179, "y": 159, "h": 402, "w": 402}]}, "medium": {"faces": [{"x": 52, "y": 463, "h": 133, "w": 133}, {"x": 179, "y": 159, "h": 402, "w": 402}]}, "small": {"faces": [{"x": 32, "y": 291, "h": 83, "w": 83}, {"x": 112, "y": 100, "h": 253, "w": 253}]}, "orig": {"faces": [{"x": 52, "y": 463, "h": 133, "w": 133}, {"x": 179, "y": 159, "h": 402, "w": 402}]}}, "sizes": {"large": {"h": 1080, "w": 865, "resize": "fit"}, "medium": {"h": 1080, "w": 865, "resize": "fit"}, "small": {"h": 680, "w": 545, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 865, "focus_rects": [{"x": 0, "y": 163, "w": 865, "h": 484}, {"x": 0, "y": 0, "w": 865, "h": 865}, {"x": 0, "y": 0, "w": 865, "h": 986}, {"x": 81, "y": 0, "w": 540, "h": 1080}, {"x": 0, "y": 0, "w": 865, "h": 1080}]}, "media_results": {"result": {"media_key": "3_1562459805427580928"}}}]}, "favorite_count": 3324, "favorited": false, "full_text": "Just a subtle reminder that <PERSON><PERSON>nick URGED #mufc to sign:\n\n▪️Julian <PERSON>\n▪️Luis <PERSON>\n▪️Dusan <PERSON> \n\nOnly for all three of his requests to be turned down by our incompetent board…🤦‍♂️ https://t.co/iHD36sIyR6", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 57, "reply_count": 135, "retweet_count": 293, "retweeted": false, "user_id_str": "1222239337548435456", "id_str": "1562459809118830596"}}}, "legacy": {"bookmark_count": 2, "bookmarked": true, "created_at": "Wed Aug 24 16:55:00 +0000 2022", "conversation_id_str": "1562483627384270853", "display_text_range": [0, 51], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/yMV9RbYce6", "expanded_url": "https://x.com/__Manveer/status/1562483627384270853/photo/1", "id_str": "1562483620744421376", "indices": [52, 75], "media_key": "3_1562483620744421376", "media_url_https": "https://pbs.twimg.com/media/Fa8Okx3WYAA2Ctk.jpg", "type": "photo", "url": "https://t.co/yMV9RbYce6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 178, "y": 106, "h": 100, "w": 100}]}, "medium": {"faces": [{"x": 104, "y": 62, "h": 58, "w": 58}]}, "small": {"faces": [{"x": 59, "y": 35, "h": 33, "w": 33}]}, "orig": {"faces": [{"x": 178, "y": 106, "h": 100, "w": 100}]}}, "sizes": {"large": {"h": 2048, "w": 2048, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2048, "width": 2048, "focus_rects": [{"x": 0, "y": 195, "w": 2048, "h": 1147}, {"x": 0, "y": 0, "w": 2048, "h": 2048}, {"x": 0, "y": 0, "w": 1796, "h": 2048}, {"x": 51, "y": 0, "w": 1024, "h": 2048}, {"x": 0, "y": 0, "w": 2048, "h": 2048}]}, "media_results": {"result": {"media_key": "3_1562483620744421376"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/yMV9RbYce6", "expanded_url": "https://x.com/__Manveer/status/1562483627384270853/photo/1", "id_str": "1562483620744421376", "indices": [52, 75], "media_key": "3_1562483620744421376", "media_url_https": "https://pbs.twimg.com/media/Fa8Okx3WYAA2Ctk.jpg", "type": "photo", "url": "https://t.co/yMV9RbYce6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 178, "y": 106, "h": 100, "w": 100}]}, "medium": {"faces": [{"x": 104, "y": 62, "h": 58, "w": 58}]}, "small": {"faces": [{"x": 59, "y": 35, "h": 33, "w": 33}]}, "orig": {"faces": [{"x": 178, "y": 106, "h": 100, "w": 100}]}}, "sizes": {"large": {"h": 2048, "w": 2048, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2048, "width": 2048, "focus_rects": [{"x": 0, "y": 195, "w": 2048, "h": 1147}, {"x": 0, "y": 0, "w": 2048, "h": 2048}, {"x": 0, "y": 0, "w": 1796, "h": 2048}, {"x": 51, "y": 0, "w": 1024, "h": 2048}, {"x": 0, "y": 0, "w": 2048, "h": 2048}]}, "media_results": {"result": {"media_key": "3_1562483620744421376"}}}]}, "favorite_count": 118, "favorited": false, "full_text": "Stop spreading Rangnick revisionism, he was a liar. https://t.co/yMV9RbYce6", "is_quote_status": true, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "quoted_status_id_str": "1562459809118830596", "quoted_status_permalink": {"url": "https://t.co/D1CgUZArIs", "expanded": "https://twitter.com/centredevils/status/1562459809118830596", "display": "x.com/centredevils/s…"}, "reply_count": 1, "retweet_count": 30, "retweeted": false, "user_id_str": "1419307052330098697", "id_str": "1562483627384270853"}, "twe_private_fields": {"created_at": 1661360100000, "updated_at": 1748554240582, "media_count": 1}}}]