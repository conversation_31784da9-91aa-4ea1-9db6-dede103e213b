import json
import os
import math

def split_json_file(input_filepath, num_splits=20):
    """
    Reads a JSON file containing a list of objects and splits it into smaller files.

    Args:
        input_filepath (str): The path to the input JSON file.
        num_splits (int): The number of smaller files to split the data into.
    """
    try:
        with open(input_filepath, 'r') as f:
            data = json.load(f)

        if not isinstance(data, list):
            print(f"Error: Input file {input_filepath} does not contain a JSON array.")
            return

        total_items = len(data)
        if total_items == 0:
            print(f"Warning: Input file {input_filepath} is empty.")
            return

        items_per_split = math.ceil(total_items / num_splits)

        input_filename = os.path.basename(input_filepath)
        filename_base, file_extension = os.path.splitext(input_filename)

        for i in range(num_splits):
            start_index = i * items_per_split
            end_index = min((i + 1) * items_per_split, total_items)
            
            if start_index >= total_items:
                break # Stop if we have already covered all items

            sub_data = data[start_index:end_index]

            output_filename = f"{filename_base}_part_{i+1:02d}{file_extension}"
            output_filepath = os.path.join(os.path.dirname(input_filepath), output_filename)

            with open(output_filepath, 'w') as outfile:
                json.dump(sub_data, outfile, indent=2)

            print(f"Created {output_filepath} with {len(sub_data)} items.")

    except FileNotFoundError:
        print(f"Error: Input file not found at {input_filepath}")
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {input_filepath}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    input_files = [f"bookmark_chunk_{i}.json" for i in range(1, 6)]
    num_sub_files = 100

    for input_file in input_files:
        print(f"Splitting {input_file}...")
        split_json_file(input_file, num_sub_files)
        print("-" * 20)
