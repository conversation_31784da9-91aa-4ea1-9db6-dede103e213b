[{"id": "1550195441546969088", "created_at": "2022-07-21 22:06:09 +03:00", "full_text": "Nunez 4 goals https://t.co/rIHLaxvHqy", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5276, "retweet_count": 509, "bookmark_count": 327, "quote_count": 96, "reply_count": 51, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1550195441546969088", "metadata": {"__typename": "Tweet", "rest_id": "1550195441546969088", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDYyNzc4ODYxNTA3MzAxMzg2", "rest_id": "1462778861507301386", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1608812416174784512/1FytMwhO_normal.jpg"}, "core": {"created_at": "Mon Nov 22 13:44:42 +0000 2021", "name": "G", "screen_name": "Gideoomatic"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The 1955'ers", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 5219, "followers_count": 38365, "friends_count": 914, "has_custom_timelines": true, "is_translator": false, "listed_count": 249, "media_count": 26094, "normal_followers_count": 38365, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1462778861507301386/1658876557", "profile_interstitial_type": "", "statuses_count": 205612, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1550195441546969088"], "editable_until_msecs": "1658432169000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 327, "bookmarked": true, "created_at": "Thu Jul 21 19:06:09 +0000 2022", "conversation_id_str": "1550195441546969088", "display_text_range": [0, 37], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/chr1s_cpfc/sta…", "expanded_url": "https://x.com/chr1s_cpfc/status/1505578771981684746/video/1", "url": "https://t.co/rIHLaxvHqy", "indices": [14, 37]}], "user_mentions": []}, "favorite_count": 5276, "favorited": false, "full_text": "Nunez 4 goals https://t.co/rIHLaxvHqy", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 96, "reply_count": 51, "retweet_count": 509, "retweeted": false, "user_id_str": "1462778861507301386", "id_str": "1550195441546969088"}, "twe_private_fields": {"created_at": 1658430369000, "updated_at": 1748554262696, "media_count": 0}}}, {"id": "1550353175781470208", "created_at": "2022-07-22 08:32:55 +03:00", "full_text": "@denicmarko ++ For Complete Python, Start here : https://t.co/izJ5kxEv1t", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1550087191254138881", "retweeted_status": null, "quoted_status": null, "favorite_count": 15, "retweet_count": 3, "bookmark_count": 5, "quote_count": 0, "reply_count": 0, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1550353175781470208", "metadata": {"__typename": "Tweet", "rest_id": "1550353175781470208", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjI5OTkxNjU5ODEyNzA0MjU3", "rest_id": "1229991659812704257", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1379560061727477762/J_vZJfyO_normal.jpg"}, "core": {"created_at": "Wed Feb 19 04:50:46 +0000 2020", "name": "<PERSON><PERSON>", "screen_name": "NainaChaturved8"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "World Traveler, <PERSON><PERSON>,Researcher <PERSON>,ACM,Competitive Programmer,Google's WTM,Goldman Sachs 10K Women,Coursera Instructor,IITB,<PERSON> hopper,53 countries", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "naina0412.medium.com", "expanded_url": "https://naina0412.medium.com/", "url": "https://t.co/9WbC7tvUsL", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10095, "followers_count": 6811, "friends_count": 4285, "has_custom_timelines": true, "is_translator": false, "listed_count": 116, "media_count": 1547, "normal_followers_count": 6811, "pinned_tweet_ids_str": ["1649603613503700992"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1229991659812704257/1620489614", "profile_interstitial_type": "", "statuses_count": 11430, "translator_type": "none", "url": "https://t.co/9WbC7tvUsL", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🇺🇸 🇸🇬 🇦🇺 🌎"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/izJ5kxEv1t", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.82306", "type": "STRING"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 150, "width": 200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=280x150"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Deep dive into Important topics, code, resources and implementations…", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "medium.datadriveninvestor.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 400, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 899, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "895255995961663492", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 75, "width": 100, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=100x100"}, "type": "IMAGE"}}, {"key": "app_num_ratings", "value": {"string_value": "148,385", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 899, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 899, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "medium.datadriveninvestor.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "app_name", "value": {"string_value": "Medium: Read & Write Stories", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 41, "green": 35, "red": 30}, "percentage": 75.07}, {"rgb": {"blue": 124, "green": 116, "red": 105}, "percentage": 12.3}, {"rgb": {"blue": 107, "green": 74, "red": 22}, "percentage": 3.43}, {"rgb": {"blue": 224, "green": 203, "red": 180}, "percentage": 1.86}, {"rgb": {"blue": 193, "green": 212, "red": 83}, "percentage": 1.22}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "60 days of Data Science and Machine Learning Series — Day 1", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 41, "green": 35, "red": 30}, "percentage": 75.07}, {"rgb": {"blue": 124, "green": 116, "red": 105}, "percentage": 12.3}, {"rgb": {"blue": 107, "green": 74, "red": 22}, "percentage": 3.43}, {"rgb": {"blue": 224, "green": 203, "red": 180}, "percentage": 1.86}, {"rgb": {"blue": 193, "green": 212, "red": 83}, "percentage": 1.22}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 899, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 41, "green": 35, "red": 30}, "percentage": 75.07}, {"rgb": {"blue": 124, "green": 116, "red": 105}, "percentage": 12.3}, {"rgb": {"blue": 107, "green": 74, "red": 22}, "percentage": 3.43}, {"rgb": {"blue": 224, "green": 203, "red": 180}, "percentage": 1.86}, {"rgb": {"blue": 193, "green": 212, "red": 83}, "percentage": 1.22}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 899, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/izJ5kxEv1t", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 899, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925747943757271041/qrkHB4kd?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/izJ5kxEv1t", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjo4OTUyNTU5OTU5NjE2NjM0OTI=", "rest_id": "895255995961663492", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/976315989993308161/Bobgl6Ag_normal.jpg"}, "core": {"created_at": "Wed Aug 09 12:10:37 +0000 2017", "name": "DataDrivenInvestor", "screen_name": "DDInvestorHQ"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "empowerment with data, knowledge, and expertise\n#tech #finance #entrepreneurship", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "datadriveninvestor.com", "expanded_url": "https://www.datadriveninvestor.com", "url": "https://t.co/GFOkBXzUXC", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1547, "followers_count": 2826, "friends_count": 865, "has_custom_timelines": true, "is_translator": false, "listed_count": 0, "media_count": 866, "normal_followers_count": 2826, "pinned_tweet_ids_str": ["1002112304845152256"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/895255995961663492/1502281250", "profile_interstitial_type": "", "statuses_count": 3021, "translator_type": "none", "url": "https://t.co/GFOkBXzUXC", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1690128357147775061", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1550353175781470208"], "editable_until_msecs": "1658469775000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5, "bookmarked": true, "created_at": "Fri Jul 22 05:32:55 +0000 2022", "conversation_id_str": "1550087191254138881", "display_text_range": [12, 72], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "medium.datadriveninvestor.com/60-days-of-dat…", "expanded_url": "https://medium.datadriveninvestor.com/60-days-of-data-science-and-machine-learning-series-day-1-a62ce3c7aac1?sk=081c29312cd10755e51a640ecd2baedf", "url": "https://t.co/izJ5kxEv1t", "indices": [49, 72]}], "user_mentions": [{"id_str": "202982523", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>", "indices": [0, 11]}]}, "favorite_count": 15, "favorited": false, "full_text": "@denicmarko ++ For Complete Python, Start here : https://t.co/izJ5kxEv1t", "in_reply_to_screen_name": "<PERSON><PERSON><PERSON><PERSON>", "in_reply_to_status_id_str": "1550087191254138881", "in_reply_to_user_id_str": "202982523", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 0, "retweet_count": 3, "retweeted": false, "user_id_str": "1229991659812704257", "id_str": "1550353175781470208"}, "twe_private_fields": {"created_at": 1658467975000, "updated_at": 1748554262696, "media_count": 0}}}]