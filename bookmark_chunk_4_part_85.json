[{"id": "1839379171715506280", "created_at": "2024-09-26 21:59:03 +03:00", "full_text": "As asked to me by my students: A few websites where you will find remote jobs and get paid in USD. Enjoy!\n\nangel dot co\nbehance dot net\ncloudpeeps dot com\ncontentwritingjobs dot com\ndesignhill dot com\ndremote dot io\nfiverr dot com\nflexjobs dot com\nfreelancer dot com\nglassdoor dot com\nguru dot com\nhired dot com\nhireable dot com\nhubstaff dot com\nindeed dot com\njobbatical dot com\njobspresso dot co\njooble dot org\njustremote dot co\nlinkedin dot com\nonlinejobs dot ph\nproblogger dot com\nremote dot co\nremoteok dot io\nremoteworkhub dot com\nremotive dot com\nsimplyhired dot com\nstackoverflow dot com slash jobs\ntalent dot com\ntaskrabbit dot com\nthebalancecareers dot com\nthemuse dot com\ntoptal dot com\nupwork dot com\nvirtualvocations dot com\nweworkremotely dot com\nworkingnomads dot com\nzirtual dot com", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 441, "retweet_count": 139, "bookmark_count": 736, "quote_count": 3, "reply_count": 31, "views_count": 14401, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1839379171715506280", "metadata": {"__typename": "Tweet", "rest_id": "1839379171715506280", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODA5Nzg2NA==", "rest_id": "18097864", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1441080656155451394/3wIEIY1Z_normal.jpg"}, "core": {"created_at": "Sat Dec 13 12:56:32 +0000 2008", "name": "Julien Barbier 🙃❤️🏴‍☠️ 七転び八起き", "screen_name": "julienbarbier42"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "https://t.co/1groW5G8Sm • Product #ALX_SE & AI #ALX_AI • co-founder @holbertonschool • OG team @docker", "entities": {"description": {"urls": [{"display_url": "forms.gle/rit4GGMhHu1QNp…", "expanded_url": "https://forms.gle/rit4GGMhHu1QNpb5A", "url": "https://t.co/1groW5G8Sm", "indices": [0, 23]}]}, "url": {"urls": [{"display_url": "shiporsink.net", "expanded_url": "http://www.shiporsink.net/", "url": "https://t.co/iCVNi1axTg", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 105792, "followers_count": 78725, "friends_count": 2810, "has_custom_timelines": true, "is_translator": false, "listed_count": 323, "media_count": 3213, "normal_followers_count": 78725, "pinned_tweet_ids_str": ["1927017132958597602"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/18097864/1668203251", "profile_interstitial_type": "", "statuses_count": 32669, "translator_type": "none", "url": "https://t.co/iCVNi1axTg", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "/etc/init.d"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1839379171715506280"], "editable_until_msecs": "1727380743000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "14401", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4MzkzNzkxNzE2MTQ4MDE5MjA=", "text": "As asked to me by my students: A few websites where you will find remote jobs and get paid in USD. Enjoy!\n\nangel dot co\nbehance dot net\ncloudpeeps dot com\ncontentwritingjobs dot com\ndesignhill dot com\ndremote dot io\nfiverr dot com\nflexjobs dot com\nfreelancer dot com\nglassdoor dot com\nguru dot com\nhired dot com\nhireable dot com\nhubstaff dot com\nindeed dot com\njobbatical dot com\njobspresso dot co\njooble dot org\njustremote dot co\nlinkedin dot com\nonlinejobs dot ph\nproblogger dot com\nremote dot co\nremoteok dot io\nremoteworkhub dot com\nremotive dot com\nsimplyhired dot com\nstackoverflow dot com slash jobs\ntalent dot com\ntaskrabbit dot com\nthebalancecareers dot com\nthemuse dot com\ntoptal dot com\nupwork dot com\nvirtualvocations dot com\nweworkremotely dot com\nworkingnomads dot com\nzirtual dot com", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 736, "bookmarked": true, "created_at": "Thu Sep 26 18:59:03 +0000 2024", "conversation_id_str": "1839379171715506280", "display_text_range": [0, 280], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 441, "favorited": false, "full_text": "As asked to me by my students: A few websites where you will find remote jobs and get paid in USD. Enjoy!\n\nangel dot co\nbehance dot net\ncloudpeeps dot com\ncontentwritingjobs dot com\ndesignhill dot com\ndremote dot io\nfiverr dot com\nflexjobs dot com\nfreelancer dot com\nglassdoor dot", "is_quote_status": false, "lang": "en", "quote_count": 3, "reply_count": 31, "retweet_count": 139, "retweeted": false, "user_id_str": "18097864", "id_str": "1839379171715506280"}, "twe_private_fields": {"created_at": 1727377143000, "updated_at": 1748554149587, "media_count": 0}}}, {"id": "1839665827018060006", "created_at": "2024-09-27 16:58:07 +03:00", "full_text": "Full body exercise 💪 https://t.co/FuK66DrVfT", "media": [{"type": "video", "url": "https://t.co/FuK66DrVfT", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1839665763311079424/pu/img/1nh7rBsr7sI0SR6R.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/720x1280/ayrKLlNCDhyag5wR.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4541, "retweet_count": 948, "bookmark_count": 5363, "quote_count": 2, "reply_count": 5, "views_count": 388992, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1839665827018060006", "metadata": {"__typename": "Tweet", "rest_id": "1839665827018060006", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzQ0ODkyOTM0MDg5NDQxMjgy", "rest_id": "1344892934089441282", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1390536079896322052/tywXphAl_normal.jpg"}, "core": {"created_at": "Fri Jan 01 06:27:41 +0000 2021", "name": "Aesthetic WorkOut", "screen_name": "aesthetics_way"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Aesthetics Physiques 🏋️ Bodybuilding Motivation 💪", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 633, "followers_count": 408340, "friends_count": 17, "has_custom_timelines": false, "is_translator": false, "listed_count": 2813, "media_count": 2136, "normal_followers_count": 408340, "pinned_tweet_ids_str": ["1921006885479514322"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1344892934089441282/**********", "profile_interstitial_type": "", "statuses_count": 2178, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "<PERSON><PERSON><PERSON> Account"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1839665827018060006"], "editable_until_msecs": "*************", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "388992", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5363, "bookmarked": true, "created_at": "Fri Sep 27 13:58:07 +0000 2024", "conversation_id_str": "1839665827018060006", "display_text_range": [0, 20], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/FuK66DrVfT", "expanded_url": "https://x.com/aesthetics_way/status/1839665827018060006/video/1", "id_str": "1839665763311079424", "indices": [21, 44], "media_key": "7_1839665763311079424", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1839665763311079424/pu/img/1nh7rBsr7sI0SR6R.jpg", "type": "video", "url": "https://t.co/FuK66DrVfT", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1280, "w": 720, "resize": "fit"}, "medium": {"h": 1200, "w": 675, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1280, "width": 720, "focus_rects": []}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 18367, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/pl/HrW0WGCKNsgo5vmG.m3u8?tag=12&v=cfc"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/320x568/8bOSJeylIapaQCk7.mp4?tag=12"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/480x852/p3IjZQJcS4tUjjE9.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/720x1280/ayrKLlNCDhyag5wR.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1839665763311079424"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/FuK66DrVfT", "expanded_url": "https://x.com/aesthetics_way/status/1839665827018060006/video/1", "id_str": "1839665763311079424", "indices": [21, 44], "media_key": "7_1839665763311079424", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1839665763311079424/pu/img/1nh7rBsr7sI0SR6R.jpg", "type": "video", "url": "https://t.co/FuK66DrVfT", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1280, "w": 720, "resize": "fit"}, "medium": {"h": 1200, "w": 675, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1280, "width": 720, "focus_rects": []}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 18367, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/pl/HrW0WGCKNsgo5vmG.m3u8?tag=12&v=cfc"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/320x568/8bOSJeylIapaQCk7.mp4?tag=12"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/480x852/p3IjZQJcS4tUjjE9.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1839665763311079424/pu/vid/avc1/720x1280/ayrKLlNCDhyag5wR.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1839665763311079424"}}}]}, "favorite_count": 4541, "favorited": false, "full_text": "Full body exercise 💪 https://t.co/FuK66DrVfT", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 5, "retweet_count": 948, "retweeted": false, "user_id_str": "1344892934089441282", "id_str": "1839665827018060006"}, "twe_private_fields": {"created_at": 1727445487000, "updated_at": 1748554144882, "media_count": 1}}}]