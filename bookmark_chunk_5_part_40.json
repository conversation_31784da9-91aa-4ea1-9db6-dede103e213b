[{"id": "1878165864412791032", "created_at": "2025-01-11 22:43:31 +03:00", "full_text": "Ubicloud is an open-source cloud platform that runs on bare metal providers like Hetzner and AWS, offering IaaS features, GitHub Actions integration, and managed services like PostgreSQL, with a focus on cost reduction and infrastructure control https://t.co/6Wn6l7ZXeY", "media": [{"type": "photo", "url": "https://t.co/6Wn6l7ZXeY", "thumbnail": "https://pbs.twimg.com/media/GhCV3B1WkAAbc2f?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GhCV3B1WkAAbc2f?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1125, "retweet_count": 110, "bookmark_count": 1278, "quote_count": 4, "reply_count": 15, "views_count": 100262, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1878165864412791032", "metadata": {"__typename": "Tweet", "rest_id": "1878165864412791032", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzI2MTgwNzU2MzEwMzMxMzk5", "rest_id": "1326180756310331399", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1905090420142379008/Ydq5So7B_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Nov 10 15:13:32 +0000 2020", "name": "<PERSON>", "screen_name": "tom_doerr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Follow for posts about GitHub repos, DSPy, and agents\nSubscribe for top posts\nDM to share your AI project (Due to volume of DMs I'll prioritize subscribers)", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "tom-doerr.github.io/repo_posts/", "expanded_url": "https://tom-doerr.github.io/repo_posts/", "url": "https://t.co/WreHYiW9xe", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 52917, "followers_count": 84560, "friends_count": 2135, "has_custom_timelines": true, "is_translator": false, "listed_count": 808, "media_count": 6792, "normal_followers_count": 84560, "pinned_tweet_ids_str": ["1886002942962024635"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1326180756310331399/1721336045", "profile_interstitial_type": "", "statuses_count": 20559, "translator_type": "none", "url": "https://t.co/WreHYiW9xe", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1878165864412791032"], "editable_until_msecs": "1736628211000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "100262", "state": "EnabledWithCount"}, "source": "<a href=\"https://composio.dev/\" rel=\"nofollow\">Composio</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1278, "bookmarked": true, "created_at": "Sat Jan 11 19:43:31 +0000 2025", "conversation_id_str": "1878165864412791032", "display_text_range": [0, 245], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/6Wn6l7ZXeY", "expanded_url": "https://x.com/tom_doerr/status/1878165864412791032/photo/1", "id_str": "1878165817231052800", "indices": [246, 269], "media_key": "3_1878165817231052800", "media_url_https": "https://pbs.twimg.com/media/GhCV3B1WkAAbc2f.png", "type": "photo", "url": "https://t.co/6Wn6l7ZXeY", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 476, "w": 732, "resize": "fit"}, "medium": {"h": 476, "w": 732, "resize": "fit"}, "small": {"h": 442, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 476, "width": 732, "focus_rects": [{"x": 0, "y": 66, "w": 732, "h": 410}, {"x": 0, "y": 0, "w": 476, "h": 476}, {"x": 0, "y": 0, "w": 418, "h": 476}, {"x": 0, "y": 0, "w": 238, "h": 476}, {"x": 0, "y": 0, "w": 732, "h": 476}]}, "media_results": {"result": {"media_key": "3_1878165817231052800"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/6Wn6l7ZXeY", "expanded_url": "https://x.com/tom_doerr/status/1878165864412791032/photo/1", "id_str": "1878165817231052800", "indices": [246, 269], "media_key": "3_1878165817231052800", "media_url_https": "https://pbs.twimg.com/media/GhCV3B1WkAAbc2f.png", "type": "photo", "url": "https://t.co/6Wn6l7ZXeY", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 476, "w": 732, "resize": "fit"}, "medium": {"h": 476, "w": 732, "resize": "fit"}, "small": {"h": 442, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 476, "width": 732, "focus_rects": [{"x": 0, "y": 66, "w": 732, "h": 410}, {"x": 0, "y": 0, "w": 476, "h": 476}, {"x": 0, "y": 0, "w": 418, "h": 476}, {"x": 0, "y": 0, "w": 238, "h": 476}, {"x": 0, "y": 0, "w": 732, "h": 476}]}, "media_results": {"result": {"media_key": "3_1878165817231052800"}}}]}, "favorite_count": 1125, "favorited": false, "full_text": "Ubicloud is an open-source cloud platform that runs on bare metal providers like Hetzner and AWS, offering IaaS features, GitHub Actions integration, and managed services like PostgreSQL, with a focus on cost reduction and infrastructure control https://t.co/6Wn6l7ZXeY", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 4, "reply_count": 15, "retweet_count": 110, "retweeted": false, "user_id_str": "1326180756310331399", "id_str": "1878165864412791032"}, "twe_private_fields": {"created_at": 1736624611000, "updated_at": 1748554112350, "media_count": 1}}}, {"id": "1878257125618569332", "created_at": "2025-01-12 04:46:09 +03:00", "full_text": "Open-source project providing a cloud-based Linux desktop environment controlled by large-scale language models, supporting multiple inference providers and cross-platform use https://t.co/jF23IbyAZJ", "media": [{"type": "photo", "url": "https://t.co/jF23IbyAZJ", "thumbnail": "https://pbs.twimg.com/media/GhDo4EJWUAAGTed?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GhDo4EJWUAAGTed?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 283, "retweet_count": 27, "bookmark_count": 312, "quote_count": 1, "reply_count": 4, "views_count": 22962, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1878257125618569332", "metadata": {"__typename": "Tweet", "rest_id": "1878257125618569332", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzI2MTgwNzU2MzEwMzMxMzk5", "rest_id": "1326180756310331399", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1905090420142379008/Ydq5So7B_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Nov 10 15:13:32 +0000 2020", "name": "<PERSON>", "screen_name": "tom_doerr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Follow for posts about GitHub repos, DSPy, and agents\nSubscribe for top posts\nDM to share your AI project (Due to volume of DMs I'll prioritize subscribers)", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "tom-doerr.github.io/repo_posts/", "expanded_url": "https://tom-doerr.github.io/repo_posts/", "url": "https://t.co/WreHYiW9xe", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 52917, "followers_count": 84560, "friends_count": 2135, "has_custom_timelines": true, "is_translator": false, "listed_count": 808, "media_count": 6792, "normal_followers_count": 84560, "pinned_tweet_ids_str": ["1886002942962024635"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1326180756310331399/1721336045", "profile_interstitial_type": "", "statuses_count": 20559, "translator_type": "none", "url": "https://t.co/WreHYiW9xe", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1878257125618569332"], "editable_until_msecs": "1736649969000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "22962", "state": "EnabledWithCount"}, "source": "<a href=\"https://composio.dev/\" rel=\"nofollow\">Composio</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 312, "bookmarked": true, "created_at": "Sun Jan 12 01:46:09 +0000 2025", "conversation_id_str": "1878257125618569332", "display_text_range": [0, 175], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/jF23IbyAZJ", "expanded_url": "https://x.com/tom_doerr/status/1878257125618569332/photo/1", "id_str": "1878257094496768000", "indices": [176, 199], "media_key": "3_1878257094496768000", "media_url_https": "https://pbs.twimg.com/media/GhDo4EJWUAAGTed.png", "type": "photo", "url": "https://t.co/jF23IbyAZJ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 690, "w": 712, "resize": "fit"}, "medium": {"h": 690, "w": 712, "resize": "fit"}, "small": {"h": 659, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 690, "width": 712, "focus_rects": [{"x": 0, "y": 281, "w": 712, "h": 399}, {"x": 0, "y": 0, "w": 690, "h": 690}, {"x": 0, "y": 0, "w": 605, "h": 690}, {"x": 0, "y": 0, "w": 345, "h": 690}, {"x": 0, "y": 0, "w": 712, "h": 690}]}, "media_results": {"result": {"media_key": "3_1878257094496768000"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/jF23IbyAZJ", "expanded_url": "https://x.com/tom_doerr/status/1878257125618569332/photo/1", "id_str": "1878257094496768000", "indices": [176, 199], "media_key": "3_1878257094496768000", "media_url_https": "https://pbs.twimg.com/media/GhDo4EJWUAAGTed.png", "type": "photo", "url": "https://t.co/jF23IbyAZJ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 690, "w": 712, "resize": "fit"}, "medium": {"h": 690, "w": 712, "resize": "fit"}, "small": {"h": 659, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 690, "width": 712, "focus_rects": [{"x": 0, "y": 281, "w": 712, "h": 399}, {"x": 0, "y": 0, "w": 690, "h": 690}, {"x": 0, "y": 0, "w": 605, "h": 690}, {"x": 0, "y": 0, "w": 345, "h": 690}, {"x": 0, "y": 0, "w": 712, "h": 690}]}, "media_results": {"result": {"media_key": "3_1878257094496768000"}}}]}, "favorite_count": 283, "favorited": false, "full_text": "Open-source project providing a cloud-based Linux desktop environment controlled by large-scale language models, supporting multiple inference providers and cross-platform use https://t.co/jF23IbyAZJ", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 4, "retweet_count": 27, "retweeted": false, "user_id_str": "1326180756310331399", "id_str": "1878257125618569332"}, "twe_private_fields": {"created_at": 1736646369000, "updated_at": 1748554112350, "media_count": 1}}}]