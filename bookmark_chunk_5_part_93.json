[{"id": "1924130890609041842", "created_at": "2025-05-18 18:52:07 +03:00", "full_text": "Wild buffalo weren’t confined to feedlots, injected with antibiotics, or bred to maximize fat content. They moved freely, naturally fertilized the land, and existed within balanced ecosystems. \n\nComparing them to mass-produced, industrial cattle is like comparing a natural forest to a monocrop plantation. One sustains life, the other depletes it. \n\nThe issue isn’t cows; it’s the industrial supply chain built for profit, not sustainability. But keep pretending the past mirrors the present while ignoring the science.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1923759334187737376", "retweeted_status": null, "quoted_status": null, "favorite_count": 95, "retweet_count": 11, "bookmark_count": 4, "quote_count": 0, "reply_count": 3, "views_count": 3785, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1924130890609041842", "metadata": {"__typename": "Tweet", "rest_id": "1924130890609041842", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDIwMDc0MDkwODg3NDA1NTY4", "rest_id": "1420074090887405568", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1924842401287569414/YBsNsgb__normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 27 17:30:30 +0000 2021", "name": "King Of Plebs", "screen_name": "7_pow_7"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Principles aren’t political, they’re a compass keeping you from being a mindless political parrot. Consistency is strength, and facts don’t bend to feelings.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 60074, "followers_count": 308, "friends_count": 696, "has_custom_timelines": true, "is_translator": false, "listed_count": 11, "media_count": 92, "normal_followers_count": 308, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1420074090887405568/1628354707", "profile_interstitial_type": "", "statuses_count": 1668, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "San Diego, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1924130890609041842"], "editable_until_msecs": "1747587127000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "3785", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE5MjQxMzA4OTA1MzM1Mjc1NTM=", "text": "Wild buffalo weren’t confined to feedlots, injected with antibiotics, or bred to maximize fat content. They moved freely, naturally fertilized the land, and existed within balanced ecosystems. \n\nComparing them to mass-produced, industrial cattle is like comparing a natural forest to a monocrop plantation. One sustains life, the other depletes it. \n\nThe issue isn’t cows; it’s the industrial supply chain built for profit, not sustainability. But keep pretending the past mirrors the present while ignoring the science.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 4, "bookmarked": true, "created_at": "Sun May 18 15:52:07 +0000 2025", "conversation_id_str": "1923759334187737376", "display_text_range": [13, 293], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "17599264", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON>", "indices": [0, 12]}]}, "favorite_count": 95, "favorited": false, "full_text": "@Mark_Sisson Wild buffalo weren’t confined to feedlots, injected with antibiotics, or bred to maximize fat content. They moved freely, naturally fertilized the land, and existed within balanced ecosystems. \n\nComparing them to mass-produced, industrial cattle is like comparing a natural forest", "in_reply_to_screen_name": "<PERSON><PERSON>", "in_reply_to_status_id_str": "1923759334187737376", "in_reply_to_user_id_str": "17599264", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 3, "retweet_count": 11, "retweeted": false, "user_id_str": "1420074090887405568", "id_str": "1924130890609041842"}, "twe_private_fields": {"created_at": 1747583527000, "updated_at": 1748553996861, "media_count": 0}}}, {"id": "1926536490022002718", "created_at": "2025-05-25 10:11:07 +03:00", "full_text": "Source: https://t.co/3qsn4wehs3\nRead More Low-Level Lore here: https://t.co/38wQ9Kg60j", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1926536187994386811", "retweeted_status": null, "quoted_status": null, "favorite_count": 11, "retweet_count": 5, "bookmark_count": 22, "quote_count": 0, "reply_count": 0, "views_count": 2996, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1926536490022002718", "metadata": {"__typename": "Tweet", "rest_id": "1926536490022002718", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTU2MzM4NDU3MTM1Njg1NjMz", "rest_id": "1556338457135685633", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1811143441625051136/E6CTVoX0_normal.jpg"}, "core": {"created_at": "Sun Aug 07 17:56:28 +0000 2022", "name": "<PERSON><PERSON>", "screen_name": "chessMan786"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "engineer | engineering | learning to learn the low-level system", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "discord.com/invite/2QWN9y3…", "expanded_url": "https://discord.com/invite/2QWN9y3B9M", "url": "https://t.co/rtE8lRomlr", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 4832, "followers_count": 26155, "friends_count": 363, "has_custom_timelines": false, "is_translator": false, "listed_count": 147, "media_count": 1682, "normal_followers_count": 26155, "pinned_tweet_ids_str": ["1923801604484223099"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1556338457135685633/1721622927", "profile_interstitial_type": "", "statuses_count": 3365, "translator_type": "none", "url": "https://t.co/rtE8lRomlr", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Somewhere Backproping"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/3qsn4wehs3", "legacy": {"binding_values": [{"key": "player_url", "value": {"string_value": "https://www.youtube.com/embed/yOyaJXpAYZQ", "type": "STRING"}}, {"key": "player_image_large", "value": {"image_value": {"height": 320, "width": 569, "url": "https://pbs.twimg.com/card_img/1926510096193818625/1b59ROuH?format=jpg&name=800x320_1"}, "type": "IMAGE"}}, {"key": "player_image", "value": {"image_value": {"height": 158, "width": 280, "url": "https://pbs.twimg.com/card_img/1926510096193818625/1b59ROuH?format=jpg&name=280x280"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.67903", "type": "STRING"}}, {"key": "description", "value": {"string_value": "In this video, I compare a simple C program with the compiled machine code of that program.Support me on Patreon: https://www.patreon.com/beneater", "type": "STRING"}}, {"key": "player_width", "value": {"string_value": "1280", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "www.youtube.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "10228272", "path": []}}}, {"key": "player_image_original", "value": {"image_value": {"height": 720, "width": 1280, "url": "https://pbs.twimg.com/card_img/1926510096193818625/1b59ROuH?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "app_num_ratings", "value": {"string_value": "42,088,439", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "player_height", "value": {"string_value": "720", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "youtube.com", "type": "STRING"}}, {"key": "app_name", "value": {"string_value": "YouTube", "type": "STRING"}}, {"key": "player_image_small", "value": {"image_value": {"height": 81, "width": 144, "url": "https://pbs.twimg.com/card_img/1926510096193818625/1b59ROuH?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "title", "value": {"string_value": "Comparing C to machine language", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/3qsn4wehs3", "type": "STRING"}}, {"key": "player_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 235, "green": 218, "red": 228}, "percentage": 83.82}, {"rgb": {"blue": 93, "green": 93, "red": 117}, "percentage": 15.26}, {"rgb": {"blue": 31, "green": 25, "red": 48}, "percentage": 0.89}]}, "type": "IMAGE_COLOR"}}, {"key": "player_image_x_large", "value": {"image_value": {"height": 720, "width": 1280, "url": "https://pbs.twimg.com/card_img/1926510096193818625/1b59ROuH?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "player", "url": "https://t.co/3qsn4wehs3", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMDIyODI3Mg==", "rest_id": "10228272", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915882040353837056/VbhPvueq_normal.jpg"}, "core": {"created_at": "Tue Nov 13 21:43:46 +0000 2007", "name": "YouTube", "screen_name": "YouTube"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "like & subscribe", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yt.be/3NDRz", "expanded_url": "https://yt.be/3NDRz", "url": "https://t.co/WBV5E1Rh1y", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6069, "followers_count": 79010249, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 77696, "media_count": 16005, "normal_followers_count": 79010249, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/10228272/1745416765", "profile_interstitial_type": "", "statuses_count": 60001, "translator_type": "regular", "url": "https://t.co/WBV5E1Rh1y", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Bruno, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1926536490022002718"], "editable_until_msecs": "1748160667000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "2996", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 22, "bookmarked": true, "created_at": "Sun May 25 07:11:07 +0000 2025", "conversation_id_str": "1926536187994386811", "display_text_range": [0, 86], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/yOyaJXpAYZQ?si…", "expanded_url": "https://youtu.be/yOyaJXpAYZQ?si=A7D4vaPzNgKmrrcj", "url": "https://t.co/3qsn4wehs3", "indices": [8, 31]}, {"display_url": "chessman7.substack.com", "expanded_url": "https://chessman7.substack.com", "url": "https://t.co/38wQ9Kg60j", "indices": [63, 86]}], "user_mentions": []}, "favorite_count": 11, "favorited": false, "full_text": "Source: https://t.co/3qsn4wehs3\nRead More Low-Level Lore here: https://t.co/38wQ9Kg60j", "in_reply_to_screen_name": "chessMan786", "in_reply_to_status_id_str": "1926536187994386811", "in_reply_to_user_id_str": "1556338457135685633", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 0, "retweet_count": 5, "retweeted": false, "user_id_str": "1556338457135685633", "id_str": "1926536490022002718"}, "twe_private_fields": {"created_at": 1748157067000, "updated_at": 1748553996861, "media_count": 0}}}]