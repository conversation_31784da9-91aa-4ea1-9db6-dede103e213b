[{"id": "1523229924223422465", "created_at": "2022-05-08 12:14:48 +03:00", "full_text": "@jxxf @elitischmos *I do not remember the source. https://t.co/ZGSFbRlCSQ", "media": [{"type": "photo", "url": "https://t.co/ZGSFbRlCSQ", "thumbnail": "https://pbs.twimg.com/media/FSOZc82X0AEmF4C?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FSOZc82X0AEmF4C?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1522993091409768448", "retweeted_status": null, "quoted_status": null, "favorite_count": 1485, "retweet_count": 103, "bookmark_count": 84, "quote_count": 12, "reply_count": 4, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1523229924223422465", "metadata": {"__typename": "Tweet", "rest_id": "1523229924223422465", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo0MTcxODQzNzg=", "rest_id": "417184378", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1648759053/IAmACutePerson_normal.jpg"}, "core": {"created_at": "Sun Nov 20 16:50:59 +0000 2011", "name": "ksk", "screen_name": "KarmaKatna"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "trying to understand", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 68316, "followers_count": 512, "friends_count": 210, "has_custom_timelines": true, "is_translator": false, "listed_count": 22, "media_count": 1228, "normal_followers_count": 512, "pinned_tweet_ids_str": ["1891153144526106863"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/417184378/1349110458", "profile_interstitial_type": "", "statuses_count": 73759, "translator_type": "regular", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1523229924223422465"], "editable_until_msecs": "1652003088000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 84, "bookmarked": true, "created_at": "Sun May 08 09:14:48 +0000 2022", "conversation_id_str": "1522993091409768448", "display_text_range": [19, 49], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/ZGSFbRlCSQ", "expanded_url": "https://x.com/KarmaKatna/status/1523229924223422465/photo/1", "id_str": "1523229821630926849", "indices": [50, 73], "media_key": "3_1523229821630926849", "media_url_https": "https://pbs.twimg.com/media/FSOZc82X0AEmF4C.jpg", "type": "photo", "url": "https://t.co/ZGSFbRlCSQ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}, "medium": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}, "small": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}, "orig": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}}, "sizes": {"large": {"h": 640, "w": 511, "resize": "fit"}, "medium": {"h": 640, "w": 511, "resize": "fit"}, "small": {"h": 640, "w": 511, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 640, "width": 511, "focus_rects": [{"x": 0, "y": 256, "w": 511, "h": 286}, {"x": 0, "y": 129, "w": 511, "h": 511}, {"x": 0, "y": 57, "w": 511, "h": 583}, {"x": 191, "y": 0, "w": 320, "h": 640}, {"x": 0, "y": 0, "w": 511, "h": 640}]}, "media_results": {"result": {"media_key": "3_1523229821630926849"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "19463693", "name": "<EMAIL>", "screen_name": "jxxf", "indices": [0, 5]}, {"id_str": "271532907", "name": "<PERSON>ων<PERSON>ταν<PERSON><PERSON><PERSON>ος", "screen_name": "elitischmos", "indices": [6, 18]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/ZGSFbRlCSQ", "expanded_url": "https://x.com/KarmaKatna/status/1523229924223422465/photo/1", "id_str": "1523229821630926849", "indices": [50, 73], "media_key": "3_1523229821630926849", "media_url_https": "https://pbs.twimg.com/media/FSOZc82X0AEmF4C.jpg", "type": "photo", "url": "https://t.co/ZGSFbRlCSQ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}, "medium": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}, "small": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}, "orig": {"faces": [{"x": 358, "y": 449, "h": 84, "w": 84}]}}, "sizes": {"large": {"h": 640, "w": 511, "resize": "fit"}, "medium": {"h": 640, "w": 511, "resize": "fit"}, "small": {"h": 640, "w": 511, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 640, "width": 511, "focus_rects": [{"x": 0, "y": 256, "w": 511, "h": 286}, {"x": 0, "y": 129, "w": 511, "h": 511}, {"x": 0, "y": 57, "w": 511, "h": 583}, {"x": 191, "y": 0, "w": 320, "h": 640}, {"x": 0, "y": 0, "w": 511, "h": 640}]}, "media_results": {"result": {"media_key": "3_1523229821630926849"}}}]}, "favorite_count": 1485, "favorited": false, "full_text": "@jxxf @elitischmos *I do not remember the source. https://t.co/ZGSFbRlCSQ", "in_reply_to_screen_name": "jxxf", "in_reply_to_status_id_str": "1522993091409768448", "in_reply_to_user_id_str": "19463693", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 12, "reply_count": 4, "retweet_count": 103, "retweeted": false, "user_id_str": "417184378", "id_str": "1523229924223422465"}, "twe_private_fields": {"created_at": 1652001288000, "updated_at": 1748554346019, "media_count": 1}}}, {"id": "1523607083307835394", "created_at": "2022-05-09 13:13:30 +03:00", "full_text": "Learning how to code in an interactive way is way more fun. \n\nSo, I created a list of interactive learning tools for you. \n\nIn this list, you can find tools to learn CSS, JavaScript, TypeScript, React, Python, Git, SQL, Regex, and more.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2067, "retweet_count": 516, "bookmark_count": 1570, "quote_count": 6, "reply_count": 54, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1523607083307835394", "metadata": {"__typename": "Tweet", "rest_id": "1523607083307835394", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMDI5ODI1MjM=", "rest_id": "202982523", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1308385514744098816/oDXuaci__normal.jpg"}, "core": {"created_at": "Fri Oct 15 07:47:09 +0000 2010", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Software Engineer · Community Builder · Creator", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "markodenic.tech/html-ebook", "expanded_url": "https://markodenic.tech/html-ebook", "url": "https://t.co/mLUfqxwP0C", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 83664, "followers_count": 258270, "friends_count": 454, "has_custom_timelines": true, "is_translator": false, "listed_count": 6075, "media_count": 1862, "normal_followers_count": 258270, "pinned_tweet_ids_str": ["1925500650063897047"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/202982523/1589663754", "profile_interstitial_type": "", "statuses_count": 31756, "translator_type": "none", "url": "https://t.co/mLUfqxwP0C", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Get my free eBook →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1523607083307835394"], "editable_until_msecs": "1652093010000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1570, "bookmarked": true, "created_at": "Mon May 09 10:13:30 +0000 2022", "conversation_id_str": "1523607083307835394", "display_text_range": [0, 236], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2067, "favorited": false, "full_text": "Learning how to code in an interactive way is way more fun. \n\nSo, I created a list of interactive learning tools for you. \n\nIn this list, you can find tools to learn CSS, JavaScript, TypeScript, React, Python, Git, SQL, Regex, and more.", "is_quote_status": false, "lang": "en", "quote_count": 6, "reply_count": 54, "retweet_count": 516, "retweeted": false, "user_id_str": "202982523", "id_str": "1523607083307835394"}, "twe_private_fields": {"created_at": 1652091210000, "updated_at": 1748554346019, "media_count": 0}}}]