[{"id": "1391487740118769678", "created_at": "2021-05-09 23:18:24 +03:00", "full_text": "123movies and putlockers have done more good for our generation than any government", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 175284, "retweet_count": 32308, "bookmark_count": 7163, "quote_count": 1118, "reply_count": 461, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1391487740118769678", "metadata": {"__typename": "Tweet", "rest_id": "1391487740118769678", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjk0MDY2MzczMjYyNTQwODAw", "rest_id": "1294066373262540800", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1737447483884605440/5fVG3Heo_normal.jpg"}, "core": {"created_at": "Fri Aug 14 00:21:03 +0000 2020", "name": "M", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "CRG", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "instagram.com/micha<PERSON><PERSON><PERSON>r", "expanded_url": "http://instagram.com/micha<PERSON><PERSON>su<PERSON>r", "url": "https://t.co/tsYNfcp9Dg", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8364, "followers_count": 7712, "friends_count": 1467, "has_custom_timelines": true, "is_translator": false, "listed_count": 17, "media_count": 46, "normal_followers_count": 7712, "pinned_tweet_ids_str": ["1340064216322027521"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1294066373262540800/1703074966", "profile_interstitial_type": "", "statuses_count": 2273, "translator_type": "none", "url": "https://t.co/tsYNfcp9Dg", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1391487740118769678"], "editable_until_msecs": "1620593304493", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 7163, "bookmarked": true, "created_at": "Sun May 09 20:18:24 +0000 2021", "conversation_id_str": "1391487740118769678", "display_text_range": [0, 83], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 175284, "favorited": false, "full_text": "123movies and putlockers have done more good for our generation than any government", "is_quote_status": false, "lang": "en", "quote_count": 1118, "reply_count": 461, "retweet_count": 32308, "retweeted": false, "user_id_str": "1294066373262540800", "id_str": "1391487740118769678"}, "twe_private_fields": {"created_at": 1620591504000, "updated_at": 1748554407657, "media_count": 0}}}, {"id": "1391524314005057538", "created_at": "2021-05-10 01:43:44 +03:00", "full_text": "Name a film you've watched more than five times. No explanations.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3585, "retweet_count": 446, "bookmark_count": 404, "quote_count": 3714, "reply_count": 10052, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1391524314005057538", "metadata": {"__typename": "Tweet", "rest_id": "1391524314005057538", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzgxNzA2NjQ4MzY5MTM1NjI1", "rest_id": "1381706648369135625", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1653082967978262560/z9ccy2wS_normal.jpg"}, "core": {"created_at": "Mon Apr 12 20:32:00 +0000 2021", "name": "SH", "screen_name": "SenneH1990"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "ALLEZ LES ROUGES", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "bsky.app/profile/senneh…", "expanded_url": "https://bsky.app/profile/senneh1990.bsky.social", "url": "https://t.co/Nl3elhEqAw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2405, "followers_count": 1640, "friends_count": 531, "has_custom_timelines": true, "is_translator": false, "listed_count": 11, "media_count": 3469, "normal_followers_count": 1640, "pinned_tweet_ids_str": ["1631719774932672513"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1381706648369135625/1748372863", "profile_interstitial_type": "", "statuses_count": 16223, "translator_type": "none", "url": "https://t.co/Nl3elhEqAw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1391524314005057538"], "editable_until_msecs": "1620602024387", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 404, "bookmarked": true, "created_at": "Sun May 09 22:43:44 +0000 2021", "conversation_id_str": "1391524314005057538", "display_text_range": [0, 65], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 3585, "favorited": false, "full_text": "Name a film you've watched more than five times. No explanations.", "is_quote_status": false, "lang": "en", "quote_count": 3714, "reply_count": 10052, "retweet_count": 446, "retweeted": false, "user_id_str": "1381706648369135625", "id_str": "1391524314005057538"}, "twe_private_fields": {"created_at": 1620600224000, "updated_at": 1748554407657, "media_count": 0}}}]