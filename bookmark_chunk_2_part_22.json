[{"id": "1429772203113996289", "created_at": "2021-08-23 14:47:11 +03:00", "full_text": "@_fels1 One more category - Testing Websites and Apps on sites Like Usertesting, TryMyUI, Userfeel, Userlytics, etc.\n\nNo experience required.\nSpeaking out your thoughts on the experience, ease of use, etc\n\nPays $10-60 per test\nHow it works 👇🏿👇🏿👇🏿\nhttps://t.co/TosHxudJjD", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1429677541024059392", "retweeted_status": null, "quoted_status": null, "favorite_count": 105, "retweet_count": 28, "bookmark_count": 74, "quote_count": 1, "reply_count": 3, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1429772203113996289", "metadata": {"__typename": "Tweet", "rest_id": "1429772203113996289", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjYyMjU3ODIyODg2MjUyNTQ1", "rest_id": "1262257822886252545", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1262258306539806720/s1i9slQu_normal.jpg"}, "core": {"created_at": "Mon May 18 05:45:55 +0000 2020", "name": "<PERSON>", "screen_name": "<PERSON>___Mbu<PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I create tutorial videos that solve everyday problems on YouTube 👉🇰🇪https://t.co/ub5IQuJ6L3", "entities": {"description": {"urls": [{"display_url": "bit.ly/FreelancerInsi…", "expanded_url": "http://bit.ly/FreelancerInsights", "url": "https://t.co/ub5IQuJ6L3", "indices": [68, 91]}]}, "url": {"urls": [{"display_url": "freelancerinsights.com", "expanded_url": "https://freelancerinsights.com/", "url": "https://t.co/oCiNrSy9nx", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 262944, "followers_count": 1099, "friends_count": 420, "has_custom_timelines": true, "is_translator": false, "listed_count": 3, "media_count": 2670, "normal_followers_count": 1099, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_interstitial_type": "", "statuses_count": 203457, "translator_type": "none", "url": "https://t.co/oCiNrSy9nx", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Nairobi, Kenya"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/TosHxudJjD", "legacy": {"binding_values": [{"key": "player_url", "value": {"string_value": "https://www.youtube.com/embed/Al_QxX08f6A", "type": "STRING"}}, {"key": "player_image_large", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "player_image", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.67902", "type": "STRING"}}, {"key": "description", "value": {"string_value": "In this video, I'll walk you through the requirements of becoming a website and application tester. Website testing is an exciting way of making money online...", "type": "STRING"}}, {"key": "player_width", "value": {"string_value": "1280", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "www.youtube.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "10228272", "path": []}}}, {"key": "app_num_ratings", "value": {"string_value": "42,060,347", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "player_height", "value": {"string_value": "720", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "youtube.com", "type": "STRING"}}, {"key": "app_name", "value": {"string_value": "YouTube", "type": "STRING"}}, {"key": "player_image_small", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "title", "value": {"string_value": "How to Become a Website and App Tester - Website Testing for Beginners", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/TosHxudJjD", "type": "STRING"}}, {"key": "player_image_x_large", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "player", "url": "https://t.co/TosHxudJjD", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMDIyODI3Mg==", "rest_id": "10228272", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915882040353837056/VbhPvueq_normal.jpg"}, "core": {"created_at": "Tue Nov 13 21:43:46 +0000 2007", "name": "YouTube", "screen_name": "YouTube"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "like & subscribe", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yt.be/3NDRz", "expanded_url": "https://yt.be/3NDRz", "url": "https://t.co/WBV5E1Rh1y", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6069, "followers_count": 79009634, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 77696, "media_count": 16005, "normal_followers_count": 79009634, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/10228272/1745416765", "profile_interstitial_type": "", "statuses_count": 60001, "translator_type": "regular", "url": "https://t.co/WBV5E1Rh1y", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Bruno, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1429772203113996289"], "editable_until_msecs": "1629721031762", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 74, "bookmarked": true, "created_at": "Mon Aug 23 11:47:11 +0000 2021", "conversation_id_str": "1429677541024059392", "display_text_range": [8, 270], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/Al_QxX08f6A", "expanded_url": "https://youtu.be/Al_QxX08f6A", "url": "https://t.co/TosHxudJjD", "indices": [247, 270]}], "user_mentions": [{"id_str": "820309675245760512", "name": "<PERSON><PERSON>", "screen_name": "_fels1", "indices": [0, 7]}]}, "favorite_count": 105, "favorited": false, "full_text": "@_fels1 One more category - Testing Websites and Apps on sites Like Usertesting, TryMyUI, Userfeel, Userlytics, etc.\n\nNo experience required.\nSpeaking out your thoughts on the experience, ease of use, etc\n\nPays $10-60 per test\nHow it works 👇🏿👇🏿👇🏿\nhttps://t.co/TosHxudJjD", "in_reply_to_screen_name": "_fels1", "in_reply_to_status_id_str": "1429677541024059392", "in_reply_to_user_id_str": "820309675245760512", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 3, "retweet_count": 28, "retweeted": false, "user_id_str": "1262257822886252545", "id_str": "1429772203113996289"}, "twe_private_fields": {"created_at": 1629719231000, "updated_at": 1748554378042, "media_count": 0}}}, {"id": "1431632770355408902", "created_at": "2021-08-28 18:00:25 +03:00", "full_text": "Men,\n\nHow to boost your sex stamina\n\n- Fasting daily\n\n- Meat &amp; Eggs\n\n- Sleep\n\n- Eat saturated fats\n\n- Compound workouts in the gym\n\n- Kegels exercise\n\n- Walk in the sun\n\n- SUN YOUR BALLS\n\nPlease:\n\n- avoid blue pills, \n\n-don't masturbate, \n\n- no pornography\n\n#MasculinitySaturday", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3148, "retweet_count": 715, "bookmark_count": 155, "quote_count": 15, "reply_count": 65, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1431632770355408902", "metadata": {"__typename": "Tweet", "rest_id": "1431632770355408902", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozNDkwODM3MQ==", "rest_id": "34908371", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1398393807041929219/EZUbnPO9_normal.jpg"}, "core": {"created_at": "Fri Apr 24 12:07:14 +0000 2009", "name": "<PERSON>", "screen_name": "amerix"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Reproductive Health | Men's Health & Wellness | Mandela Washington Fellow | <EMAIL>", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "amerix.co.ke", "expanded_url": "http://www.amerix.co.ke", "url": "https://t.co/GatlDhjdqn", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2320, "followers_count": 2186947, "friends_count": 562, "has_custom_timelines": true, "is_translator": false, "listed_count": 1083, "media_count": 3917, "normal_followers_count": 2186947, "pinned_tweet_ids_str": ["1570469035355021318"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/34908371/**********", "profile_interstitial_type": "", "statuses_count": 47534, "translator_type": "none", "url": "https://t.co/GatlDhjdqn", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Minneapolis, MN. Kenya"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1457977582155141121", "professional_type": "Creator", "category": [{"id": 584, "name": "Medical & Health", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1431632770355408902"], "editable_until_msecs": "1630164625560", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://buffer.com\" rel=\"nofollow\"><PERSON><PERSON>er App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 155, "bookmarked": true, "created_at": "Sat Aug 28 15:00:25 +0000 2021", "conversation_id_str": "1431632770355408902", "display_text_range": [0, 282], "entities": {"hashtags": [{"indices": [262, 282], "text": "MasculinitySaturday"}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 3148, "favorited": false, "full_text": "Men,\n\nHow to boost your sex stamina\n\n- Fasting daily\n\n- Meat &amp; Eggs\n\n- Sleep\n\n- Eat saturated fats\n\n- Compound workouts in the gym\n\n- Kegels exercise\n\n- Walk in the sun\n\n- SUN YOUR BALLS\n\nPlease:\n\n- avoid blue pills, \n\n-don't masturbate, \n\n- no pornography\n\n#MasculinitySaturday", "is_quote_status": false, "lang": "en", "quote_count": 15, "reply_count": 65, "retweet_count": 715, "retweeted": false, "user_id_str": "34908371", "id_str": "1431632770355408902"}, "twe_private_fields": {"created_at": 1630162825000, "updated_at": 1748554397392, "media_count": 0}}}]