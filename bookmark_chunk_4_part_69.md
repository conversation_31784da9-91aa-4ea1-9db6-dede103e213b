---
Tweet URL: https://twitter.com/shivambhadani_/status/1803686555946721408
Tweet ID: 1803686555946721408
Author: @shivambhadani_
---

Backend Developers should know these:

- Application Server (Django, Nodejs, atleast one framework of any language)

- APIs (REST & GraphQL)

- SQL & NoSQL DBs

- Authentication and Authorization (JWT, Session, OAuth2, SSO)

- Multi-threading, Asynchronous Programming

- Caching (Redis, CDNs)

- Message Brokers (Streaming, PubSub)

- Rate Limiting

- Network Basics (TCP, UDP, Proxy, VPNs, Firewalls)

- CI/CD (Jenkins)

- Web Server (Ngnix)

- Container (Docker)

- AWS (IAM, S3, EC2, VPC, ECR, ECS)

- Serverless architecture (AWS Lambda)

- Job Scheduling (Crone Jobs)

- Scaling (Horizontal & Vertical)

---
Category: Software Development, Backend Development, Tech Skills