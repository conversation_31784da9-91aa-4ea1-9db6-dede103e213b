# Bookmarks from bookmark_chunk_4_part_42.json

## Category: Technology / AI / Google DeepMind / Text-to-Image / Imagen 2 / Vertex AI

*   **Author:** @GoogleDeepMind
    **Date:** 2023-12-13 18:11:52 +03:00
    **Content:** Meet Imagen 2: our most advanced text-to-image diffusion technology. ✨

It features high-quality, photorealistic outputs and stronger consistency with your prompts. 🖼️

Now available to use via @GoogleCloud’s #VertexAI platform. → https://t.co/T1IIJMbIW9 https://t.co/iWIzi2jgZH
    **URL:** [https://twitter.com/GoogleDeepMind/status/1734954295655534780](https://twitter.com/GoogleDeepMind/status/1734954295655534780)
    **Media:**
      - Type: photo
      - URL: https://t.co/iWIzi2jgZH
      - Thumbnail: https://pbs.twimg.com/media/GBPK8F_W4AAGU0s?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/GBPK8F_W4AAGU0s?format=jpg&name=orig
    **External Links:**
      - [https://dpmd.ai/x-imagen2](https://dpmd.ai/x-imagen2)

---

## Category: Technology / AI / Google / Gemini API / SDK / Developer Tools / Programming Languages

*   **Author:** @MiguelRamosPM
    **Date:** 2023-12-13 20:50:46 +03:00
    **Content:** Today's the day! 
My team and sibling team in Vertex AI have released the SDKs for Gemini API. We put a lot of effort in creating a simple and streamline developer experience. With a few lines of code you can use the Gemini Pro model in Python, JavaScript / Node.js, Kotlin, Swift, Go, and Java.  
A piece of code worth more than 1000 words, here you can find all the quickstarts, samples, and repos with the implementation of the SDKs:

Google AI
https://t.co/mfJhUd5cLU
Python https://t.co/8Qj8jqGyXZ
JS https://t.co/bzLlRWOkgh
Android https://t.co/jfEtAxkVSE
Swift https://t.co/L7bwCzGJ6A
Go https://t.co/CRqWOiPoNT

Vertex AI
https://t.co/tQ2KmxTHeG
Python https://t.co/fLS9MxxNLQ
Node https://t.co/0NDBHdTaMP
Java https://t.co/wri74az26N
Go https://t.co/OJxTolA3ir 

(Dart will come pretty soon, we are finishing up some details)
    **URL:** [https://twitter.com/MiguelRamosPM/status/1734994285299388890](https://twitter.com/MiguelRamosPM/status/1734994285299388890)
    **Media:**
      - Type: photo
      - URL: https://t.co/Kg8KqlV4GA
      - Thumbnail: https://pbs.twimg.com/media/GBPvsSPaUAA3ANh?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/GBPvsSPaUAA3ANh?format=jpg&name=orig
    **External Links:**
      - [https://ai.google.dev/](https://ai.google.dev/)
      - [https://ai.google.dev/tutorials/python_quickstart](https://ai.google.dev/tutorials/python_quickstart)
      - [https://ai.google.dev/tutorials/node_quickstart](https://ai.google.dev/tutorials/node_quickstart)
      - [https://ai.google.dev/tutorials/android_quickstart](https://ai.google.dev/tutorials/android_quickstart)
      - [https://ai.google.dev/tutorials/swift_quickstart](https://ai.google.dev/tutorials/swift_quickstart)
      - [https://ai.google.dev/tutorials/go_quickstart](https://ai.google.dev/tutorials/go_quickstart)
      - [https://cloud.google.com/vertex-ai/docs/generative-ai/start/quickstarts/api-quickstart](https://cloud.google.com/vertex-ai/docs/generative-ai/start/quickstarts/api-quickstart)
      - [https://github.com/googleapis/python-aiplatform/blob/main/samples/snippets/prediction_service/predict_text_prompt_sample.py](https://github.com/googleapis/python-aiplatform/blob/main/samples/snippets/prediction_service/predict_text_prompt_sample.py)
      - [https://github.com/googleapis/nodejs-ai-platform/blob/main/samples/predict-text-prompt.js](https://github.com/googleapis/nodejs-ai-platform/blob/main/samples/predict-text-prompt.js)
      - [https://github.com/googleapis/java-aiplatform/blob/main/samples/snippets/src/main/java/com/example/aiplatform/PredictTextPromptSample.java](https://github.com/googleapis/java-aiplatform/blob/main/samples/snippets/src/main/java/com/example/aiplatform/PredictTextPromptSample.java)
      - [https://github.com/googleapis/google-cloud-go/blob/main/aiplatform/apiv1/prediction_client_example_test.go](https://github.com/googleapis/google-cloud-go/blob/main/aiplatform/apiv1/prediction_client_example_test.go)

---