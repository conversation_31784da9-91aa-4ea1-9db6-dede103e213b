[{"id": "1878325524314947586", "created_at": "2025-01-12 09:17:57 +03:00", "full_text": "Umi-OCR: Free, open-source software for offline OCR, supporting screenshot capture, batch image processing, PDF document recognition, QR code scanning, and multi-language text extraction https://t.co/boj06edMPk", "media": [{"type": "photo", "url": "https://t.co/boj06edMPk", "thumbnail": "https://pbs.twimg.com/media/GhEnFcGW0AAVRDr?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GhEnFcGW0AAVRDr?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1407, "retweet_count": 173, "bookmark_count": 1724, "quote_count": 0, "reply_count": 8, "views_count": 93338, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1878325524314947586", "metadata": {"__typename": "Tweet", "rest_id": "1878325524314947586", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzI2MTgwNzU2MzEwMzMxMzk5", "rest_id": "1326180756310331399", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1905090420142379008/Ydq5So7B_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Nov 10 15:13:32 +0000 2020", "name": "<PERSON>", "screen_name": "tom_doerr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Follow for posts about GitHub repos, DSPy, and agents\nSubscribe for top posts\nDM to share your AI project (Due to volume of DMs I'll prioritize subscribers)", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "tom-doerr.github.io/repo_posts/", "expanded_url": "https://tom-doerr.github.io/repo_posts/", "url": "https://t.co/WreHYiW9xe", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 52917, "followers_count": 84560, "friends_count": 2135, "has_custom_timelines": true, "is_translator": false, "listed_count": 808, "media_count": 6792, "normal_followers_count": 84560, "pinned_tweet_ids_str": ["1886002942962024635"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1326180756310331399/1721336045", "profile_interstitial_type": "", "statuses_count": 20559, "translator_type": "none", "url": "https://t.co/WreHYiW9xe", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1878325524314947586"], "editable_until_msecs": "1736666277000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "93338", "state": "EnabledWithCount"}, "source": "<a href=\"https://composio.dev/\" rel=\"nofollow\">Composio</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1724, "bookmarked": true, "created_at": "Sun Jan 12 06:17:57 +0000 2025", "conversation_id_str": "1878325524314947586", "display_text_range": [0, 186], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/boj06edMPk", "expanded_url": "https://x.com/tom_doerr/status/1878325524314947586/photo/1", "id_str": "1878325493985890304", "indices": [187, 210], "media_key": "3_1878325493985890304", "media_url_https": "https://pbs.twimg.com/media/GhEnFcGW0AAVRDr.png", "type": "photo", "url": "https://t.co/boj06edMPk", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 310, "w": 758, "resize": "fit"}, "medium": {"h": 310, "w": 758, "resize": "fit"}, "small": {"h": 278, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 310, "width": 758, "focus_rects": [{"x": 6, "y": 0, "w": 554, "h": 310}, {"x": 128, "y": 0, "w": 310, "h": 310}, {"x": 147, "y": 0, "w": 272, "h": 310}, {"x": 206, "y": 0, "w": 155, "h": 310}, {"x": 0, "y": 0, "w": 758, "h": 310}]}, "media_results": {"result": {"media_key": "3_1878325493985890304"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/boj06edMPk", "expanded_url": "https://x.com/tom_doerr/status/1878325524314947586/photo/1", "id_str": "1878325493985890304", "indices": [187, 210], "media_key": "3_1878325493985890304", "media_url_https": "https://pbs.twimg.com/media/GhEnFcGW0AAVRDr.png", "type": "photo", "url": "https://t.co/boj06edMPk", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 310, "w": 758, "resize": "fit"}, "medium": {"h": 310, "w": 758, "resize": "fit"}, "small": {"h": 278, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 310, "width": 758, "focus_rects": [{"x": 6, "y": 0, "w": 554, "h": 310}, {"x": 128, "y": 0, "w": 310, "h": 310}, {"x": 147, "y": 0, "w": 272, "h": 310}, {"x": 206, "y": 0, "w": 155, "h": 310}, {"x": 0, "y": 0, "w": 758, "h": 310}]}, "media_results": {"result": {"media_key": "3_1878325493985890304"}}}]}, "favorite_count": 1407, "favorited": false, "full_text": "Umi-OCR: Free, open-source software for offline OCR, supporting screenshot capture, batch image processing, PDF document recognition, QR code scanning, and multi-language text extraction https://t.co/boj06edMPk", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 8, "retweet_count": 173, "retweeted": false, "user_id_str": "1326180756310331399", "id_str": "1878325524314947586"}, "twe_private_fields": {"created_at": 1736662677000, "updated_at": 1748554112350, "media_count": 1}}}, {"id": "1878472000215343565", "created_at": "2025-01-12 18:59:59 +03:00", "full_text": "Stanford launched a free Google Deep Research clone called STORM.\n\nIt uses GPT 4-o + Bing Search under the hood to generate long cited reports from many websites in ~3mins.\n\nIt's also completely open-source and free to use. Link below. https://t.co/WAHoLHehWT", "media": [{"type": "photo", "url": "https://t.co/WAHoLHehWT", "thumbnail": "https://pbs.twimg.com/media/GhGsVEcbAAE3NyS?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GhGsVEcbAAE3NyS?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3030, "retweet_count": 359, "bookmark_count": 4592, "quote_count": 36, "reply_count": 58, "views_count": 329312, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1878472000215343565", "metadata": {"__typename": "Tweet", "rest_id": "1878472000215343565", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozNjEwNDQzMTE=", "rest_id": "361044311", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1471065068041244674/eLm0sHqx_normal.jpg"}, "core": {"created_at": "Wed Aug 24 04:46:48 +0000 2011", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "VC at @MenloVentures. Formerly founding team @glean, @Google Search. @Cornell CS. Tweets about tech, immigration, India, fitness and search.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "debarghyadas.com", "expanded_url": "http://debarghyadas.com", "url": "https://t.co/1iv49ef972", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 44880, "followers_count": 178152, "friends_count": 4850, "has_custom_timelines": true, "is_translator": false, "listed_count": 1495, "media_count": 2286, "normal_followers_count": 178152, "pinned_tweet_ids_str": ["1813598830182707261"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/361044311/1660701810", "profile_interstitial_type": "", "statuses_count": 14490, "translator_type": "none", "url": "https://t.co/1iv49ef972", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "San Francisco"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1479504307770707975", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1878472000215343565"], "editable_until_msecs": "1736701199000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "329312", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 4592, "bookmarked": true, "created_at": "Sun Jan 12 15:59:59 +0000 2025", "conversation_id_str": "1878472000215343565", "display_text_range": [0, 235], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/WAHoLHehWT", "expanded_url": "https://x.com/deedydas/status/1878472000215343565/photo/1", "id_str": "1878471997560389633", "indices": [236, 259], "media_key": "3_1878471997560389633", "media_url_https": "https://pbs.twimg.com/media/GhGsVEcbAAE3NyS.jpg", "type": "photo", "url": "https://t.co/WAHoLHehWT", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 497, "y": 1400, "h": 155, "w": 155}, {"x": 679, "y": 1317, "h": 187, "w": 187}]}, "medium": {"faces": [{"x": 333, "y": 938, "h": 103, "w": 103}, {"x": 454, "y": 882, "h": 125, "w": 125}]}, "small": {"faces": [{"x": 188, "y": 530, "h": 58, "w": 58}, {"x": 257, "y": 499, "h": 70, "w": 70}]}, "orig": {"faces": [{"x": 497, "y": 1400, "h": 155, "w": 155}, {"x": 679, "y": 1317, "h": 187, "w": 187}]}}, "sizes": {"large": {"h": 1792, "w": 1179, "resize": "fit"}, "medium": {"h": 1200, "w": 790, "resize": "fit"}, "small": {"h": 680, "w": 447, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1792, "width": 1179, "focus_rects": [{"x": 0, "y": 72, "w": 1179, "h": 660}, {"x": 0, "y": 0, "w": 1179, "h": 1179}, {"x": 0, "y": 0, "w": 1179, "h": 1344}, {"x": 141, "y": 0, "w": 896, "h": 1792}, {"x": 0, "y": 0, "w": 1179, "h": 1792}]}, "media_results": {"result": {"media_key": "3_1878471997560389633"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/WAHoLHehWT", "expanded_url": "https://x.com/deedydas/status/1878472000215343565/photo/1", "id_str": "1878471997560389633", "indices": [236, 259], "media_key": "3_1878471997560389633", "media_url_https": "https://pbs.twimg.com/media/GhGsVEcbAAE3NyS.jpg", "type": "photo", "url": "https://t.co/WAHoLHehWT", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 497, "y": 1400, "h": 155, "w": 155}, {"x": 679, "y": 1317, "h": 187, "w": 187}]}, "medium": {"faces": [{"x": 333, "y": 938, "h": 103, "w": 103}, {"x": 454, "y": 882, "h": 125, "w": 125}]}, "small": {"faces": [{"x": 188, "y": 530, "h": 58, "w": 58}, {"x": 257, "y": 499, "h": 70, "w": 70}]}, "orig": {"faces": [{"x": 497, "y": 1400, "h": 155, "w": 155}, {"x": 679, "y": 1317, "h": 187, "w": 187}]}}, "sizes": {"large": {"h": 1792, "w": 1179, "resize": "fit"}, "medium": {"h": 1200, "w": 790, "resize": "fit"}, "small": {"h": 680, "w": 447, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1792, "width": 1179, "focus_rects": [{"x": 0, "y": 72, "w": 1179, "h": 660}, {"x": 0, "y": 0, "w": 1179, "h": 1179}, {"x": 0, "y": 0, "w": 1179, "h": 1344}, {"x": 141, "y": 0, "w": 896, "h": 1792}, {"x": 0, "y": 0, "w": 1179, "h": 1792}]}, "media_results": {"result": {"media_key": "3_1878471997560389633"}}}]}, "favorite_count": 3030, "favorited": false, "full_text": "Stanford launched a free Google Deep Research clone called STORM.\n\nIt uses GPT 4-o + Bing Search under the hood to generate long cited reports from many websites in ~3mins.\n\nIt's also completely open-source and free to use. Link below. https://t.co/WAHoLHehWT", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 36, "reply_count": 58, "retweet_count": 359, "retweeted": false, "user_id_str": "361044311", "id_str": "1878472000215343565"}, "twe_private_fields": {"created_at": 1736697599000, "updated_at": 1748554112350, "media_count": 1}}}]