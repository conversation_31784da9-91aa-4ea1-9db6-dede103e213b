[{"id": "1898590960919220244", "created_at": "2025-03-09 07:25:33 +03:00", "full_text": "Introduction to Object-Oriented Programming (OOP)\nObject-Oriented Programming (OOP) is a programming paradigm that focuses on objects rather than functions and logic. It is widely used in modern programming languages like JavaScript, Python, Java, and C++.\n\nA thread 🧵👇🏻👇🏻\n\n#WebDevelopment #FrontendDevelopment #FullStackDevelopment #Coding #Programming #LearnToCode #Developer #ReactJS #JavaScript #NextJS #TypeScript #ReactDeveloper #JSFrameworks #HTML #CSS #TailwindCSS #WebDesign #UIUX #CSSTricks #NodeJS #ExpressJS #MongoDB #SQL #APIDevelopment #100DaysOfCode #CodeNewbie #TechContent #LearnWithMe #pushpendratips #coder #coding", "media": [{"type": "photo", "url": "https://t.co/PExVlpQ6J7", "thumbnail": "https://pbs.twimg.com/media/GlklVNnXUAAzmF1?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GlklVNnXUAAzmF1?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1672, "retweet_count": 300, "bookmark_count": 1883, "quote_count": 7, "reply_count": 50, "views_count": 116579, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1898590960919220244", "metadata": {"__typename": "Tweet", "rest_id": "1898590960919220244", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTE2MjgyMDU0MTYxNjMzMjgw", "rest_id": "1516282054161633280", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1818994239390244864/WMByuLrb_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Apr 19 05:06:54 +0000 2022", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "pushpendratips"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "🧑‍JavaScript for New Developers | Full Stack Development\n📒 Daily Informative Posts\n🚀 Best Place for Learners\n👇 Resources for you\nhttps://t.co/gjKVfBD89M", "entities": {"description": {"urls": [{"display_url": "t.me/pushpendratips", "expanded_url": "https://t.me/pushpendratips", "url": "https://t.co/gjKVfBD89M", "indices": [129, 152]}]}, "url": {"urls": [{"display_url": "youtube.com/@TheKeepOnCodi…", "expanded_url": "https://www.youtube.com/@TheKeepOnCoding", "url": "https://t.co/ze3CjnkaBI", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 19246, "followers_count": 3828, "friends_count": 114, "has_custom_timelines": false, "is_translator": false, "listed_count": 9, "media_count": 1451, "normal_followers_count": 3828, "pinned_tweet_ids_str": ["1898590960919220244"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1516282054161633280/1729332843", "profile_interstitial_type": "", "statuses_count": 23851, "translator_type": "none", "url": "https://t.co/ze3CjnkaBI", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Noida, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1898590960919220244"], "editable_until_msecs": "1741497933000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "116579", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4OTg1OTA5NjA3OTc1MzIxNjA=", "text": "Introduction to Object-Oriented Programming (OOP)\nObject-Oriented Programming (OOP) is a programming paradigm that focuses on objects rather than functions and logic. It is widely used in modern programming languages like JavaScript, Python, Java, and C++.\n\nA thread 🧵👇🏻👇🏻\n\n#WebDevelopment #FrontendDevelopment #FullStackDevelopment #Coding #Programming #LearnToCode #Developer #ReactJS #JavaScript #NextJS #TypeScript #ReactDeveloper #JSFrameworks #HTML #CSS #TailwindCSS #WebDesign #UIUX #CSSTricks #NodeJS #ExpressJS #MongoDB #SQL #APIDevelopment #100DaysOfCode #CodeNewbie #TechContent #LearnWithMe #pushpendratips #coder #coding", "entity_set": {"hashtags": [{"indices": [274, 289], "text": "WebDevelopment"}, {"indices": [290, 310], "text": "FrontendDevelopment"}, {"indices": [311, 332], "text": "FullStackDevelopment"}, {"indices": [333, 340], "text": "Coding"}, {"indices": [341, 353], "text": "Programming"}, {"indices": [354, 366], "text": "LearnToCode"}, {"indices": [367, 377], "text": "Developer"}, {"indices": [378, 386], "text": "ReactJS"}, {"indices": [387, 398], "text": "JavaScript"}, {"indices": [399, 406], "text": "NextJS"}, {"indices": [407, 418], "text": "TypeScript"}, {"indices": [419, 434], "text": "ReactDeveloper"}, {"indices": [435, 448], "text": "JSFrameworks"}, {"indices": [449, 454], "text": "HTML"}, {"indices": [455, 459], "text": "CSS"}, {"indices": [460, 472], "text": "TailwindCSS"}, {"indices": [473, 483], "text": "WebDesign"}, {"indices": [484, 489], "text": "UIUX"}, {"indices": [490, 500], "text": "CSSTricks"}, {"indices": [501, 508], "text": "NodeJS"}, {"indices": [509, 519], "text": "ExpressJS"}, {"indices": [520, 528], "text": "MongoDB"}, {"indices": [529, 533], "text": "SQL"}, {"indices": [534, 549], "text": "APIDevelopment"}, {"indices": [550, 564], "text": "100DaysOfCode"}, {"indices": [565, 576], "text": "CodeNewbie"}, {"indices": [577, 589], "text": "TechContent"}, {"indices": [590, 602], "text": "LearnWithMe"}, {"indices": [603, 618], "text": "pushpendratips"}, {"indices": [619, 625], "text": "coder"}, {"indices": [626, 633], "text": "coding"}], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 1883, "bookmarked": true, "created_at": "Sun Mar 09 04:25:33 +0000 2025", "conversation_id_str": "1898590960919220244", "display_text_range": [0, 272], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/PExVlpQ6J7", "expanded_url": "https://x.com/pushpendratips/status/1898590960919220244/photo/1", "id_str": "1898589764275621888", "indices": [273, 296], "media_key": "3_1898589764275621888", "media_url_https": "https://pbs.twimg.com/media/GlklVNnXUAAzmF1.jpg", "type": "photo", "url": "https://t.co/PExVlpQ6J7", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1456, "w": 1080, "resize": "fit"}, "medium": {"h": 1200, "w": 890, "resize": "fit"}, "small": {"h": 680, "w": 504, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1456, "width": 1080, "focus_rects": [{"x": 0, "y": 98, "w": 1080, "h": 605}, {"x": 0, "y": 0, "w": 1080, "h": 1080}, {"x": 0, "y": 0, "w": 1080, "h": 1231}, {"x": 176, "y": 0, "w": 728, "h": 1456}, {"x": 0, "y": 0, "w": 1080, "h": 1456}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1898589764275621888"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/PExVlpQ6J7", "expanded_url": "https://x.com/pushpendratips/status/1898590960919220244/photo/1", "id_str": "1898589764275621888", "indices": [273, 296], "media_key": "3_1898589764275621888", "media_url_https": "https://pbs.twimg.com/media/GlklVNnXUAAzmF1.jpg", "type": "photo", "url": "https://t.co/PExVlpQ6J7", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1456, "w": 1080, "resize": "fit"}, "medium": {"h": 1200, "w": 890, "resize": "fit"}, "small": {"h": 680, "w": 504, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1456, "width": 1080, "focus_rects": [{"x": 0, "y": 98, "w": 1080, "h": 605}, {"x": 0, "y": 0, "w": 1080, "h": 1080}, {"x": 0, "y": 0, "w": 1080, "h": 1231}, {"x": 176, "y": 0, "w": 728, "h": 1456}, {"x": 0, "y": 0, "w": 1080, "h": 1456}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1898589764275621888"}}}]}, "favorite_count": 1672, "favorited": false, "full_text": "Introduction to Object-Oriented Programming (OOP)\nObject-Oriented Programming (OOP) is a programming paradigm that focuses on objects rather than functions and logic. It is widely used in modern programming languages like JavaScript, Python, Java, and C++.\n\nA thread 🧵👇🏻👇🏻 https://t.co/PExVlpQ6J7", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 7, "reply_count": 50, "retweet_count": 300, "retweeted": false, "user_id_str": "1516282054161633280", "id_str": "1898590960919220244"}, "twe_private_fields": {"created_at": 1741494333000, "updated_at": 1748554089969, "media_count": 1}}}, {"id": "1898974745959256426", "created_at": "2025-03-10 08:50:35 +03:00", "full_text": "A collective list of free APIs endpoints https://t.co/n1WvGpFAi0", "media": [{"type": "photo", "url": "https://t.co/n1WvGpFAi0", "thumbnail": "https://pbs.twimg.com/media/GlqDdohWwAAa45e?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GlqDdohWwAAa45e?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 611, "retweet_count": 65, "bookmark_count": 587, "quote_count": 1, "reply_count": 3, "views_count": 37855, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1898974745959256426", "metadata": {"__typename": "Tweet", "rest_id": "1898974745959256426", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzE1NjM5MTQ3MjQ4MjQ2Nzg1", "rest_id": "1315639147248246785", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1679831765744259073/hoVtsOZ9_normal.jpg"}, "core": {"created_at": "Mon Oct 12 13:03:35 +0000 2020", "name": "GitHub Projects Community", "screen_name": "GithubProjects"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "We're sharing/showcasing best of @github projects/repos. Follow to stay in loop. Promoting Open-Source Contributions. UNOFFICIAL, but followed by github", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "ko-fi.com/githubprojects", "expanded_url": "http://ko-fi.com/githubprojects", "url": "https://t.co/bBwZV6SJRz", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 11993, "followers_count": 115421, "friends_count": 256, "has_custom_timelines": true, "is_translator": false, "listed_count": 541, "media_count": 1291, "normal_followers_count": 115421, "pinned_tweet_ids_str": ["1926518641853358549"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1315639147248246785/1607963821", "profile_interstitial_type": "", "statuses_count": 5295, "translator_type": "none", "url": "https://t.co/bBwZV6SJRz", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1490267652644024320", "professional_type": "Creator", "category": [{"id": 1009, "name": "Community", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "******************************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1898974745959256426"], "editable_until_msecs": "1741589435000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "37855", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 587, "bookmarked": true, "created_at": "Mon Mar 10 05:50:35 +0000 2025", "conversation_id_str": "1898974745959256426", "display_text_range": [0, 40], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/n1WvGpFAi0", "expanded_url": "https://x.com/GithubProjects/status/1898974745959256426/photo/1", "id_str": "1898974738006851584", "indices": [41, 64], "media_key": "3_1898974738006851584", "media_url_https": "https://pbs.twimg.com/media/GlqDdohWwAAa45e.jpg", "type": "photo", "url": "https://t.co/n1WvGpFAi0", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1388, "w": 1179, "resize": "fit"}, "medium": {"h": 1200, "w": 1019, "resize": "fit"}, "small": {"h": 680, "w": 578, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1388, "width": 1179, "focus_rects": [{"x": 0, "y": 0, "w": 1179, "h": 660}, {"x": 0, "y": 0, "w": 1179, "h": 1179}, {"x": 0, "y": 0, "w": 1179, "h": 1344}, {"x": 0, "y": 0, "w": 694, "h": 1388}, {"x": 0, "y": 0, "w": 1179, "h": 1388}]}, "media_results": {"result": {"media_key": "3_1898974738006851584"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/n1WvGpFAi0", "expanded_url": "https://x.com/GithubProjects/status/1898974745959256426/photo/1", "id_str": "1898974738006851584", "indices": [41, 64], "media_key": "3_1898974738006851584", "media_url_https": "https://pbs.twimg.com/media/GlqDdohWwAAa45e.jpg", "type": "photo", "url": "https://t.co/n1WvGpFAi0", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1388, "w": 1179, "resize": "fit"}, "medium": {"h": 1200, "w": 1019, "resize": "fit"}, "small": {"h": 680, "w": 578, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1388, "width": 1179, "focus_rects": [{"x": 0, "y": 0, "w": 1179, "h": 660}, {"x": 0, "y": 0, "w": 1179, "h": 1179}, {"x": 0, "y": 0, "w": 1179, "h": 1344}, {"x": 0, "y": 0, "w": 694, "h": 1388}, {"x": 0, "y": 0, "w": 1179, "h": 1388}]}, "media_results": {"result": {"media_key": "3_1898974738006851584"}}}]}, "favorite_count": 611, "favorited": false, "full_text": "A collective list of free APIs endpoints https://t.co/n1WvGpFAi0", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 3, "retweet_count": 65, "retweeted": false, "user_id_str": "1315639147248246785", "id_str": "1898974745959256426"}, "twe_private_fields": {"created_at": 1741585835000, "updated_at": 1748554089969, "media_count": 1}}}]