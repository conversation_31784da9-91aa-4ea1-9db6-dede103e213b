[{"id": "1829158662964691159", "created_at": "2024-08-29 17:06:24 +03:00", "full_text": "You can Crawl entire website with Claude 3.5 or GPT4-o with this open-sourced tool firecrawl. 💯\n\nTurn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.\n\nCrawls all accessible subpages and give you clean data for each. No sitemap required.\n\nThe greatest benefit is that the extracted data is catered for LLM-based pipelines.", "media": [{"type": "photo", "url": "https://t.co/XiXCRNpjwx", "thumbnail": "https://pbs.twimg.com/media/GWJ57UJXwAAW5_m?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GWJ57UJXwAAW5_m?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5010, "retweet_count": 501, "bookmark_count": 11687, "quote_count": 26, "reply_count": 108, "views_count": 940227, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1829158662964691159", "metadata": {"__typename": "Tweet", "rest_id": "1829158662964691159", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNTg4MzQ1NDA4", "rest_id": "**********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1816185267037859840/Fd18CH0v_normal.jpg"}, "core": {"created_at": "Wed Jun 25 22:38:54 +0000 2014", "name": "<PERSON><PERSON><PERSON>", "screen_name": "rohanpaul_ai"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "💼 Engineer.\n\n📚 I write daily on actionable AI developments.\n\n🗞️ Subscribe and instantly get a 1300+page Python book → https://t.co/lK1BBqjk0Z", "entities": {"description": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "https://rohan-paul.com", "url": "https://t.co/lK1BBqjk0Z", "indices": [118, 141]}]}, "url": {"urls": [{"display_url": "rohan-paul.com", "expanded_url": "http://www.rohan-paul.com", "url": "https://t.co/2NKnK0xg7T", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 32936, "followers_count": 64302, "friends_count": 811, "has_custom_timelines": true, "is_translator": false, "listed_count": 1228, "media_count": 15320, "normal_followers_count": 64302, "pinned_tweet_ids_str": ["1898099124165394754"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/**********/**********", "profile_interstitial_type": "", "statuses_count": 38933, "translator_type": "none", "url": "https://t.co/2NKnK0xg7T", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Ex Inv Banker (Deutsche)"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1826415623603180000", "professional_type": "Creator", "category": [{"id": 958, "name": "Entrepreneur", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1829158662964691159"], "editable_until_msecs": "*************", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "940227", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4MjkxNTg2NjI4NzY2MDY0NjQ=", "text": "You can Crawl entire website with Claude 3.5 or GPT4-o with this open-sourced tool firecrawl. 💯\n\nTurn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.\n\nCrawls all accessible subpages and give you clean data for each. No sitemap required.\n\nThe greatest benefit is that the extracted data is catered for LLM-based pipelines.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 11687, "bookmarked": true, "created_at": "Thu Aug 29 14:06:24 +0000 2024", "conversation_id_str": "1829158662964691159", "display_text_range": [0, 275], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/XiXCRNpjwx", "expanded_url": "https://x.com/rohanpaul_ai/status/1829158662964691159/photo/1", "id_str": "1829158458593099776", "indices": [276, 299], "media_key": "3_1829158458593099776", "media_url_https": "https://pbs.twimg.com/media/GWJ57UJXwAAW5_m.jpg", "type": "photo", "url": "https://t.co/XiXCRNpjwx", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1434, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2631, "width": 1842, "focus_rects": [{"x": 0, "y": 865, "w": 1842, "h": 1032}, {"x": 0, "y": 460, "w": 1842, "h": 1842}, {"x": 0, "y": 331, "w": 1842, "h": 2100}, {"x": 263, "y": 0, "w": 1316, "h": 2631}, {"x": 0, "y": 0, "w": 1842, "h": 2631}]}, "media_results": {"result": {"media_key": "3_1829158458593099776"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/XiXCRNpjwx", "expanded_url": "https://x.com/rohanpaul_ai/status/1829158662964691159/photo/1", "id_str": "1829158458593099776", "indices": [276, 299], "media_key": "3_1829158458593099776", "media_url_https": "https://pbs.twimg.com/media/GWJ57UJXwAAW5_m.jpg", "type": "photo", "url": "https://t.co/XiXCRNpjwx", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 2048, "w": 1434, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2631, "width": 1842, "focus_rects": [{"x": 0, "y": 865, "w": 1842, "h": 1032}, {"x": 0, "y": 460, "w": 1842, "h": 1842}, {"x": 0, "y": 331, "w": 1842, "h": 2100}, {"x": 263, "y": 0, "w": 1316, "h": 2631}, {"x": 0, "y": 0, "w": 1842, "h": 2631}]}, "media_results": {"result": {"media_key": "3_1829158458593099776"}}}]}, "favorite_count": 5010, "favorited": false, "full_text": "You can Crawl entire website with Claude 3.5 or GPT4-o with this open-sourced tool firecrawl. 💯\n\nTurn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.\n\nCrawls all accessible subpages and give you clean data for each. No https://t.co/XiXCRNpjwx", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 26, "reply_count": 108, "retweet_count": 501, "retweeted": false, "user_id_str": "**********", "id_str": "1829158662964691159"}, "twe_private_fields": {"created_at": 1724940384000, "updated_at": 1748554149588, "media_count": 1}}}, {"id": "1829202906790363551", "created_at": "2024-08-29 20:02:12 +03:00", "full_text": "You have to make an API, and then it’s a paid service, why call it open source? Have you tried Crawl4ai? It’s extremely fast because it's smart enough not to overuse LLMs! Here, you pay for the LLM and the API!! Crawl4ai also provides structured JSON, which you can use with any LLM, even Ollama.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1829158662964691159", "retweeted_status": null, "quoted_status": null, "favorite_count": 94, "retweet_count": 1, "bookmark_count": 155, "quote_count": 0, "reply_count": 6, "views_count": 6269, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1829202906790363551", "metadata": {"__typename": "Tweet", "rest_id": "1829202906790363551", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MjAyMjIxNA==", "rest_id": "72022214", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1863507975438299136/Tgi1wWHR_normal.jpg"}, "core": {"created_at": "Sun Sep 06 12:11:16 +0000 2009", "name": "Uncle<PERSON> (Hossein)", "screen_name": "uncle<PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Author of Crawl4AI (#1 GitHub Trending). Founder of Kidocode, SE Asia's largest tech & biz school. Leading AI research on synthetic data, AI consultant.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "unclecode.com", "expanded_url": "https://unclecode.com/", "url": "https://t.co/s6DdP3rHzU", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2778, "followers_count": 2510, "friends_count": 315, "has_custom_timelines": false, "is_translator": false, "listed_count": 40, "media_count": 217, "normal_followers_count": 2510, "pinned_tweet_ids_str": ["1926991073362030808"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/72022214/1733628517", "profile_interstitial_type": "", "statuses_count": 2126, "translator_type": "none", "url": "https://t.co/s6DdP3rHzU", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Singapore"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1663748727960641536", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1829202906790363551"], "editable_until_msecs": "1724954532000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "6269", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4MjkyMDI5MDY3MDY1MjIxMTQ=", "text": "You have to make an API, and then it’s a paid service, why call it open source? Have you tried Crawl4ai? It’s extremely fast because it's smart enough not to overuse LLMs! Here, you pay for the LLM and the API!! Crawl4ai also provides structured JSON, which you can use with any LLM, even Ollama.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 155, "bookmarked": true, "created_at": "Thu Aug 29 17:02:12 +0000 2024", "conversation_id_str": "1829158662964691159", "display_text_range": [14, 292], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "**********", "name": "<PERSON><PERSON><PERSON>", "screen_name": "rohanpaul_ai", "indices": [0, 13]}]}, "favorite_count": 94, "favorited": false, "full_text": "@rohanpaul_ai You have to make an API, and then it’s a paid service, why call it open source? Have you tried Crawl4ai? It’s extremely fast because it's smart enough not to overuse LLMs! Here, you pay for the LLM and the API!! Crawl4ai also provides structured JSON, which you can use with any", "in_reply_to_screen_name": "rohanpaul_ai", "in_reply_to_status_id_str": "1829158662964691159", "in_reply_to_user_id_str": "**********", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 6, "retweet_count": 1, "retweeted": false, "user_id_str": "72022214", "id_str": "1829202906790363551"}, "twe_private_fields": {"created_at": 1724950932000, "updated_at": 1748554149588, "media_count": 0}}}]