[{"id": "1551903013001764866", "created_at": "2022-07-26 15:11:25 +03:00", "full_text": "<PERSON><PERSON><PERSON>'s Roadmap for\n\n     ❍  HTML\n     ❍  CSS\n\nwith practice problems and project ideas.\n\n⇩", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2336, "retweet_count": 679, "bookmark_count": 1485, "quote_count": 12, "reply_count": 74, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1551903013001764866", "metadata": {"__typename": "Tweet", "rest_id": "1551903013001764866", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204229, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204229, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1551903013001764866"], "editable_until_msecs": "1658839285000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1485, "bookmarked": true, "created_at": "<PERSON><PERSON> 26 12:11:25 +0000 2022", "conversation_id_str": "1551903013001764866", "display_text_range": [0, 94], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2336, "favorited": false, "full_text": "<PERSON><PERSON><PERSON>'s Roadmap for\n\n     ❍  HTML\n     ❍  CSS\n\nwith practice problems and project ideas.\n\n⇩", "is_quote_status": false, "lang": "en", "quote_count": 12, "reply_count": 74, "retweet_count": 679, "retweeted": false, "user_id_str": "69941978", "id_str": "1551903013001764866"}, "twe_private_fields": {"created_at": 1658837485000, "updated_at": 1748554255574, "media_count": 0}}}, {"id": "1552291739012063232", "created_at": "2022-07-27 16:56:05 +03:00", "full_text": "Twitter hates LinkedIn.\n\nBut I'll do 130M+ impressions &amp; $1.4M on the platform in 2022. \n\nHere's how I use LinkedIn unlike anyone else:\n\n🧵", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 7907, "retweet_count": 1366, "bookmark_count": 8934, "quote_count": 133, "reply_count": 382, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1552291739012063232", "metadata": {"__typename": "Tweet", "rest_id": "1552291739012063232", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxOTY5NDUzNg==", "rest_id": "19694536", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1365425625616556045/NDhia9nF_normal.jpg"}, "core": {"created_at": "Thu Jan 29 03:47:15 +0000 2009", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Running Unsubscribe — A private network of 800+ entrepreneurs building profitable businesses that improve their lives @ https://t.co/DY0tXerPn3", "entities": {"description": {"urls": [{"display_url": "jointheunsubscribed.co", "expanded_url": "http://jointheunsubscribed.co", "url": "https://t.co/DY0tXerPn3", "indices": [120, 143]}]}, "url": {"urls": [{"display_url": "jointheunsubscribed.co", "expanded_url": "https://jointheunsubscribed.co", "url": "https://t.co/xIvG6XWiTA", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 113933, "followers_count": 531105, "friends_count": 982, "has_custom_timelines": true, "is_translator": false, "listed_count": 8053, "media_count": 1018, "normal_followers_count": 531105, "pinned_tweet_ids_str": ["1926972099526119500"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/19694536/1747263465", "profile_interstitial_type": "", "statuses_count": 33473, "translator_type": "none", "url": "https://t.co/xIvG6XWiTA", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Join 800+ members here →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1458840996821803016", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "cash_app_handle": "$theju<PERSON><PERSON><PERSON>lsh", "venmo_handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1552291739012063232"], "editable_until_msecs": "1658931965000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 8934, "bookmarked": true, "created_at": "Wed Jul 27 13:56:05 +0000 2022", "conversation_id_str": "1552291739012063232", "display_text_range": [0, 142], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 7907, "favorited": false, "full_text": "Twitter hates LinkedIn.\n\nBut I'll do 130M+ impressions &amp; $1.4M on the platform in 2022. \n\nHere's how I use LinkedIn unlike anyone else:\n\n🧵", "is_quote_status": false, "lang": "en", "quote_count": 133, "reply_count": 382, "retweet_count": 1366, "retweeted": false, "user_id_str": "19694536", "id_str": "1552291739012063232"}, "twe_private_fields": {"created_at": 1658930165000, "updated_at": 1748554255574, "media_count": 0}}}]