# Bookmarks from bookmark_chunk_3_part_37.json

## Category: Programming / JavaScript / Learning Resources / Tutorials / Beginners

*   **Author:** @souravintech
    **Date:** 2022-08-02 04:39:29 +03:00
    **Content:** Learn JavaScript for beginners A Mega Thread 

 CallBack Function!
 Function Returning!
 Array Methods!
 Clone Objects!
 call(), apply() and bind().
 Lexical Scope!
 Default or rest parameters.
 Arithmetic Operators!
 Basic Events!
 Global and Local Variables!
    **URL:** [https://twitter.com/souravintech/status/1554280697048641536](https://twitter.com/souravintech/status/1554280697048641536)

---

## Category: Sports / Football / Fantasy Premier League / Strategy / Player Comparison / Dilemmas

*   **Author:** @FPLGOAT7
    **Date:** 2022-08-02 15:53:17 +03:00
    **Content:** A detailed THREAD regarding the most common FPL dilemmas managers face, I cover 

 <PERSON><PERSON> vs <PERSON> vs <PERSON> vs <PERSON> vs <PERSON> vs <PERSON><PERSON>
 Best 8m midfielder
 <PERSON><PERSON> vs <PERSON><PERSON>

Make sure you like retweet and share if you find it useful 
    **URL:** [https://twitter.com/FPLGOAT7/status/1554450260960870402](https://twitter.com/FPLGOAT7/status/1554450260960870402)

---
