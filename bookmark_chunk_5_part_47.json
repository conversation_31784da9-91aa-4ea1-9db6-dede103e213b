[{"id": "1886448015533089248", "created_at": "2025-02-03 19:13:50 +03:00", "full_text": "Traditional RAG sucks because it promises \"relevant chunks\" but in fact returns \"similar chunks\". \n\nRelevancy requires reasoning.\n\nIntroducing ReAG - Reasoning Augmented Generation https://t.co/DSGyGak1oi", "media": [{"type": "photo", "url": "https://t.co/DSGyGak1oi", "thumbnail": "https://pbs.twimg.com/media/Gi3qNeVWcAAjhs-?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Gi3qNeVWcAAjhs-?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3788, "retweet_count": 297, "bookmark_count": 4589, "quote_count": 58, "reply_count": 150, "views_count": 514019, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1886448015533089248", "metadata": {"__typename": "Tweet", "rest_id": "1886448015533089248", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMDIyNTQ3MA==", "rest_id": "20225470", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1564920592868016130/3eW3IZ0y_normal.jpg"}, "core": {"created_at": "Fri Feb 06 09:31:48 +0000 2009", "name": "homanp", "screen_name": "pelaseyed"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "CTO @superagent_ai", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "superagent.sh", "expanded_url": "https://superagent.sh", "url": "https://t.co/y4cURvKjAh", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7771, "followers_count": 7319, "friends_count": 422, "has_custom_timelines": true, "is_translator": false, "listed_count": 197, "media_count": 508, "normal_followers_count": 7319, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/20225470/1739822429", "profile_interstitial_type": "", "statuses_count": 5750, "translator_type": "none", "url": "https://t.co/y4cURvKjAh", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1556986726027038720", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1886448015533089248"], "editable_until_msecs": "1738602830000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "514019", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 4589, "bookmarked": true, "created_at": "Mon Feb 03 16:13:50 +0000 2025", "conversation_id_str": "1886448015533089248", "display_text_range": [0, 180], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/DSGyGak1oi", "expanded_url": "https://x.com/pelaseyed/status/1886448015533089248/photo/1", "id_str": "1886421336139919360", "indices": [181, 204], "media_key": "3_1886421336139919360", "media_url_https": "https://pbs.twimg.com/media/Gi3qNeVWcAAjhs-.jpg", "type": "photo", "url": "https://t.co/DSGyGak1oi", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 656, "w": 1766, "resize": "fit"}, "medium": {"h": 446, "w": 1200, "resize": "fit"}, "small": {"h": 253, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 656, "width": 1766, "focus_rects": [{"x": 0, "y": 0, "w": 1171, "h": 656}, {"x": 0, "y": 0, "w": 656, "h": 656}, {"x": 0, "y": 0, "w": 575, "h": 656}, {"x": 56, "y": 0, "w": 328, "h": 656}, {"x": 0, "y": 0, "w": 1766, "h": 656}]}, "media_results": {"result": {"media_key": "3_1886421336139919360"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/DSGyGak1oi", "expanded_url": "https://x.com/pelaseyed/status/1886448015533089248/photo/1", "id_str": "1886421336139919360", "indices": [181, 204], "media_key": "3_1886421336139919360", "media_url_https": "https://pbs.twimg.com/media/Gi3qNeVWcAAjhs-.jpg", "type": "photo", "url": "https://t.co/DSGyGak1oi", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 656, "w": 1766, "resize": "fit"}, "medium": {"h": 446, "w": 1200, "resize": "fit"}, "small": {"h": 253, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 656, "width": 1766, "focus_rects": [{"x": 0, "y": 0, "w": 1171, "h": 656}, {"x": 0, "y": 0, "w": 656, "h": 656}, {"x": 0, "y": 0, "w": 575, "h": 656}, {"x": 56, "y": 0, "w": 328, "h": 656}, {"x": 0, "y": 0, "w": 1766, "h": 656}]}, "media_results": {"result": {"media_key": "3_1886421336139919360"}}}]}, "favorite_count": 3788, "favorited": false, "full_text": "Traditional RAG sucks because it promises \"relevant chunks\" but in fact returns \"similar chunks\". \n\nRelevancy requires reasoning.\n\nIntroducing ReAG - Reasoning Augmented Generation https://t.co/DSGyGak1oi", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 58, "reply_count": 150, "retweet_count": 297, "retweeted": false, "user_id_str": "20225470", "id_str": "1886448015533089248"}, "twe_private_fields": {"created_at": 1738599230000, "updated_at": 1748554112349, "media_count": 1}}}, {"id": "1886547542936314233", "created_at": "2025-02-04 01:49:19 +03:00", "full_text": "True Power of C & Assembly: The Art of Writing Code That Outruns Compilers 🚀\nEver wondered why some programs run blazingly fast even when written decades ago? It’s not magic it’s the mastery of C and Assembly, where every instruction counts, and every cycle matters.\n\nHere’s the deal:\nModern compilers are smart, but they’ll never replace the raw control you get with Assembly blended into your C code. Knowing when to drop down to Assembly isn’t just a flex it’s how you:\n\n•Optimize critical code paths beyond what the compiler can do\n\n•Reduce latency in real-time systems\n\n•Shrink binaries for embedded devices\n\n•Understand the machine like you’re speaking its native language\n\n🔥Pro Tips for Mastering C + Assembly:\n\n1. Inline Assembly Isn’t Dead: GCC’s asm() can still outpace high-level optimizations in tight loops.\n\n2. Profile First, Optimize Later: Don’t guess use tools like gprof or perf to spot real bottlenecks.\n\n3. Leverage CPU Instructions: SIMD, prefetching, and cache-aligned data can be game-changers.\n\n4. Know Your ABI: Mastering calling conventions helps blend C and Assembly seamlessly.\n\nMastering these techniques turns good code into great code where performance isn’t just a goal, it’s the default.", "media": [{"type": "photo", "url": "https://t.co/djJoGSSHnl", "thumbnail": "https://pbs.twimg.com/media/Gi5c_cNa4AQXEzU?format=png&name=thumb", "original": "https://pbs.twimg.com/media/Gi5c_cNa4AQXEzU?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 194, "retweet_count": 19, "bookmark_count": 108, "quote_count": 1, "reply_count": 2, "views_count": 8182, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1886547542936314233", "metadata": {"__typename": "Tweet", "rest_id": "1886547542936314233", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODA0MDIyNzM5MzE1MDgxMjE2", "rest_id": "1804022739315081216", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1855208199886151680/JMNDPARZ_normal.jpg"}, "core": {"created_at": "Fri Jun 21 05:25:29 +0000 2024", "name": "Quantum 𝕏", "screen_name": "Quantm_X"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Tuning into the frequencies of time, thought, and the unknown.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 23499, "followers_count": 2802, "friends_count": 160, "has_custom_timelines": false, "is_translator": false, "listed_count": 6, "media_count": 2798, "normal_followers_count": 2802, "pinned_tweet_ids_str": ["1926626550172049504"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1804022739315081216/1745434487", "profile_interstitial_type": "", "statuses_count": 6300, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1810966283929952321", "professional_type": "Creator", "category": [{"id": 994, "name": "Content Publisher", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bandcamp_handle": "", "patreon_handle": "", "venmo_handle": ""}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1886547542936314233"], "editable_until_msecs": "1738626559000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "8182", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4ODY1NDc1NDI3ODk1MTMyMTg=", "text": "True Power of C & Assembly: The Art of Writing Code That Outruns Compilers 🚀\nEver wondered why some programs run blazingly fast even when written decades ago? It’s not magic it’s the mastery of C and Assembly, where every instruction counts, and every cycle matters.\n\nHere’s the deal:\nModern compilers are smart, but they’ll never replace the raw control you get with Assembly blended into your C code. Knowing when to drop down to Assembly isn’t just a flex it’s how you:\n\n•Optimize critical code paths beyond what the compiler can do\n\n•Reduce latency in real-time systems\n\n•Shrink binaries for embedded devices\n\n•Understand the machine like you’re speaking its native language\n\n🔥Pro Tips for Mastering C + Assembly:\n\n1. Inline Assembly Isn’t Dead: GCC’s asm() can still outpace high-level optimizations in tight loops.\n\n2. Profile First, Optimize Later: Don’t guess use tools like gprof or perf to spot real bottlenecks.\n\n3. Leverage CPU Instructions: SIMD, prefetching, and cache-aligned data can be game-changers.\n\n4. Know Your ABI: Mastering calling conventions helps blend C and Assembly seamlessly.\n\nMastering these techniques turns good code into great code where performance isn’t just a goal, it’s the default.", "entity_set": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 0, "to_index": 75, "richtext_types": ["Bold"]}]}}}}, "grok_analysis_button": true, "community_results": {"result": {"__typename": "Community", "id_str": "17839905***********", "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}}}, "author_community_relationship": {"community_results": {"result": {"__typename": "Community", "id_str": "17839905***********", "name": "C and Assembly Developers", "description": "This community is geared towards accelerated knowledge of low-level computing concepts, and active contribution to projects in C and ASM.", "created_at": *************, "question": "THIS IS MANDITORY:\n\nYour GitHub account or a snippet of code you've written in C or Assembly that you're proud of.", "search_tags": ["programming", "c", "asm", "softwaredevelopment", "cprogramming", "lowlevel", "code", "math", "ai", "ml"], "is_nsfw": false, "primary_community_topic": {"topic_id": "303", "topic_name": "Software"}, "actions": {"delete_action_result": {"__typename": "CommunityDeleteActionUnavailable", "reason": "Unavailable"}, "join_action_result": {"__typename": "CommunityJoinActionUnavailable", "reason": "ViewerIsMember", "message": "You are already a member."}, "leave_action_result": {"__typename": "CommunityLeaveAction"}, "pin_action_result": {"__typename": "CommunityTweetPinActionUnavailable"}}, "admin_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102822, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26522, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "creator_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102822, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26522, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "invites_result": {"__typename": "CommunityInvites", "remaining_invite_count": 10, "users_to_invite_slice": {"items": [], "slice_info": {}}}, "join_policy": "Open", "invites_policy": "MemberInvitesAllowed", "is_pinned": true, "members_facepile_results": [{"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102822, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26522, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDk1ODMzNzcxNTI4NDg2OTEy", "rest_id": "1095833771528486912", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1852982863295361024/nt6Wkcdk_normal.jpg"}, "core": {"created_at": "Wed Feb 13 23:55:05 +0000 2019", "name": "<PERSON><PERSON><PERSON>", "screen_name": "DisperseControl"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I am potato. e/acc. Bulltard.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "brrr.money", "expanded_url": "https://brrr.money/", "url": "https://t.co/LnifTg9G7X", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 638143, "followers_count": 4496, "friends_count": 7347, "has_custom_timelines": true, "is_translator": false, "listed_count": 23, "media_count": 26419, "normal_followers_count": 4496, "pinned_tweet_ids_str": ["1917289486897029355"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1095833771528486912/1652435782", "profile_interstitial_type": "", "statuses_count": 55745, "translator_type": "none", "url": "https://t.co/LnifTg9G7X", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Canazuela"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoyMTkyMDM0MTI=", "rest_id": "219203412", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1920040820654432256/68AZgHjH_normal.jpg"}, "core": {"created_at": "Wed Nov 24 06:15:09 +0000 2010", "name": "Erroneous Input", "screen_name": "<PERSON>_<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "what?", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 62246, "followers_count": 1341, "friends_count": 202, "has_custom_timelines": false, "is_translator": false, "listed_count": 63, "media_count": 18475, "normal_followers_count": 1341, "pinned_tweet_ids_str": ["1777265833670262905"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/219203412/1721665890", "profile_interstitial_type": "", "statuses_count": 79937, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDQxNjUxNjg5MDQyNzg4MzUy", "rest_id": "1041651689042788352", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1846890999634681856/iixBL8be_normal.jpg"}, "core": {"created_at": "Mon Sep 17 11:34:50 +0000 2018", "name": "faulty *ptrrr", "screen_name": "0x_shaq"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Chief pager engineer 📟", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pwner.gg/blog/", "expanded_url": "https://pwner.gg/blog/", "url": "https://t.co/VVHKg0umzt", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10259, "followers_count": 4694, "friends_count": 364, "has_custom_timelines": true, "is_translator": false, "listed_count": 24, "media_count": 604, "normal_followers_count": 4694, "pinned_tweet_ids_str": ["1794342802173890682"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1041651689042788352/1659977667", "profile_interstitial_type": "", "statuses_count": 2346, "translator_type": "none", "url": "https://t.co/VVHKg0umzt", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🇮🇱Israel"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxNTAxMTU2Mzk2NTA2OTM5Mzk1", "rest_id": "1501156396506939395", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1810298224382763008/VVGhTJi4_normal.jpg"}, "core": {"created_at": "<PERSON>e Mar 08 11:23:25 +0000 2022", "name": "<PERSON>raw 🍓", "screen_name": "ddnnddc"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Technologist, major node in the cat distribution system.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/playlist?list=…", "expanded_url": "https://www.youtube.com/playlist?list=PLdVyg0SqO9v_FMdp7oPhmlMSP0kRpziVg", "url": "https://t.co/Itnc8qT9Ow", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 49644, "followers_count": 434, "friends_count": 951, "has_custom_timelines": false, "is_translator": false, "listed_count": 2, "media_count": 960, "normal_followers_count": 434, "pinned_tweet_ids_str": ["1832506180339716148"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1501156396506939395/1720444898", "profile_interstitial_type": "", "statuses_count": 8050, "translator_type": "none", "url": "https://t.co/Itnc8qT9Ow", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Melbourne, Australia"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1693828904694563112", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}], "moderator_count": 9, "member_count": 49694, "role": "Member", "rules": [{"rest_id": "1840492394057511216", "name": "Keep posts on topic.", "description": "Focus discussions on C, Assembly programming, and related topics. Avoid posting unrelated content to maintain quality. This implies no politics.."}, {"rest_id": "1783990929403363417", "name": "Explore and share.", "description": "Encourage curiosity and learning. Share your experiences, code snippets, and resources. Help others by contributing to the collective knowledge."}, {"rest_id": "1783990992099848586", "name": "Provide constructive feedback.", "description": "When critiquing code or concepts, offer clear, constructive, and polite feedback. Aim to help others improve and learn."}, {"rest_id": "1785457160471908430", "name": "Privacy and security.", "description": "Respect the privacy and the integrity of the community."}, {"rest_id": "1795107937150730598", "name": "Low effort bait questions lead to permanent removal.", "description": "Posts that use shallow questions to boost engagement risk permanent removal to uphold discussion quality."}, {"rest_id": "1840493930280014028", "name": "Suggestion-Only Policy: DM @7etsuo for a groupchat invite.", "description": "By joining the community group chat, you agree to keep it a space for suggestions, C-related questions, sharing work, and community suggestions."}], "custom_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 21, "green": 194, "blue": 225}, "percentage": 25.43}, {"rgb": {"red": 58, "green": 154, "blue": 255}, "percentage": 11.73}, {"rgb": {"red": 243, "green": 244, "blue": 250}, "percentage": 10.8}, {"rgb": {"red": 20, "green": 190, "blue": 12}, "percentage": 8.56}, {"rgb": {"red": 226, "green": 97, "blue": 162}, "percentage": 8.44}]}, "original_img_url": "https://pbs.twimg.com/community_banner_img/1827118393209618432/I8no5S8w?format=jpg&name=orig", "original_img_width": 1200, "original_img_height": 480, "salient_rect": {"left": 601, "top": 240, "width": 1, "height": 1}}}, "default_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 1, "green": 161, "blue": 155}, "percentage": 79.35}, {"rgb": {"red": 248, "green": 120, "blue": 132}, "percentage": 11.83}, {"rgb": {"red": 212, "green": 133, "blue": 146}, "percentage": 2.97}, {"rgb": {"red": 129, "green": 175, "blue": 168}, "percentage": 1.95}, {"rgb": {"red": 244, "green": 84, "blue": 97}, "percentage": 0.81}]}, "original_img_url": "https://pbs.twimg.com/media/FECQY8MVEAEnZBg.jpg", "original_img_width": 1200, "original_img_height": 480}}, "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}, "join_requests_result": {"__typename": "CommunityJoinRequestsUnavailable"}}}, "role": "NonMember", "user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODA0MDIyNzM5MzE1MDgxMjE2", "rest_id": "1804022739315081216", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1855208199886151680/JMNDPARZ_normal.jpg"}, "core": {"created_at": "Fri Jun 21 05:25:29 +0000 2024", "name": "Quantum 𝕏", "screen_name": "Quantm_X"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Tuning into the frequencies of time, thought, and the unknown.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 23499, "followers_count": 2802, "friends_count": 160, "has_custom_timelines": false, "is_translator": false, "listed_count": 6, "media_count": 2798, "normal_followers_count": 2802, "pinned_tweet_ids_str": ["1926626550172049504"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1804022739315081216/1745434487", "profile_interstitial_type": "", "statuses_count": 6300, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1810966283929952321", "professional_type": "Creator", "category": [{"id": 994, "name": "Content Publisher", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bandcamp_handle": "", "patreon_handle": "", "venmo_handle": ""}, "verification": {"verified": false}}}}, "legacy": {"bookmark_count": 108, "bookmarked": true, "created_at": "Mon Feb 03 22:49:19 +0000 2025", "conversation_id_str": "1886547542936314233", "display_text_range": [0, 282], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/djJoGSSHnl", "expanded_url": "https://x.com/Quantuminno_X/status/1886547542936314233/photo/1", "id_str": "1886547538888810500", "indices": [283, 306], "media_key": "3_1886547538888810500", "media_url_https": "https://pbs.twimg.com/media/Gi5c_cNa4AQXEzU.png", "type": "photo", "url": "https://t.co/djJoGSSHnl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 477, "w": 375, "resize": "fit"}, "medium": {"h": 477, "w": 375, "resize": "fit"}, "small": {"h": 477, "w": 375, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 477, "width": 375, "focus_rects": [{"x": 0, "y": 0, "w": 375, "h": 210}, {"x": 0, "y": 0, "w": 375, "h": 375}, {"x": 0, "y": 0, "w": 375, "h": 428}, {"x": 59, "y": 0, "w": 239, "h": 477}, {"x": 0, "y": 0, "w": 375, "h": 477}]}, "media_results": {"result": {"media_key": "3_1886547538888810500"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/djJoGSSHnl", "expanded_url": "https://x.com/Quantuminno_X/status/1886547542936314233/photo/1", "id_str": "1886547538888810500", "indices": [283, 306], "media_key": "3_1886547538888810500", "media_url_https": "https://pbs.twimg.com/media/Gi5c_cNa4AQXEzU.png", "type": "photo", "url": "https://t.co/djJoGSSHnl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 477, "w": 375, "resize": "fit"}, "medium": {"h": 477, "w": 375, "resize": "fit"}, "small": {"h": 477, "w": 375, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 477, "width": 375, "focus_rects": [{"x": 0, "y": 0, "w": 375, "h": 210}, {"x": 0, "y": 0, "w": 375, "h": 375}, {"x": 0, "y": 0, "w": 375, "h": 428}, {"x": 59, "y": 0, "w": 239, "h": 477}, {"x": 0, "y": 0, "w": 375, "h": 477}]}, "media_results": {"result": {"media_key": "3_1886547538888810500"}}}]}, "favorite_count": 194, "favorited": false, "full_text": "True Power of C &amp; Assembly: The Art of Writing Code That Outruns Compilers 🚀\nEver wondered why some programs run blazingly fast even when written decades ago? It’s not magic it’s the mastery of C and Assembly, where every instruction counts, and every cycle matters.\n\nHere’s the https://t.co/djJoGSSHnl", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 2, "retweet_count": 19, "retweeted": false, "user_id_str": "1804022739315081216", "id_str": "1886547542936314233"}, "twe_private_fields": {"created_at": 1738622959000, "updated_at": 1748554099723, "media_count": 1}}}]