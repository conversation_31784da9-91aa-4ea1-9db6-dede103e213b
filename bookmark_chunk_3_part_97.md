# Bookmarks from bookmark_chunk_3_part_97.json

## Category: Technology / Programming / Learning Resources / Free Courses / Infographics

*   **Author:** @ChrisStaud
    **Date:** 2022-12-10 09:27:42 +03:00
    **Content:** Learn Programming For Free👇 https://t.co/nhffZOviqy
    **URL:** [https://twitter.com/ChrisStaud/status/1601463649020821504](https://twitter.com/ChrisStaud/status/1601463649020821504)
    **Media:**
      - Type: photo
      - URL: https://t.co/nhffZOviqy
      - Thumbnail: https://pbs.twimg.com/media/FjmKsp1WYAE-l9P?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/FjmKsp1WYAE-l9P?format=jpg&name=orig
      - Alt Text: Learn Programming For Free

---

## Category: Technology / Linux / Command Line Tools / Grep / Sed / Awk / Learning Resources / Books / Cheatsheets

*   **Author:** @learnbyexample
    **Date:** 2022-12-16 13:18:40 +03:00
    **Content:** @Cheerio95460970 @nixcraft I wrote books on GNU grep, sed and awk (among others) with plenty of examples and exercises. Free to read online (https://t.co/zJjdRuVXjf).

I'd also highly recommend maintaining a cheatsheet for your common use cases (would be quicker than searching docs, books, etc).
    **URL:** [https://twitter.com/learnbyexample/status/1603696103396085760](https://twitter.com/learnbyexample/status/1603696103396085760)
    **External Links:**
      - [https://learnbyexample.github.io/books/](https://learnbyexample.github.io/books/)

---
