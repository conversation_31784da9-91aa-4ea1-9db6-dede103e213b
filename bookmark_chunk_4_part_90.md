# Bookmark Chunk 4 Part 90

## Tweet 1

**Author:** @7etsuo
**Tweet URL:** [https://twitter.com/7etsuo/status/1841260078974091309](https://twitter.com/7etsuo/status/1841260078974091309)
**Timestamp:** 2024-10-02 02:33:06 +03:00
**Category:** Technology / Programming / C / Education / Video

**Content:**
🧵1/n <PERSON> from Stanford University explains pointers and structs in C, showing a clever way to access struct fields. This series is one of the best resources online for C programming.

Source: Stanford University

👇 Lectures with handouts, assignments, and videos. https://t.co/Z77n7Q6WEr

**Media:**
- Type: video
- URL: https://t.co/Z77n7Q6WEr

---

## Tweet 2

**Author:** @svpino
**Tweet URL:** [https://twitter.com/svpino/status/1841434400200749132](https://twitter.com/svpino/status/1841434400200749132)
**Timestamp:** 2024-10-02 14:05:49 +03:00
**Category:** Technology / AI / LLM / Prompt Engineering / Thread

**Content:**
I'm going to show you how to create an AI agent that can hold a conversation.

No, I'm not talking about a chatbot. I'm talking about an agent that can remember what you said earlier in the conversation and use that information to inform its responses.

This is a game-changer for building AI applications.

Here's how to do it using LangChain and OpenAI models.

🧵👇

#ai #llm #python #langchain https://t.co/0302aFkCgT

---