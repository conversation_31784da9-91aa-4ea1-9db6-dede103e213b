<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twitter Bookmarks Viewer</title>
    <style>
        :root {
            --bg-color: #f5f8fa;
            --card-bg: #ffffff;
            --text-color: #14171a;
            --border-color: #e1e8ed;
            --primary-color: #1da1f2;
            --category-bg: #e8f5fe;
            --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .dark-mode {
            --bg-color: #15202b;
            --card-bg: #192734;
            --text-color: #f5f8fa;
            --border-color: #38444d;
            --primary-color: #1da1f2;
            --category-bg: #1e3a5f;
            --shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.5;
            transition: background-color 0.3s, color 0.3s;
        }

        header {
            background-color: var(--card-bg);
            padding: 1rem 2rem;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-box {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 50px;
            background: var(--bg-color);
            color: var(--text-color);
            width: 250px;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0d8cd9;
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            gap: 2rem;
        }

        .sidebar {
            flex: 0 0 250px;
            position: sticky;
            top: 100px;
            align-self: flex-start;
            background: var(--card-bg);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .category-list {
            list-style: none;
        }

        .category-item {
            margin-bottom: 0.5rem;
        }

        .category-link {
            display: block;
            padding: 0.75rem 1rem;
            background: var(--category-bg);
            color: var(--primary-color);
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.2s;
        }

        .category-link:hover, .category-link.active {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
        }

        .bookmark-card {
            background: var(--card-bg);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: transform 0.3s;
        }

        .bookmark-card:hover {
            transform: translateY(-5px);
        }

        .card-content {
            padding: 1.5rem;
        }

        .card-category {
            display: inline-block;
            background: var(--category-bg);
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .card-author {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .card-date {
            color: #657786;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .card-text {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .card-link {
            display: inline-block;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: bold;
            margin-top: 1rem;
        }

        .card-link:hover {
            text-decoration: underline;
        }

        footer {
            text-align: center;
            padding: 2rem;
            color: #657786;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            .sidebar {
                position: relative;
                top: 0;
                width: 100%;
            }
            .controls {
                margin-top: 1rem;
                width: 100%;
            }
            .search-box {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">Twitter Bookmarks Viewer</div>
        <div class="controls">
            <input type="text" class="search-box" placeholder="Search bookmarks...">
            <button class="btn theme-toggle">Toggle Theme</button>
        </div>
    </header>

    <div class="container">
        <aside class="sidebar">
            <h2>Categories</h2>
            <ul class="category-list">
                <!-- Categories will be dynamically added here -->
            </ul>
        </aside>

        <main class="main-content">
            <!-- Bookmark cards will be dynamically added here -->
            <div class="bookmark-card">
                <div class="card-content">
                    <span class="card-category">Technology</span>
                    <div class="card-author">@exampleuser</div>
                    <div class="card-date">2022-01-01 12:00:00</div>
                    <p class="card-text">This is an example bookmark content preview that will be replaced with actual data.</p>
                    <a href="#" class="card-link">View on Twitter</a>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <p>Bookmark Viewer &copy; 2025 | Loaded <span id="bookmark-count">0</span> bookmarks</p>
    </footer>

    <script src="bookmark_loader.js"></script>
</body>
</html>
