[{"id": "1748007207403495627", "created_at": "2024-01-18 18:39:29 +03:00", "full_text": "The official Samsung Galaxy S24 Series ringtones, wallpapers, and video wallpapers (AOD) are now available for free download in full quality! Click on the link in this thread to access them. (Tutorial video attached).\n\nIncludes:\n- Over the Horizon (2024 version, full song and Suga of BTS Remix)\n- Samsung Galaxy S24 Ultra wallpapers\n- AOD video wallpapers", "media": [{"type": "photo", "url": "https://t.co/9mx0zzBvUW", "thumbnail": "https://pbs.twimg.com/media/GEIp1IBawAEbfGn?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GEIp1IBawAEbfGn?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/9mx0zzBvUW", "thumbnail": "https://pbs.twimg.com/media/GEIp2UmbAAAGuC4?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GEIp2UmbAAAGuC4?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/9mx0zzBvUW", "thumbnail": "https://pbs.twimg.com/media/GEIp2-naEAADbTl?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GEIp2-naEAADbTl?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 377, "retweet_count": 33, "bookmark_count": 138, "quote_count": 2, "reply_count": 21, "views_count": 42425, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1748007207403495627", "metadata": {"__typename": "Tweet", "rest_id": "1748007207403495627", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjU2MTczNjc3ODYyNTk2NjA4", "rest_id": "1656173677862596608", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1809700473655414785/3CquWTgV_normal.jpg"}, "core": {"created_at": "Wed May 10 05:46:10 +0000 2023", "name": "sid", "screen_name": "immasiddx"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Founder @skinvincible_ai. Sharing the latest in tech, AI, and politics with a sprinkle of memes. Opinions are personal.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "immasidd.com", "expanded_url": "http://immasidd.com", "url": "https://t.co/RH5pt8kw5k", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 95832, "followers_count": 9969, "friends_count": 652, "has_custom_timelines": false, "is_translator": false, "listed_count": 72, "media_count": 4844, "normal_followers_count": 9969, "pinned_tweet_ids_str": ["1669721470006857729"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1656173677862596608/1685702320", "profile_interstitial_type": "", "statuses_count": 24685, "translator_type": "none", "url": "https://t.co/RH5pt8kw5k", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Bharat 🇮🇳 "}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1721173269557264622", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1748007207403495627"], "editable_until_msecs": "1705595969000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "42425", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3NDgwMDcyMDczMTUzODYzNjg=", "text": "The official Samsung Galaxy S24 Series ringtones, wallpapers, and video wallpapers (AOD) are now available for free download in full quality! Click on the link in this thread to access them. (Tutorial video attached).\n\nIncludes:\n- Over the Horizon (2024 version, full song and Suga of BTS Remix)\n- Samsung Galaxy S24 Ultra wallpapers\n- AOD video wallpapers", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 138, "bookmarked": true, "created_at": "<PERSON><PERSON> <PERSON> 18 15:39:29 +0000 2024", "conversation_id_str": "1748007207403495627", "display_text_range": [0, 276], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/9mx0zzBvUW", "expanded_url": "https://x.com/immasiddtweets/status/1748007207403495627/photo/1", "id_str": "1748005598036410369", "indices": [277, 300], "media_key": "3_1748005598036410369", "media_url_https": "https://pbs.twimg.com/media/GEIp1IBawAEbfGn.jpg", "type": "photo", "url": "https://t.co/9mx0zzBvUW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1033, "w": 1920, "resize": "fit"}, "medium": {"h": 646, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1033, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1845, "h": 1033}, {"x": 108, "y": 0, "w": 1033, "h": 1033}, {"x": 171, "y": 0, "w": 906, "h": 1033}, {"x": 366, "y": 0, "w": 517, "h": 1033}, {"x": 0, "y": 0, "w": 1920, "h": 1033}]}, "media_results": {"result": {"media_key": "3_1748005598036410369"}}}, {"display_url": "pic.x.com/9mx0zzBvUW", "expanded_url": "https://x.com/immasiddtweets/status/1748007207403495627/photo/1", "id_str": "1748005618592710656", "indices": [277, 300], "media_key": "3_1748005618592710656", "media_url_https": "https://pbs.twimg.com/media/GEIp2UmbAAAGuC4.jpg", "type": "photo", "url": "https://t.co/9mx0zzBvUW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1033, "w": 1920, "resize": "fit"}, "medium": {"h": 646, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1033, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1845, "h": 1033}, {"x": 396, "y": 0, "w": 1033, "h": 1033}, {"x": 459, "y": 0, "w": 906, "h": 1033}, {"x": 654, "y": 0, "w": 517, "h": 1033}, {"x": 0, "y": 0, "w": 1920, "h": 1033}]}, "media_results": {"result": {"media_key": "3_1748005618592710656"}}}, {"display_url": "pic.x.com/9mx0zzBvUW", "expanded_url": "https://x.com/immasiddtweets/status/1748007207403495627/photo/1", "id_str": "1748005629871132672", "indices": [277, 300], "media_key": "3_1748005629871132672", "media_url_https": "https://pbs.twimg.com/media/GEIp2-naEAADbTl.jpg", "type": "photo", "url": "https://t.co/9mx0zzBvUW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1033, "w": 1920, "resize": "fit"}, "medium": {"h": 646, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1033, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1845, "h": 1033}, {"x": 396, "y": 0, "w": 1033, "h": 1033}, {"x": 459, "y": 0, "w": 906, "h": 1033}, {"x": 654, "y": 0, "w": 517, "h": 1033}, {"x": 0, "y": 0, "w": 1920, "h": 1033}]}, "media_results": {"result": {"media_key": "3_1748005629871132672"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/9mx0zzBvUW", "expanded_url": "https://x.com/immasiddtweets/status/1748007207403495627/photo/1", "id_str": "1748005598036410369", "indices": [277, 300], "media_key": "3_1748005598036410369", "media_url_https": "https://pbs.twimg.com/media/GEIp1IBawAEbfGn.jpg", "type": "photo", "url": "https://t.co/9mx0zzBvUW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1033, "w": 1920, "resize": "fit"}, "medium": {"h": 646, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1033, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1845, "h": 1033}, {"x": 108, "y": 0, "w": 1033, "h": 1033}, {"x": 171, "y": 0, "w": 906, "h": 1033}, {"x": 366, "y": 0, "w": 517, "h": 1033}, {"x": 0, "y": 0, "w": 1920, "h": 1033}]}, "media_results": {"result": {"media_key": "3_1748005598036410369"}}}, {"display_url": "pic.x.com/9mx0zzBvUW", "expanded_url": "https://x.com/immasiddtweets/status/1748007207403495627/photo/1", "id_str": "1748005618592710656", "indices": [277, 300], "media_key": "3_1748005618592710656", "media_url_https": "https://pbs.twimg.com/media/GEIp2UmbAAAGuC4.jpg", "type": "photo", "url": "https://t.co/9mx0zzBvUW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1033, "w": 1920, "resize": "fit"}, "medium": {"h": 646, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1033, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1845, "h": 1033}, {"x": 396, "y": 0, "w": 1033, "h": 1033}, {"x": 459, "y": 0, "w": 906, "h": 1033}, {"x": 654, "y": 0, "w": 517, "h": 1033}, {"x": 0, "y": 0, "w": 1920, "h": 1033}]}, "media_results": {"result": {"media_key": "3_1748005618592710656"}}}, {"display_url": "pic.x.com/9mx0zzBvUW", "expanded_url": "https://x.com/immasiddtweets/status/1748007207403495627/photo/1", "id_str": "1748005629871132672", "indices": [277, 300], "media_key": "3_1748005629871132672", "media_url_https": "https://pbs.twimg.com/media/GEIp2-naEAADbTl.jpg", "type": "photo", "url": "https://t.co/9mx0zzBvUW", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1033, "w": 1920, "resize": "fit"}, "medium": {"h": 646, "w": 1200, "resize": "fit"}, "small": {"h": 366, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1033, "width": 1920, "focus_rects": [{"x": 0, "y": 0, "w": 1845, "h": 1033}, {"x": 396, "y": 0, "w": 1033, "h": 1033}, {"x": 459, "y": 0, "w": 906, "h": 1033}, {"x": 654, "y": 0, "w": 517, "h": 1033}, {"x": 0, "y": 0, "w": 1920, "h": 1033}]}, "media_results": {"result": {"media_key": "3_1748005629871132672"}}}]}, "favorite_count": 377, "favorited": false, "full_text": "The official Samsung Galaxy S24 Series ringtones, wallpapers, and video wallpapers (AOD) are now available for free download in full quality! Click on the link in this thread to access them. (Tutorial video attached).\n\nIncludes:\n- Over the Horizon (2024 version, full song and https://t.co/9mx0zzBvUW", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 21, "retweet_count": 33, "retweeted": false, "user_id_str": "1656173677862596608", "id_str": "1748007207403495627"}, "twe_private_fields": {"created_at": 1705592369000, "updated_at": 1748554178748, "media_count": 3}}}, {"id": "1750345160616251533", "created_at": "2024-01-25 05:29:40 +03:00", "full_text": "👨‍💻 How to get started: Head over to the GitHub repository I published, and give a read on the ReadMe file for instructions and a tutorial on how to use the package!\n\nLink to Github: [https://t.co/JUigllgMiB]", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1750345005737324917", "retweeted_status": null, "quoted_status": null, "favorite_count": 20, "retweet_count": 2, "bookmark_count": 31, "quote_count": 1, "reply_count": 2, "views_count": 2968, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1750345160616251533", "metadata": {"__typename": "Tweet", "rest_id": "1750345160616251533", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjg5Mjg1MjUxMjk2NjA0MTYw", "rest_id": "1689285251296604160", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1824751307468492800/j0hN-WlW_normal.jpg"}, "core": {"created_at": "Wed Aug 09 14:39:53 +0000 2023", "name": "Athal", "screen_name": "athalakbar13"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "⚽️", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/muhammad-at…", "expanded_url": "https://www.linkedin.com/in/muhammad-athal-akbar-204aba21b/", "url": "https://t.co/n5Y84oz9us", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 651, "followers_count": 321, "friends_count": 219, "has_custom_timelines": false, "is_translator": false, "listed_count": 3, "media_count": 275, "normal_followers_count": 321, "pinned_tweet_ids_str": ["1829158188635369976"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1689285251296604160/1691788288", "profile_interstitial_type": "", "statuses_count": 3246, "translator_type": "none", "url": "https://t.co/n5Y84oz9us", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Pakistan"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1696753215075926298", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/JUigllgMiB", "legacy": {"binding_values": [{"key": "description", "value": {"string_value": "This is a Python Package that will allow you to analyze soccer data and visualize it - athalak13/SoccerViz-Library", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "This is a Python Package that will allow you to analyze soccer data and visualize it - athalak13/SoccerViz-Library", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "This is a Python Package that will allow you to analyze soccer data and visualize it - athalak13/SoccerViz-Library", "type": "STRING"}}, {"key": "title", "value": {"string_value": "GitHub - athalak13/SoccerViz-Library: This is a Python Package that will allow you to analyze...", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/JUigllgMiB", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/JUigllgMiB", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620142, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17800, "media_count": 2646, "normal_followers_count": 2620142, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1750345160616251533"], "editable_until_msecs": "1706153380000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "2968", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 31, "bookmarked": true, "created_at": "<PERSON><PERSON> <PERSON> 25 02:29:40 +0000 2024", "conversation_id_str": "1750344811746656329", "display_text_range": [0, 208], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/athalak13/Socc…", "expanded_url": "https://github.com/athalak13/SoccerViz-Library", "url": "https://t.co/JUigllgMiB", "indices": [184, 207]}], "user_mentions": []}, "favorite_count": 20, "favorited": false, "full_text": "👨‍💻 How to get started: Head over to the GitHub repository I published, and give a read on the ReadMe file for instructions and a tutorial on how to use the package!\n\nLink to Github: [https://t.co/JUigllgMiB]", "in_reply_to_screen_name": "athalakbar13", "in_reply_to_status_id_str": "1750345005737324917", "in_reply_to_user_id_str": "1689285251296604160", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 2, "retweet_count": 2, "retweeted": false, "user_id_str": "1689285251296604160", "id_str": "1750345160616251533"}, "twe_private_fields": {"created_at": 1706149780000, "updated_at": 1748554178747, "media_count": 0}}}]