[{"id": "1382221427273703425", "created_at": "2021-04-14 09:37:23 +03:00", "full_text": "*Here we go*\nFinally finished part I of my project Matic or rather project six - Finding a number six for Manchester United, I took a dip into the numbers to see which players could potentially be a fit for United.\nShares/feedback most appreciated!\nhttps://t.co/2UJQilD0m5", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 459, "retweet_count": 95, "bookmark_count": 88, "quote_count": 25, "reply_count": 54, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1382221427273703425", "metadata": {"__typename": "Tweet", "rest_id": "1382221427273703425", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyOTIxMTE2NDg4", "rest_id": "2921116488", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1836005518411747329/DNbhanPf_normal.jpg"}, "core": {"created_at": "Sun Dec 07 02:18:04 +0000 2014", "name": "<PERSON><PERSON>", "screen_name": "NinadB_06"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "✍️Freelance sports journalist\n📱 Social Media and Features for @Bundesliga_EN \nFt. BBC Sport and Premier League, amongst others.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "muckrack.com/ninad-barbadik…", "expanded_url": "https://muckrack.com/ninad-barbadikar-1", "url": "https://t.co/ubviovS9uT", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 31187, "followers_count": 10523, "friends_count": 3035, "has_custom_timelines": true, "is_translator": false, "listed_count": 168, "media_count": 2412, "normal_followers_count": 10523, "pinned_tweet_ids_str": ["1764987672089563266"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2921116488/1725319155", "profile_interstitial_type": "", "statuses_count": 26908, "translator_type": "none", "url": "https://t.co/ubviovS9uT", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Visakhapatnam "}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1600566955626348558", "professional_type": "Creator", "category": [{"id": 955, "name": "Journalist", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/2UJQilD0m5", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.82306", "type": "STRING"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 150, "width": 267, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=280x150"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "“Let’s talk about six, baby!”", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "ninad06.medium.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 320, "width": 569, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=800x320_1"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 675, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "*********", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 81, "width": 144, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "app_num_ratings", "value": {"string_value": "148,385", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 675, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 675, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "ninad06.medium.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "app_name", "value": {"string_value": "Medium: Read & Write Stories", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 98.16}, {"rgb": {"blue": 143, "green": 143, "red": 143}, "percentage": 1.71}, {"rgb": {"blue": 14, "green": 14, "red": 14}, "percentage": 0.13}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "Finding a number six for Manchester United — part I", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 98.16}, {"rgb": {"blue": 143, "green": 143, "red": 143}, "percentage": 1.71}, {"rgb": {"blue": 14, "green": 14, "red": 14}, "percentage": 0.13}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 675, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 98.16}, {"rgb": {"blue": 143, "green": 143, "red": 143}, "percentage": 1.71}, {"rgb": {"blue": 14, "green": 14, "red": 14}, "percentage": 0.13}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 675, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/2UJQilD0m5", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 675, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927658711331549184/l-mDUmR3?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/2UJQilD0m5", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjo1NzEyMDIxMDM=", "rest_id": "*********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1825934378192564224/Z-TG086Z_normal.jpg"}, "core": {"created_at": "Fri May 04 20:16:39 +0000 2012", "name": "Medium", "screen_name": "Medium"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Everyone has a story to tell; find ours on Medium, Instagram/Threads (@medium), LinkedIn and https://t.co/zVH9KVhzvr", "entities": {"description": {"urls": [{"display_url": "medium.com/blog/newslette…", "expanded_url": "https://medium.com/blog/newsletters/medium-daily-edition", "url": "https://t.co/zVH9KVhzvr", "indices": [93, 116]}]}, "url": {"urls": [{"display_url": "medium.com", "expanded_url": "http://medium.com", "url": "https://t.co/SbA6IT79Vn", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 5672, "followers_count": 2060830, "friends_count": 367, "has_custom_timelines": true, "is_translator": false, "listed_count": 11338, "media_count": 1040, "normal_followers_count": 2060830, "pinned_tweet_ids_str": ["1730329661450715157"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/*********/**********", "profile_interstitial_type": "", "statuses_count": 48723, "translator_type": "regular", "url": "https://t.co/SbA6IT79Vn", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Not updating this account "}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1382221427273703425"], "editable_until_msecs": "*************", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 88, "bookmarked": true, "created_at": "Wed Apr 14 06:37:23 +0000 2021", "conversation_id_str": "1382221427273703425", "display_text_range": [0, 272], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "ninad06.medium.com/finding-a-numb…", "expanded_url": "https://ninad06.medium.com/finding-a-number-six-for-manchester-united-part-i-e3fefe1e5536", "url": "https://t.co/2UJQilD0m5", "indices": [249, 272]}], "user_mentions": []}, "favorite_count": 459, "favorited": false, "full_text": "*Here we go*\nFinally finished part I of my project Matic or rather project six - Finding a number six for Manchester United, I took a dip into the numbers to see which players could potentially be a fit for United.\nShares/feedback most appreciated!\nhttps://t.co/2UJQilD0m5", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 25, "reply_count": 54, "retweet_count": 95, "retweeted": false, "user_id_str": "2921116488", "id_str": "1382221427273703425"}, "twe_private_fields": {"created_at": 1618382243000, "updated_at": 1748554424671, "media_count": 0}}}, {"id": "1382651104714973185", "created_at": "2021-04-15 14:04:46 +03:00", "full_text": "The Review: GW 31 - A thread \n\nGreetings everyone, welcome to my latest thread where I discuss the upcoming double gameweek with particular focus on triple captaincy:\n\n#FPL #FPLCommunity @OfficialFPL  \n\n(Data taken from @FFScout and @FFH_HQ with consent) https://t.co/RjnD3TNPzx", "media": [{"type": "photo", "url": "https://t.co/RjnD3TNPzx", "thumbnail": "https://pbs.twimg.com/media/EzAoUavWEAcKY1P?format=png&name=thumb", "original": "https://pbs.twimg.com/media/EzAoUavWEAcKY1P?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 903, "retweet_count": 178, "bookmark_count": 48, "quote_count": 30, "reply_count": 27, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1382651104714973185", "metadata": {"__typename": "Tweet", "rest_id": "1382651104714973185", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNzQ5NDc5OTY=", "rest_id": "274947996", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1459686915498819587/cYF4VOWO_normal.jpg"}, "core": {"created_at": "Thu Mar 31 10:12:41 +0000 2011", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "Big<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Win at #FPL: https://t.co/3OX2N2WRLl | Former FPL rank #4 | Ambassador @FFH_HQ |   Instagram: https://t.co/k25uuGJgRE | Contact: <EMAIL>", "entities": {"description": {"urls": [{"display_url": "bit.ly/3OGcv0O", "expanded_url": "http://bit.ly/3OGcv0O", "url": "https://t.co/3OX2N2WRLl", "indices": [13, 36]}, {"display_url": "instagram.com/bigmanbakar", "expanded_url": "http://instagram.com/bigman<PERSON>kar", "url": "https://t.co/k25uuGJgRE", "indices": [94, 117]}]}, "url": {"urls": [{"display_url": "fplbrief.com", "expanded_url": "http://fplbrief.com", "url": "https://t.co/YyzUM8A9u6", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 27254, "followers_count": 169378, "friends_count": 3049, "has_custom_timelines": true, "is_translator": false, "listed_count": 2808, "media_count": 2966, "normal_followers_count": 169378, "pinned_tweet_ids_str": ["1926693900825051475"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/274947996/1627383539", "profile_interstitial_type": "", "statuses_count": 41472, "translator_type": "none", "url": "https://t.co/YyzUM8A9u6", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1382651104714973185"], "editable_until_msecs": "1618486486436", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 48, "bookmarked": true, "created_at": "Thu Apr 15 11:04:46 +0000 2021", "conversation_id_str": "1382651104714973185", "display_text_range": [0, 254], "entities": {"hashtags": [{"indices": [168, 172], "text": "FPL"}, {"indices": [173, 186], "text": "FPLCommunity"}], "media": [{"display_url": "pic.x.com/RjnD3TNPzx", "expanded_url": "https://x.com/BigManBakar/status/1382651104714973185/photo/1", "id_str": "1382649416843137031", "indices": [255, 278], "media_key": "3_1382649416843137031", "media_url_https": "https://pbs.twimg.com/media/EzAoUavWEAcKY1P.png", "type": "photo", "url": "https://t.co/RjnD3TNPzx", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1076, "w": 1912, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1076, "width": 1912, "focus_rects": [{"x": 0, "y": 0, "w": 1912, "h": 1071}, {"x": 418, "y": 0, "w": 1076, "h": 1076}, {"x": 484, "y": 0, "w": 944, "h": 1076}, {"x": 687, "y": 0, "w": 538, "h": 1076}, {"x": 0, "y": 0, "w": 1912, "h": 1076}]}, "media_results": {"result": {"media_key": "3_1382649416843137031"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "761568335138058240", "name": "Fantasy Premier League", "screen_name": "OfficialFPL", "indices": [187, 199]}, {"id_str": "21189279", "name": "Fantasy Football Scout", "screen_name": "FFScout", "indices": [220, 228]}, {"id_str": "899965155647381505", "name": "Fantasy Football Hub", "screen_name": "FFH_HQ", "indices": [233, 240]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/RjnD3TNPzx", "expanded_url": "https://x.com/BigManBakar/status/1382651104714973185/photo/1", "id_str": "1382649416843137031", "indices": [255, 278], "media_key": "3_1382649416843137031", "media_url_https": "https://pbs.twimg.com/media/EzAoUavWEAcKY1P.png", "type": "photo", "url": "https://t.co/RjnD3TNPzx", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1076, "w": 1912, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1076, "width": 1912, "focus_rects": [{"x": 0, "y": 0, "w": 1912, "h": 1071}, {"x": 418, "y": 0, "w": 1076, "h": 1076}, {"x": 484, "y": 0, "w": 944, "h": 1076}, {"x": 687, "y": 0, "w": 538, "h": 1076}, {"x": 0, "y": 0, "w": 1912, "h": 1076}]}, "media_results": {"result": {"media_key": "3_1382649416843137031"}}}]}, "favorite_count": 903, "favorited": false, "full_text": "The Review: GW 31 - A thread \n\nGreetings everyone, welcome to my latest thread where I discuss the upcoming double gameweek with particular focus on triple captaincy:\n\n#FPL #FPLCommunity @OfficialFPL  \n\n(Data taken from @FFScout and @FFH_HQ with consent) https://t.co/RjnD3TNPzx", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 30, "reply_count": 27, "retweet_count": 178, "retweeted": false, "user_id_str": "274947996", "id_str": "1382651104714973185"}, "twe_private_fields": {"created_at": 1618484686000, "updated_at": 1748554424671, "media_count": 1}}}]