[{"id": "1606801628476088321", "created_at": "2022-12-25 02:58:55 +03:00", "full_text": "A Quick Linux Tip. 🐧💡\n\nUse this command to get the password of the currently connected Wi-Fi network.\n\n'nmcli device wifi show-password' https://t.co/MoEedRyHk9", "media": [{"type": "photo", "url": "https://t.co/MoEedRyHk9", "thumbnail": "https://pbs.twimg.com/media/FkyBkMQXgAIc1WB?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FkyBkMQXgAIc1WB?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1887, "retweet_count": 302, "bookmark_count": 618, "quote_count": 8, "reply_count": 40, "views_count": 137468, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1606801628476088321", "metadata": {"__typename": "Tweet", "rest_id": "1606801628476088321", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTAwNzkxMTE2NTc3NzkyMDU=", "rest_id": "850079111657779205", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/875078837100589057/2fNZ7_g6_normal.jpg"}, "core": {"created_at": "Thu Apr 06 20:13:48 +0000 2017", "name": "It's FOSS", "screen_name": "itsfoss2"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Linux and Open Source Web Portal 🐧\n\nFollow us to\n- Get the latest Linux and Open Source news 📰\n- Learn Linux tips and tutorials 💡\n- Enjoy Linux memes 🤣", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "itsfoss.com", "expanded_url": "https://itsfoss.com/", "url": "https://t.co/ruOsDaNFFH", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 20834, "followers_count": 143604, "friends_count": 147, "has_custom_timelines": true, "is_translator": false, "listed_count": 1251, "media_count": 2065, "normal_followers_count": 143604, "pinned_tweet_ids_str": ["1839579934668243211"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/850079111657779205/1644405372", "profile_interstitial_type": "", "statuses_count": 22358, "translator_type": "none", "url": "https://t.co/ruOsDaNFFH", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1467483842638274566", "professional_type": "Business", "category": [{"id": 144, "name": "Education", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1606801628476088321"], "editable_until_msecs": "1671928135000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "137468", "state": "EnabledWithCount"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 618, "bookmarked": true, "created_at": "Sat Dec 24 23:58:55 +0000 2022", "conversation_id_str": "1606801628476088321", "display_text_range": [0, 136], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/MoEedRyHk9", "expanded_url": "https://x.com/itsfoss2/status/1606801628476088321/photo/1", "id_str": "1606801625829572610", "indices": [137, 160], "media_key": "3_1606801625829572610", "media_url_https": "https://pbs.twimg.com/media/FkyBkMQXgAIc1WB.jpg", "type": "photo", "url": "https://t.co/MoEedRyHk9", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 720, "w": 1094, "resize": "fit"}, "medium": {"h": 720, "w": 1094, "resize": "fit"}, "small": {"h": 448, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 720, "width": 1094, "focus_rects": [{"x": 0, "y": 0, "w": 1094, "h": 613}, {"x": 0, "y": 0, "w": 720, "h": 720}, {"x": 0, "y": 0, "w": 632, "h": 720}, {"x": 11, "y": 0, "w": 360, "h": 720}, {"x": 0, "y": 0, "w": 1094, "h": 720}]}, "media_results": {"result": {"media_key": "3_1606801625829572610"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/MoEedRyHk9", "expanded_url": "https://x.com/itsfoss2/status/1606801628476088321/photo/1", "id_str": "1606801625829572610", "indices": [137, 160], "media_key": "3_1606801625829572610", "media_url_https": "https://pbs.twimg.com/media/FkyBkMQXgAIc1WB.jpg", "type": "photo", "url": "https://t.co/MoEedRyHk9", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 720, "w": 1094, "resize": "fit"}, "medium": {"h": 720, "w": 1094, "resize": "fit"}, "small": {"h": 448, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 720, "width": 1094, "focus_rects": [{"x": 0, "y": 0, "w": 1094, "h": 613}, {"x": 0, "y": 0, "w": 720, "h": 720}, {"x": 0, "y": 0, "w": 632, "h": 720}, {"x": 11, "y": 0, "w": 360, "h": 720}, {"x": 0, "y": 0, "w": 1094, "h": 720}]}, "media_results": {"result": {"media_key": "3_1606801625829572610"}}}]}, "favorite_count": 1887, "favorited": false, "full_text": "A Quick Linux Tip. 🐧💡\n\nUse this command to get the password of the currently connected Wi-Fi network.\n\n'nmcli device wifi show-password' https://t.co/MoEedRyHk9", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 8, "reply_count": 40, "retweet_count": 302, "retweeted": false, "user_id_str": "850079111657779205", "id_str": "1606801628476088321"}, "twe_private_fields": {"created_at": 1671926335000, "updated_at": 1748554206264, "media_count": 1}}}, {"id": "1607123433506177025", "created_at": "2022-12-26 00:17:40 +03:00", "full_text": "We are delighted to announce that as of today, the RPCS3 Loadable compatibility category has reached 0 GAMES!\n\nThis means there are no PS3 games left that boot to a black screen on the emulator - every PS3 game at the very least boots and shows image output.\n\nHappy holidays! https://t.co/3ZAWFzbyGP", "media": [{"type": "photo", "url": "https://t.co/3ZAWFzbyGP", "thumbnail": "https://pbs.twimg.com/media/Fk2lv54XwAAr09b?format=png&name=thumb", "original": "https://pbs.twimg.com/media/Fk2lv54XwAAr09b?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 16261, "retweet_count": 2368, "bookmark_count": 542, "quote_count": 244, "reply_count": 187, "views_count": 1542809, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1607123433506177025", "metadata": {"__typename": "Tweet", "rest_id": "1607123433506177025", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NDg0Mzc2NDMyMDk3MzYxOTM=", "rest_id": "848437643209736193", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1515368573539172354/sdW6TE01_normal.jpg"}, "core": {"created_at": "Sun Apr 02 07:31:12 +0000 2017", "name": "RPCS3", "screen_name": "rpcs3"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "RPCS3 is an open-source PlayStation 3 emulator and debugger for Windows, Linux, macOS and FreeBSD", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "rpcs3.net", "expanded_url": "http://rpcs3.net", "url": "https://t.co/GKslvRvSGm", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 315, "followers_count": 48191, "friends_count": 10, "has_custom_timelines": false, "is_translator": false, "listed_count": 164, "media_count": 434, "normal_followers_count": 48191, "pinned_tweet_ids_str": ["1778089019236114868"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/848437643209736193/1650126997", "profile_interstitial_type": "", "statuses_count": 1197, "translator_type": "none", "url": "https://t.co/GKslvRvSGm", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1607123433506177025"], "editable_until_msecs": "1672004860000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1542809", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 542, "bookmarked": true, "created_at": "Sun Dec 25 21:17:40 +0000 2022", "conversation_id_str": "1607123433506177025", "display_text_range": [0, 275], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/3ZAWFzbyGP", "expanded_url": "https://x.com/rpcs3/status/1607123433506177025/photo/1", "id_str": "1607122884450828288", "indices": [276, 299], "media_key": "3_1607122884450828288", "media_url_https": "https://pbs.twimg.com/media/Fk2lv54XwAAr09b.png", "type": "photo", "url": "https://t.co/3ZAWFzbyGP", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 177, "w": 733, "resize": "fit"}, "medium": {"h": 177, "w": 733, "resize": "fit"}, "small": {"h": 164, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 177, "width": 733, "focus_rects": [{"x": 0, "y": 0, "w": 316, "h": 177}, {"x": 3, "y": 0, "w": 177, "h": 177}, {"x": 14, "y": 0, "w": 155, "h": 177}, {"x": 47, "y": 0, "w": 89, "h": 177}, {"x": 0, "y": 0, "w": 733, "h": 177}]}, "media_results": {"result": {"media_key": "3_1607122884450828288"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/3ZAWFzbyGP", "expanded_url": "https://x.com/rpcs3/status/1607123433506177025/photo/1", "id_str": "1607122884450828288", "indices": [276, 299], "media_key": "3_1607122884450828288", "media_url_https": "https://pbs.twimg.com/media/Fk2lv54XwAAr09b.png", "type": "photo", "url": "https://t.co/3ZAWFzbyGP", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 177, "w": 733, "resize": "fit"}, "medium": {"h": 177, "w": 733, "resize": "fit"}, "small": {"h": 164, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 177, "width": 733, "focus_rects": [{"x": 0, "y": 0, "w": 316, "h": 177}, {"x": 3, "y": 0, "w": 177, "h": 177}, {"x": 14, "y": 0, "w": 155, "h": 177}, {"x": 47, "y": 0, "w": 89, "h": 177}, {"x": 0, "y": 0, "w": 733, "h": 177}]}, "media_results": {"result": {"media_key": "3_1607122884450828288"}}}]}, "favorite_count": 16261, "favorited": false, "full_text": "We are delighted to announce that as of today, the RPCS3 Loadable compatibility category has reached 0 GAMES!\n\nThis means there are no PS3 games left that boot to a black screen on the emulator - every PS3 game at the very least boots and shows image output.\n\nHappy holidays! https://t.co/3ZAWFzbyGP", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 244, "reply_count": 187, "retweet_count": 2368, "retweeted": false, "user_id_str": "848437643209736193", "id_str": "1607123433506177025"}, "twe_private_fields": {"created_at": 1672003060000, "updated_at": 1748554206264, "media_count": 1}}}]