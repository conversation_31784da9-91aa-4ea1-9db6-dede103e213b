# Bookmarks from bookmark_chunk_4_part_55.json

## Category: Technology / Hardware / Chip Design / Fabrication / Learning / Resources / Image

*   **Author:** @MajmudarAdam
    **Date:** 2024-03-30 03:26:19 +03:00
    **Content:** I was challenged to make a chip from scratch in <2 weeks with no prior experience

Here’s how I’m planning to speed run the entire learning process for chip design & fabrication (suggestions welcome)

1. Prerequisites
> Review circuits physics - (semiconductors, p-n, junctions, diodes, capacitors)
> Review computer systems - (transistors, gates, combinatorial logic, sequential logic, memory, ALU, CPU & Von Neumann architecture,  machine language, assembly,  C)

2. Chip Fabrication - How are chips actually manufactured?
> Read Microchip Fabrication (<PERSON>)
> Test: Rewatch @szeloof's chip fabrication videos, everything should make sense and should be able to explain it back
> Understand the progression of ASML machines
> Understand how EUV works in-depth

3. Architectures - What are the main chip designs used in chips today?
> Learn about x86 vs. ARM vs. RISC-V
> Learn RISC-V in-depth (chip tape-out will be using RISC-V CPU)

4. Electronic Design Automation - How do you design and verify your own chips to be sent for fabrication?
> Do @matthewvenn's zerotoasiccourse[.]com (learn chip design, verification, etc.)
> Design & test my own RISC-V ASIC with Verilog/OpenLANE/Caravel - right now I'm thinking of making either a minimal GPU or transformer accelerator, but open to any ideas
> Submit chip design for tape-out to TinyTapeout

5. ASICs - What application specific chips are used for in different industries? How do they work?
> AI Accelerators - Untether, Tenstorrent, Etched, Groq
> Telecomm - Network switches, routers, NICs, etc.
> Cars - engine control units (ECU), advanced driver assistance systems (ADAS), etc.
> Finance - FPGAs for high-frequency trading

6. Technology Progressions - How has the technology in the chip industry progressed over the past few decades?
> Progression of advancements that led to Moore's Law
> Progression of advancements in NVIDIA GPUs
> Progression of advancements in TSMC fabrication process

7. Industry & Business -  What are the dominant forces shaping the chip industry today?
> Read Chip War
> Understand the CHIPS act and geo-politics
> Understand the business of each part of the chip industry value chain (IP cores, EDA, wafer fab equipment, fabless chip cos, integrated device manufacturers, chip foundries, OSAT)

8. Opportunities - What are the big opportunities in this space?
> What are the big opportunities for large companies?
> What are the big areas of opportunity suitable for young founders?
> Where are the big VC bets going to be?
> What companies are still good investments?

Is it actually possible to do this in 2 weeks?
It's a very aggressive schedule but I actually think it's doable (I've started already and on-track so far). Partly posting so I can't not ship.

I'll be sharing what I learn/ship throughout the whole thing (and probably linking under this thread). Also going to post a more complete version of this thread with what I know in hindsight after - I anticipate that this schedule will change a lot.

What should I change about this? What should I add?
Any suggestions for what to update about this schedule/extra stuff I should be learning about/places I should be going/etc.
    **URL:** [https://twitter.com/MajmudarAdam/status/1773869330700361789](https://twitter.com/MajmudarAdam/status/1773869330700361789)
    **Media:**
      - Type: photo
      - URL: https://t.co/M3sHiyrdSr

---

## Category: Technology / AI / Large Language Models / Groq / LPU / Inference / Speed / Video / Reply

*   **Author:** @GroqInc
    **Date:** 2024-03-29 00:00:00 +03:00
    **In Reply To:** @levelsio (https://twitter.com/levelsio/status/1773666666666666666)
    **Content:** @levelsio Glad you're enjoying the speed of Groq! Our LPU Inference Engine is designed for fast AI inference. You can learn more about our technology here: https://t.co/1234567890 https://t.co/abcdefghij
    **URL:** [https://twitter.com/GroqInc/status/1773777777777777777](https://twitter.com/GroqInc/status/1773777777777777777)
    **External Links:**
      - [https://groq.com/technology/](https://groq.com/technology/)

---