[{"id": "1927751369202552924", "created_at": "2025-05-28 18:38:36 +03:00", "full_text": "just dropped a new video on YouTube!\n\nbuilding your own ping command in C\nyeah — raw sockets and ICMP packets\n\nit's easier than you think (and way cooler)\n\nhttps://t.co/LmSRCQ9AhA", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 298, "retweet_count": 28, "bookmark_count": 189, "quote_count": 1, "reply_count": 5, "views_count": 15459, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1927751369202552924", "metadata": {"__typename": "Tweet", "rest_id": "1927751369202552924", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTM5MjE5MTI5MzU3NzMzODg5", "rest_id": "1539219129357733889", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1916985057786662912/vP0ZMyWd_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 21 12:10:19 +0000 2022", "name": "trish", "screen_name": "_trish_xD"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Backend Developer | Low-Level Programmer | Building systems, optimizing code 🦀", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com/dexter-xD", "expanded_url": "https://github.com/dexter-xD", "url": "https://t.co/TsjLTX2Ut5", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10125, "followers_count": 22120, "friends_count": 655, "has_custom_timelines": false, "is_translator": false, "listed_count": 86, "media_count": 452, "normal_followers_count": 22120, "pinned_tweet_ids_str": ["1921793710096011751"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1539219129357733889/1730656716", "profile_interstitial_type": "", "statuses_count": 10106, "translator_type": "none", "url": "https://t.co/TsjLTX2Ut5", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "127.0.0.1"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/LmSRCQ9AhA", "legacy": {"binding_values": [{"key": "player_url", "value": {"string_value": "https://www.youtube.com/embed/qlBqkTEdN-E", "type": "STRING"}}, {"key": "player_image_large", "value": {"image_value": {"height": 320, "width": 569, "url": "https://pbs.twimg.com/card_img/1927751267126034432/TfBFWTwc?format=jpg&name=800x320_1"}, "type": "IMAGE"}}, {"key": "player_image", "value": {"image_value": {"height": 158, "width": 280, "url": "https://pbs.twimg.com/card_img/1927751267126034432/TfBFWTwc?format=jpg&name=280x280"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.67903", "type": "STRING"}}, {"key": "description", "value": {"string_value": "In this video, we delve into the inner workings of the ping command by building it from scratch in C using raw sockets and ICMP packets.You'll learn:- What I...", "type": "STRING"}}, {"key": "player_width", "value": {"string_value": "1280", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "www.youtube.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "10228272", "path": []}}}, {"key": "player_image_original", "value": {"image_value": {"height": 720, "width": 1280, "url": "https://pbs.twimg.com/card_img/1927751267126034432/TfBFWTwc?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "app_num_ratings", "value": {"string_value": "42,088,439", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "player_height", "value": {"string_value": "720", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "youtube.com", "type": "STRING"}}, {"key": "app_name", "value": {"string_value": "YouTube", "type": "STRING"}}, {"key": "player_image_small", "value": {"image_value": {"height": 81, "width": 144, "url": "https://pbs.twimg.com/card_img/1927751267126034432/TfBFWTwc?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "title", "value": {"string_value": "Build Your Own Ping in C | Raw Sockets + ICMP Explained!", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/LmSRCQ9AhA", "type": "STRING"}}, {"key": "player_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 129, "green": 168, "red": 213}, "percentage": 88.17}, {"rgb": {"blue": 34, "green": 62, "red": 97}, "percentage": 10.07}, {"rgb": {"blue": 75, "green": 97, "red": 122}, "percentage": 1.25}, {"rgb": {"blue": 34, "green": 63, "red": 120}, "percentage": 0.26}, {"rgb": {"blue": 34, "green": 44, "red": 54}, "percentage": 0.25}]}, "type": "IMAGE_COLOR"}}, {"key": "player_image_x_large", "value": {"image_value": {"height": 720, "width": 1280, "url": "https://pbs.twimg.com/card_img/1927751267126034432/TfBFWTwc?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "player", "url": "https://t.co/LmSRCQ9AhA", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMDIyODI3Mg==", "rest_id": "10228272", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915882040353837056/VbhPvueq_normal.jpg"}, "core": {"created_at": "Tue Nov 13 21:43:46 +0000 2007", "name": "YouTube", "screen_name": "YouTube"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "like & subscribe", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yt.be/3NDRz", "expanded_url": "https://yt.be/3NDRz", "url": "https://t.co/WBV5E1Rh1y", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6069, "followers_count": 79010249, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 77696, "media_count": 16005, "normal_followers_count": 79010249, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/10228272/1745416765", "profile_interstitial_type": "", "statuses_count": 60001, "translator_type": "regular", "url": "https://t.co/WBV5E1Rh1y", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Bruno, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1927751369202552924"], "editable_until_msecs": "1748450316000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "15459", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "community_results": {"result": {"__typename": "Community", "id_str": "*******************", "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}}}, "author_community_relationship": {"community_results": {"result": {"__typename": "Community", "id_str": "*******************", "name": "C and Assembly Developers", "description": "This community is geared towards accelerated knowledge of low-level computing concepts, and active contribution to projects in C and ASM.", "created_at": *************, "question": "THIS IS MANDITORY:\n\nYour GitHub account or a snippet of code you've written in C or Assembly that you're proud of.", "search_tags": ["programming", "c", "asm", "softwaredevelopment", "cprogramming", "lowlevel", "code", "math", "ai", "ml"], "is_nsfw": false, "primary_community_topic": {"topic_id": "303", "topic_name": "Software"}, "actions": {"delete_action_result": {"__typename": "CommunityDeleteActionUnavailable", "reason": "Unavailable"}, "join_action_result": {"__typename": "CommunityJoinActionUnavailable", "reason": "ViewerIsMember", "message": "You are already a member."}, "leave_action_result": {"__typename": "CommunityLeaveAction"}, "pin_action_result": {"__typename": "CommunityTweetPinActionUnavailable"}}, "admin_results": {"result": {"__typename": "User", "id": "********************************", "rest_id": "15876010***********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102822, "followers_count": 84338, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84338, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/15876010***********/1748321162", "profile_interstitial_type": "", "statuses_count": 26522, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "creator_results": {"result": {"__typename": "User", "id": "********************************", "rest_id": "15876010***********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102822, "followers_count": 84338, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84338, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/15876010***********/1748321162", "profile_interstitial_type": "", "statuses_count": 26522, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "invites_result": {"__typename": "CommunityInvites", "remaining_invite_count": 10, "users_to_invite_slice": {"items": [], "slice_info": {}}}, "join_policy": "Open", "invites_policy": "MemberInvitesAllowed", "is_pinned": true, "members_facepile_results": [{"result": {"__typename": "User", "id": "********************************", "rest_id": "15876010***********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102822, "followers_count": 84338, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84338, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/15876010***********/1748321162", "profile_interstitial_type": "", "statuses_count": 26522, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDk1ODMzNzcxNTI4NDg2OTEy", "rest_id": "1095833771528486912", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1852982863295361024/nt6Wkcdk_normal.jpg"}, "core": {"created_at": "Wed Feb 13 23:55:05 +0000 2019", "name": "<PERSON><PERSON><PERSON>", "screen_name": "DisperseControl"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I am potato. e/acc. Bulltard.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "brrr.money", "expanded_url": "https://brrr.money/", "url": "https://t.co/LnifTg9G7X", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 638141, "followers_count": 4496, "friends_count": 7347, "has_custom_timelines": true, "is_translator": false, "listed_count": 23, "media_count": 26419, "normal_followers_count": 4496, "pinned_tweet_ids_str": ["1917289486897029355"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1095833771528486912/1652435782", "profile_interstitial_type": "", "statuses_count": 55745, "translator_type": "none", "url": "https://t.co/LnifTg9G7X", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Canazuela"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoyMTkyMDM0MTI=", "rest_id": "219203412", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1920040820654432256/68AZgHjH_normal.jpg"}, "core": {"created_at": "Wed Nov 24 06:15:09 +0000 2010", "name": "Erroneous Input", "screen_name": "<PERSON>_<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "what?", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 62246, "followers_count": 1341, "friends_count": 202, "has_custom_timelines": false, "is_translator": false, "listed_count": 63, "media_count": 18475, "normal_followers_count": 1341, "pinned_tweet_ids_str": ["1777265833670262905"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/219203412/1721665890", "profile_interstitial_type": "", "statuses_count": 79937, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDQxNjUxNjg5MDQyNzg4MzUy", "rest_id": "1041651689042788352", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1846890999634681856/iixBL8be_normal.jpg"}, "core": {"created_at": "Mon Sep 17 11:34:50 +0000 2018", "name": "faulty *ptrrr", "screen_name": "0x_shaq"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Chief pager engineer 📟", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pwner.gg/blog/", "expanded_url": "https://pwner.gg/blog/", "url": "https://t.co/VVHKg0umzt", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10259, "followers_count": 4692, "friends_count": 364, "has_custom_timelines": true, "is_translator": false, "listed_count": 24, "media_count": 604, "normal_followers_count": 4692, "pinned_tweet_ids_str": ["1794342802173890682"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1041651689042788352/1659977667", "profile_interstitial_type": "", "statuses_count": 2346, "translator_type": "none", "url": "https://t.co/VVHKg0umzt", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🇮🇱Israel"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxNTAxMTU2Mzk2NTA2OTM5Mzk1", "rest_id": "1501156396506939395", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1810298224382763008/VVGhTJi4_normal.jpg"}, "core": {"created_at": "<PERSON>e Mar 08 11:23:25 +0000 2022", "name": "<PERSON>raw 🍓", "screen_name": "ddnnddc"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Technologist, major node in the cat distribution system.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/playlist?list=…", "expanded_url": "https://www.youtube.com/playlist?list=PLdVyg0SqO9v_FMdp7oPhmlMSP0kRpziVg", "url": "https://t.co/Itnc8qT9Ow", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 49643, "followers_count": 434, "friends_count": 951, "has_custom_timelines": false, "is_translator": false, "listed_count": 2, "media_count": 960, "normal_followers_count": 434, "pinned_tweet_ids_str": ["1832506180339716148"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1501156396506939395/1720444898", "profile_interstitial_type": "", "statuses_count": 8050, "translator_type": "none", "url": "https://t.co/Itnc8qT9Ow", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Melbourne, Australia"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1693828904694563112", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}], "moderator_count": 9, "member_count": 49694, "role": "Member", "rules": [{"rest_id": "1840492394057511216", "name": "Keep posts on topic.", "description": "Focus discussions on C, Assembly programming, and related topics. Avoid posting unrelated content to maintain quality. This implies no politics.."}, {"rest_id": "1783990929403363417", "name": "Explore and share.", "description": "Encourage curiosity and learning. Share your experiences, code snippets, and resources. Help others by contributing to the collective knowledge."}, {"rest_id": "1783990992099848586", "name": "Provide constructive feedback.", "description": "When critiquing code or concepts, offer clear, constructive, and polite feedback. Aim to help others improve and learn."}, {"rest_id": "1785457160471908430", "name": "Privacy and security.", "description": "Respect the privacy and the integrity of the community."}, {"rest_id": "1795107937150730598", "name": "Low effort bait questions lead to permanent removal.", "description": "Posts that use shallow questions to boost engagement risk permanent removal to uphold discussion quality."}, {"rest_id": "1840493930280014028", "name": "Suggestion-Only Policy: DM @7etsuo for a groupchat invite.", "description": "By joining the community group chat, you agree to keep it a space for suggestions, C-related questions, sharing work, and community suggestions."}], "custom_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 21, "green": 194, "blue": 225}, "percentage": 25.43}, {"rgb": {"red": 58, "green": 154, "blue": 255}, "percentage": 11.73}, {"rgb": {"red": 243, "green": 244, "blue": 250}, "percentage": 10.8}, {"rgb": {"red": 20, "green": 190, "blue": 12}, "percentage": 8.56}, {"rgb": {"red": 226, "green": 97, "blue": 162}, "percentage": 8.44}]}, "original_img_url": "https://pbs.twimg.com/community_banner_img/1827118393209618432/I8no5S8w?format=jpg&name=orig", "original_img_width": 1200, "original_img_height": 480, "salient_rect": {"left": 601, "top": 240, "width": 1, "height": 1}}}, "default_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 1, "green": 161, "blue": 155}, "percentage": 79.35}, {"rgb": {"red": 248, "green": 120, "blue": 132}, "percentage": 11.83}, {"rgb": {"red": 212, "green": 133, "blue": 146}, "percentage": 2.97}, {"rgb": {"red": 129, "green": 175, "blue": 168}, "percentage": 1.95}, {"rgb": {"red": 244, "green": 84, "blue": 97}, "percentage": 0.81}]}, "original_img_url": "https://pbs.twimg.com/media/FECQY8MVEAEnZBg.jpg", "original_img_width": 1200, "original_img_height": 480}}, "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}, "join_requests_result": {"__typename": "CommunityJoinRequestsUnavailable"}}}, "role": "Member", "user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTM5MjE5MTI5MzU3NzMzODg5", "rest_id": "1539219129357733889", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1916985057786662912/vP0ZMyWd_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 21 12:10:19 +0000 2022", "name": "trish", "screen_name": "_trish_xD"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Backend Developer | Low-Level Programmer | Building systems, optimizing code 🦀", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com/dexter-xD", "expanded_url": "https://github.com/dexter-xD", "url": "https://t.co/TsjLTX2Ut5", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10125, "followers_count": 22120, "friends_count": 655, "has_custom_timelines": false, "is_translator": false, "listed_count": 86, "media_count": 452, "normal_followers_count": 22120, "pinned_tweet_ids_str": ["1921793710096011751"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1539219129357733889/1730656716", "profile_interstitial_type": "", "statuses_count": 10106, "translator_type": "none", "url": "https://t.co/TsjLTX2Ut5", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "127.0.0.1"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "legacy": {"bookmark_count": 189, "bookmarked": true, "created_at": "Wed May 28 15:38:36 +0000 2025", "conversation_id_str": "1927751369202552924", "display_text_range": [0, 179], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/qlBqkTEdN-E?si…", "expanded_url": "https://youtu.be/qlBqkTEdN-E?si=tz0wraqFOBVfPM5f", "url": "https://t.co/LmSRCQ9AhA", "indices": [156, 179]}], "user_mentions": []}, "favorite_count": 298, "favorited": false, "full_text": "just dropped a new video on YouTube!\n\nbuilding your own ping command in C\nyeah — raw sockets and ICMP packets\n\nit's easier than you think (and way cooler)\n\nhttps://t.co/LmSRCQ9AhA", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 5, "retweet_count": 28, "retweeted": false, "user_id_str": "1539219129357733889", "id_str": "1927751369202552924"}, "twe_private_fields": {"created_at": 1748446716000, "updated_at": 1748553996861, "media_count": 0}}}, {"id": "1927850858802303482", "created_at": "2025-05-29 01:13:57 +03:00", "full_text": "@attentionmech these may be of interest as a high-altitude bird's-eye view of most of known mathematics\n\nhttps://t.co/akxBfnytal\n\nhttps://t.co/yiNNAGl7Tm\n\ni love explaining things in ways that click for others, as it helps me deepen my own understanding. my DMs are open!", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1927812620205052128", "retweeted_status": null, "quoted_status": null, "favorite_count": 933, "retweet_count": 67, "bookmark_count": 2021, "quote_count": 5, "reply_count": 12, "views_count": 24827, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1927850858802303482", "metadata": {"__typename": "Tweet", "rest_id": "1927850858802303482", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTM1MDA2Nzk2ODE3MjUyMzUy", "rest_id": "1535006796817252352", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1926373953129291777/FIJsy4F4_normal.jpg"}, "core": {"created_at": "Thu Jun 09 21:12:21 +0000 2022", "name": "<PERSON>", "screen_name": "solo_lennying"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Applied Math PhD candidate at Brown. Interests: SciML, PDEs, optimization, physics, neuroscience, philosophy, spirituality, nature, music, and everything else", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 1298, "followers_count": 264, "friends_count": 339, "has_custom_timelines": true, "is_translator": false, "listed_count": 2, "media_count": 5, "normal_followers_count": 264, "pinned_tweet_ids_str": ["1928197045598179622"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1535006796817252352/1748120088", "profile_interstitial_type": "", "statuses_count": 132, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1927850858802303482"], "editable_until_msecs": "1748474037000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "24827", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2021, "bookmarked": true, "created_at": "Wed May 28 22:13:57 +0000 2025", "conversation_id_str": "1927812620205052128", "display_text_range": [15, 271], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "sites.math.rutgers.edu/~zeilberg/akhe…", "expanded_url": "https://sites.math.rutgers.edu/~zeilberg/akherim/PCM.pdf", "url": "https://t.co/akxBfnytal", "indices": [105, 128]}, {"display_url": "horizons-2000.org/91.Links/Readi…", "expanded_url": "https://horizons-2000.org/91.Links/Reading%20etc/The%20Princeton%20Companion%20to%20App%20-%20Higham,%20Nicholas%20J._6324.pdf", "url": "https://t.co/yiNNAGl7Tm", "indices": [130, 153]}], "user_mentions": [{"id_str": "1863435997368786944", "name": "attentionmech", "screen_name": "attentionmech", "indices": [0, 14]}]}, "favorite_count": 933, "favorited": false, "full_text": "@attentionmech these may be of interest as a high-altitude bird's-eye view of most of known mathematics\n\nhttps://t.co/akxBfnytal\n\nhttps://t.co/yiNNAGl7Tm\n\ni love explaining things in ways that click for others, as it helps me deepen my own understanding. my DMs are open!", "in_reply_to_screen_name": "attentionmech", "in_reply_to_status_id_str": "1927812620205052128", "in_reply_to_user_id_str": "1863435997368786944", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 5, "reply_count": 12, "retweet_count": 67, "retweeted": false, "user_id_str": "1535006796817252352", "id_str": "1927850858802303482"}, "twe_private_fields": {"created_at": 1748470437000, "updated_at": 1748553996861, "media_count": 0}}}]