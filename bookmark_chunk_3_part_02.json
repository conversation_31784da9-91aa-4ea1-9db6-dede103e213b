[{"id": "1529079807711096832", "created_at": "2022-05-24 15:40:09 +03:00", "full_text": "17 Top Best Chrome Extensions For Web Developers.\n\nThread 🧵", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 750, "retweet_count": 226, "bookmark_count": 471, "quote_count": 4, "reply_count": 29, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1529079807711096832", "metadata": {"__typename": "Tweet", "rest_id": "1529079807711096832", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzQ1NDIxMTA1ODYzODgwNzA1", "rest_id": "1345421105863880705", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1874092567370887168/dwLUWCOb_normal.jpg"}, "core": {"created_at": "Sat Jan 02 17:26:37 +0000 2021", "name": "<PERSON>", "screen_name": "m<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "marketing manager who codes • building with AI • sharing marketing insights for devs & tech tips for marketers.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 16132, "followers_count": 73542, "friends_count": 1004, "has_custom_timelines": true, "is_translator": false, "listed_count": 1812, "media_count": 1558, "normal_followers_count": 73542, "pinned_tweet_ids_str": ["1927302962038149448"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1345421105863880705/1748338261", "profile_interstitial_type": "", "statuses_count": 20894, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1529079807711096832"], "editable_until_msecs": "1653397809000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 471, "bookmarked": true, "created_at": "<PERSON><PERSON> May 24 12:40:09 +0000 2022", "conversation_id_str": "1529079807711096832", "display_text_range": [0, 59], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 750, "favorited": false, "full_text": "17 Top Best Chrome Extensions For Web Developers.\n\nThread 🧵", "is_quote_status": false, "lang": "en", "quote_count": 4, "reply_count": 29, "retweet_count": 226, "retweeted": false, "user_id_str": "1345421105863880705", "id_str": "1529079807711096832"}, "twe_private_fields": {"created_at": 1653396009000, "updated_at": 1748554271915, "media_count": 0}}}, {"id": "1529083214459023361", "created_at": "2022-05-24 15:53:41 +03:00", "full_text": "My books are free to read online:\n\n“JavaScript for impatient programmers”: Learn ECMAScript 2022 via book, exercises, quizzes\nhttps://t.co/uXKdpnLngt\n\n“Deep JavaScript”: Go deeper\nhttps://t.co/tma31MtwA0\n\n“Tackling TypeScript”: From JS to TS\nhttps://t.co/yNjJugpoum https://t.co/1rZF0LDLuL", "media": [{"type": "photo", "url": "https://t.co/1rZF0LDLuL", "thumbnail": "https://pbs.twimg.com/media/FThkxwRXwAUfudn?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FThkxwRXwAUfudn?format=jpg&name=orig", "ext_alt_text": "Cover of the book “JavaScript for impatient programmers (ES2022 edition)” by <PERSON>. It shows a rhinoceros."}, {"type": "photo", "url": "https://t.co/1rZF0LDLuL", "thumbnail": "https://pbs.twimg.com/media/FThkyvBWQAAHruK?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FThkyvBWQAAHruK?format=jpg&name=orig", "ext_alt_text": "Cover of the book “Deep JavaScript” by <PERSON>. It shows a freediver who swims under water and follows a rope down into the deep."}, {"type": "photo", "url": "https://t.co/1rZF0LDLuL", "thumbnail": "https://pbs.twimg.com/media/FThkzZvWIAccYCn?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FThkzZvWIAccYCn?format=jpg&name=orig", "ext_alt_text": "Cover of the book “Tackling TypeScript” by <PERSON>. It shows a a pair of boxing gloves."}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 699, "retweet_count": 192, "bookmark_count": 316, "quote_count": 9, "reply_count": 14, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1529083214459023361", "metadata": {"__typename": "Tweet", "rest_id": "1529083214459023361", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1Njk5MjgxMQ==", "rest_id": "********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/467581434434453504/iFzELOBn_normal.jpeg"}, "core": {"created_at": "Wed Jul 15 11:36:31 +0000 2009", "name": "<PERSON> (INACTIVE)", "screen_name": "r<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "INACTIVE ACCOUNT. Mastodon: https://t.co/jLenO6e5ZL • Other ways of connecting with me: https://t.co/fvpPa89Isz…", "entities": {"description": {"urls": [{"display_url": "fosstodon.org/@rauschma", "expanded_url": "https://fosstodon.org/@rauschma", "url": "https://t.co/jLenO6e5ZL", "indices": [28, 51]}, {"display_url": "dr-axel.de/#web-developme", "expanded_url": "http://dr-axel.de/#web-developme", "url": "https://t.co/fvpPa89Isz", "indices": [88, 111]}]}, "url": {"urls": [{"display_url": "dr-axel.de", "expanded_url": "http://dr-axel.de/", "url": "https://t.co/9nCj4yXeSz", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 24552, "followers_count": 61365, "friends_count": 379, "has_custom_timelines": true, "is_translator": false, "listed_count": 1899, "media_count": 1029, "normal_followers_count": 61365, "pinned_tweet_ids_str": ["1808844008824742066"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/********/**********", "profile_interstitial_type": "", "statuses_count": 43872, "translator_type": "none", "url": "https://t.co/9nCj4yXeSz", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Munich, Germany"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1529083214459023361"], "editable_until_msecs": "1653398621000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 316, "bookmarked": true, "created_at": "<PERSON><PERSON> May 24 12:53:41 +0000 2022", "conversation_id_str": "1529083214459023361", "display_text_range": [0, 265], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/1rZF0LDLuL", "expanded_url": "https://x.com/rauschma/status/1529083214459023361/photo/1", "ext_alt_text": "Cover of the book “JavaScript for impatient programmers (ES2022 edition)” by <PERSON>. It shows a rhinoceros.", "id_str": "1529082879426412549", "indices": [266, 289], "media_key": "3_1529082879426412549", "media_url_https": "https://pbs.twimg.com/media/FThkxwRXwAUfudn.jpg", "type": "photo", "url": "https://t.co/1rZF0LDLuL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1499, "w": 1049, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1499, "width": 1049, "focus_rects": [{"x": 0, "y": 0, "w": 1049, "h": 587}, {"x": 0, "y": 0, "w": 1049, "h": 1049}, {"x": 0, "y": 0, "w": 1049, "h": 1196}, {"x": 149, "y": 0, "w": 750, "h": 1499}, {"x": 0, "y": 0, "w": 1049, "h": 1499}]}, "media_results": {"result": {"media_key": "3_1529082879426412549"}}}, {"display_url": "pic.x.com/1rZF0LDLuL", "expanded_url": "https://x.com/rauschma/status/1529083214459023361/photo/1", "ext_alt_text": "Cover of the book “Deep JavaScript” by <PERSON>. It shows a freediver who swims under water and follows a rope down into the deep.", "id_str": "1529082896270639104", "indices": [266, 289], "media_key": "3_1529082896270639104", "media_url_https": "https://pbs.twimg.com/media/FThkyvBWQAAHruK.jpg", "type": "photo", "url": "https://t.co/1rZF0LDLuL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1499, "w": 1049, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1499, "width": 1049, "focus_rects": [{"x": 0, "y": 868, "w": 1049, "h": 587}, {"x": 0, "y": 450, "w": 1049, "h": 1049}, {"x": 0, "y": 303, "w": 1049, "h": 1196}, {"x": 149, "y": 0, "w": 750, "h": 1499}, {"x": 0, "y": 0, "w": 1049, "h": 1499}]}, "media_results": {"result": {"media_key": "3_1529082896270639104"}}}, {"display_url": "pic.x.com/1rZF0LDLuL", "expanded_url": "https://x.com/rauschma/status/1529083214459023361/photo/1", "ext_alt_text": "Cover of the book “Tackling TypeScript” by <PERSON>. It shows a a pair of boxing gloves.", "id_str": "1529082907737858055", "indices": [266, 289], "media_key": "3_1529082907737858055", "media_url_https": "https://pbs.twimg.com/media/FThkzZvWIAccYCn.jpg", "type": "photo", "url": "https://t.co/1rZF0LDLuL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1499, "w": 1049, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1499, "width": 1049, "focus_rects": [{"x": 0, "y": 912, "w": 1049, "h": 587}, {"x": 0, "y": 450, "w": 1049, "h": 1049}, {"x": 0, "y": 303, "w": 1049, "h": 1196}, {"x": 261, "y": 0, "w": 750, "h": 1499}, {"x": 0, "y": 0, "w": 1049, "h": 1499}]}, "media_results": {"result": {"media_key": "3_1529082907737858055"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "exploringjs.com/impatient-js/", "expanded_url": "https://exploringjs.com/impatient-js/", "url": "https://t.co/uXKdpnLngt", "indices": [126, 149]}, {"display_url": "exploringjs.com/deep-js/", "expanded_url": "https://exploringjs.com/deep-js/", "url": "https://t.co/tma31MtwA0", "indices": [180, 203]}, {"display_url": "exploringjs.com/tackling-ts/", "expanded_url": "https://exploringjs.com/tackling-ts/", "url": "https://t.co/yNjJugpoum", "indices": [242, 265]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/1rZF0LDLuL", "expanded_url": "https://x.com/rauschma/status/1529083214459023361/photo/1", "ext_alt_text": "Cover of the book “JavaScript for impatient programmers (ES2022 edition)” by <PERSON>. It shows a rhinoceros.", "id_str": "1529082879426412549", "indices": [266, 289], "media_key": "3_1529082879426412549", "media_url_https": "https://pbs.twimg.com/media/FThkxwRXwAUfudn.jpg", "type": "photo", "url": "https://t.co/1rZF0LDLuL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1499, "w": 1049, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1499, "width": 1049, "focus_rects": [{"x": 0, "y": 0, "w": 1049, "h": 587}, {"x": 0, "y": 0, "w": 1049, "h": 1049}, {"x": 0, "y": 0, "w": 1049, "h": 1196}, {"x": 149, "y": 0, "w": 750, "h": 1499}, {"x": 0, "y": 0, "w": 1049, "h": 1499}]}, "media_results": {"result": {"media_key": "3_1529082879426412549"}}}, {"display_url": "pic.x.com/1rZF0LDLuL", "expanded_url": "https://x.com/rauschma/status/1529083214459023361/photo/1", "ext_alt_text": "Cover of the book “Deep JavaScript” by <PERSON>. It shows a freediver who swims under water and follows a rope down into the deep.", "id_str": "1529082896270639104", "indices": [266, 289], "media_key": "3_1529082896270639104", "media_url_https": "https://pbs.twimg.com/media/FThkyvBWQAAHruK.jpg", "type": "photo", "url": "https://t.co/1rZF0LDLuL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1499, "w": 1049, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1499, "width": 1049, "focus_rects": [{"x": 0, "y": 868, "w": 1049, "h": 587}, {"x": 0, "y": 450, "w": 1049, "h": 1049}, {"x": 0, "y": 303, "w": 1049, "h": 1196}, {"x": 149, "y": 0, "w": 750, "h": 1499}, {"x": 0, "y": 0, "w": 1049, "h": 1499}]}, "media_results": {"result": {"media_key": "3_1529082896270639104"}}}, {"display_url": "pic.x.com/1rZF0LDLuL", "expanded_url": "https://x.com/rauschma/status/1529083214459023361/photo/1", "ext_alt_text": "Cover of the book “Tackling TypeScript” by <PERSON>. It shows a a pair of boxing gloves.", "id_str": "1529082907737858055", "indices": [266, 289], "media_key": "3_1529082907737858055", "media_url_https": "https://pbs.twimg.com/media/FThkzZvWIAccYCn.jpg", "type": "photo", "url": "https://t.co/1rZF0LDLuL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1499, "w": 1049, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1499, "width": 1049, "focus_rects": [{"x": 0, "y": 912, "w": 1049, "h": 587}, {"x": 0, "y": 450, "w": 1049, "h": 1049}, {"x": 0, "y": 303, "w": 1049, "h": 1196}, {"x": 261, "y": 0, "w": 750, "h": 1499}, {"x": 0, "y": 0, "w": 1049, "h": 1499}]}, "media_results": {"result": {"media_key": "3_1529082907737858055"}}}]}, "favorite_count": 699, "favorited": false, "full_text": "My books are free to read online:\n\n“JavaScript for impatient programmers”: Learn ECMAScript 2022 via book, exercises, quizzes\nhttps://t.co/uXKdpnLngt\n\n“Deep JavaScript”: Go deeper\nhttps://t.co/tma31MtwA0\n\n“Tackling TypeScript”: From JS to TS\nhttps://t.co/yNjJugpoum https://t.co/1rZF0LDLuL", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 9, "reply_count": 14, "retweet_count": 192, "retweeted": false, "user_id_str": "********", "id_str": "1529083214459023361"}, "twe_private_fields": {"created_at": 1653396821000, "updated_at": 1748554271915, "media_count": 3}}}]