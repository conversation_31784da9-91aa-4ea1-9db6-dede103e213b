---
Tweet URL: https://twitter.com/AIatMeta/status/1806361623831171318
Tweet ID: 1806361623831171318
Author: @AIatMeta
---

Today we’re announcing Meta LLM Compiler, a family of models built on Meta Code Llama with additional code optimization and compiler capabilities. These models can emulate the compiler, predict optimal passes for code size, and disassemble code. They can be fine-tuned for new optimizations and compiler tasks.

@HuggingFace repo ➡️ [https://go.fb.me/tdd3dw](https://t.co/9URAr9sn5E)
Research paper ➡️ [https://go.fb.me/85zwgy](https://t.co/nIYvWHqm1D)

LLM Compiler achieves state-of-the-art results on code size optimization and disassembly. This work shows that AI is learning to optimize code and can assist compiler experts in identifying opportunities to apply optimizations.

We’re releasing LLM Compiler 7B & 13B models under a permissive license for both research and commercial use in the hopes of making it easier for developers and researchers alike to leverage this in their work and carry forward new research in this space.

---
Media: [https://x.com/AIatMeta/status/1806361623831171318/photo/1](https://t.co/GFDZDbZ1VF)
Category: AI, Machine Learning, Compilers, Meta, Open Source