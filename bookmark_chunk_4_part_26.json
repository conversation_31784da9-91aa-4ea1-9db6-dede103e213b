[{"id": "1676266671563067392", "created_at": "2023-07-04 19:28:12 +03:00", "full_text": "The Basic Structure For All ChatGPT Prompts\n\nStop writing single sentence prompts, you're wasting your time \n\nEvery prompt needs these 4 main elements:\n(🔖Bookmark to save for future references)\n\n1. Instructions\nThis is the main instruction that you want the AI model to perform\n\n2. Context\nAdditional data that steers the model to respond the way that you want it to\n\n3. Input Data\nInformation that you want the AI model to do something with or perform some sort of action on\n\n4. Output Indicator\nKeywords used to guide the AI model into giving a specific output\n\n-----------------------------------------------------\nBelow I used this prompt template to create a content creation calendar for an online business\n\nOnce you get these 4 down, you can get more nuanced with substructures within each basic structure\n\nFor example, \"Declaring a Role\" from my super prompt tutorial, would fall under Context", "media": [{"type": "photo", "url": "https://t.co/ZpI5c1GbKV", "thumbnail": "https://pbs.twimg.com/media/F0NLBz0XwAIgu_a?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/F0NLBz0XwAIgu_a?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2538, "retweet_count": 424, "bookmark_count": 6190, "quote_count": 14, "reply_count": 68, "views_count": 828376, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1676266671563067392", "metadata": {"__typename": "Tweet", "rest_id": "1676266671563067392", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1NzU4NjI3MTU=", "rest_id": "575862715", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1673444707421286400/8azxEQS1_normal.jpg"}, "core": {"created_at": "Thu May 10 02:27:58 +0000 2012", "name": "The AIpreneurs | Official", "screen_name": "theaipreneursof"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Actionable Lead Generation  Insights To Book More Meetings and Grow Your Presence | DMs open📬", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "calendly.com/jose_aipreneur…", "expanded_url": "https://calendly.com/jose_aipreneurs/automationaudit", "url": "https://t.co/LovkkhLuaT", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7994, "followers_count": 10102, "friends_count": 570, "has_custom_timelines": false, "is_translator": false, "listed_count": 313, "media_count": 617, "normal_followers_count": 10102, "pinned_tweet_ids_str": ["1676266671563067392"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/575862715/1687737153", "profile_interstitial_type": "", "statuses_count": 5625, "translator_type": "none", "url": "https://t.co/LovkkhLuaT", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1673085887331160067", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1676266671563067392"], "editable_until_msecs": "1688491692000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "828376", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE2NzYyNjY2NzE0NjIzMDk4ODg=", "text": "The Basic Structure For All ChatGPT Prompts\n\nStop writing single sentence prompts, you're wasting your time \n\nEvery prompt needs these 4 main elements:\n(🔖Bookmark to save for future references)\n\n1. Instructions\nThis is the main instruction that you want the AI model to perform\n\n2. Context\nAdditional data that steers the model to respond the way that you want it to\n\n3. Input Data\nInformation that you want the AI model to do something with or perform some sort of action on\n\n4. Output Indicator\nKeywords used to guide the AI model into giving a specific output\n\n-----------------------------------------------------\nBelow I used this prompt template to create a content creation calendar for an online business\n\nOnce you get these 4 down, you can get more nuanced with substructures within each basic structure\n\nFor example, \"Declaring a Role\" from my super prompt tutorial, would fall under Context", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 0, "to_index": 43, "richtext_types": ["Bold", "Italic"]}, {"from_index": 123, "to_index": 128, "richtext_types": ["Bold"]}, {"from_index": 196, "to_index": 211, "richtext_types": ["Bold"]}, {"from_index": 280, "to_index": 290, "richtext_types": ["Bold"]}, {"from_index": 369, "to_index": 382, "richtext_types": ["Bold"]}, {"from_index": 478, "to_index": 497, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 6190, "bookmarked": true, "created_at": "<PERSON><PERSON> 04 16:28:12 +0000 2023", "conversation_id_str": "1676266671563067392", "display_text_range": [0, 277], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/ZpI5c1GbKV", "expanded_url": "https://x.com/theaipreneursof/status/1676266671563067392/photo/1", "id_str": "1676265980773777410", "indices": [278, 301], "media_key": "3_1676265980773777410", "media_url_https": "https://pbs.twimg.com/media/F0NLBz0XwAIgu_a.jpg", "type": "photo", "url": "https://t.co/ZpI5c1GbKV", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 900, "w": 1600, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 900, "width": 1600, "focus_rects": [{"x": 0, "y": 4, "w": 1600, "h": 896}, {"x": 630, "y": 0, "w": 900, "h": 900}, {"x": 686, "y": 0, "w": 789, "h": 900}, {"x": 855, "y": 0, "w": 450, "h": 900}, {"x": 0, "y": 0, "w": 1600, "h": 900}]}, "media_results": {"result": {"media_key": "3_1676265980773777410"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/ZpI5c1GbKV", "expanded_url": "https://x.com/theaipreneursof/status/1676266671563067392/photo/1", "id_str": "1676265980773777410", "indices": [278, 301], "media_key": "3_1676265980773777410", "media_url_https": "https://pbs.twimg.com/media/F0NLBz0XwAIgu_a.jpg", "type": "photo", "url": "https://t.co/ZpI5c1GbKV", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 900, "w": 1600, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 900, "width": 1600, "focus_rects": [{"x": 0, "y": 4, "w": 1600, "h": 896}, {"x": 630, "y": 0, "w": 900, "h": 900}, {"x": 686, "y": 0, "w": 789, "h": 900}, {"x": 855, "y": 0, "w": 450, "h": 900}, {"x": 0, "y": 0, "w": 1600, "h": 900}]}, "media_results": {"result": {"media_key": "3_1676265980773777410"}}}]}, "favorite_count": 2538, "favorited": false, "full_text": "The Basic Structure For All ChatGPT Prompts\n\nStop writing single sentence prompts, you're wasting your time \n\nEvery prompt needs these 4 main elements:\n(🔖Bookmark to save for future references)\n\n1. Instructions\nThis is the main instruction that you want the AI model to perform https://t.co/ZpI5c1GbKV", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 14, "reply_count": 68, "retweet_count": 424, "retweeted": false, "user_id_str": "575862715", "id_str": "1676266671563067392"}, "twe_private_fields": {"created_at": 1688488092000, "updated_at": 1748554188924, "media_count": 1}}}, {"id": "1677537058594881541", "created_at": "2023-07-08 07:36:16 +03:00", "full_text": "@9ouya https://t.co/YlKCs6L7vA", "media": [{"type": "photo", "url": "https://t.co/YlKCs6L7vA", "thumbnail": "https://pbs.twimg.com/media/F0fPD2UaYAAbQEP?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/F0fPD2UaYAAbQEP?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1677504683601010690", "retweeted_status": null, "quoted_status": null, "favorite_count": 4, "retweet_count": 0, "bookmark_count": 1, "quote_count": 0, "reply_count": 1, "views_count": 372, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1677537058594881541", "metadata": {"__typename": "Tweet", "rest_id": "1677537058594881541", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MjQyMzcwMzIxNjc3MTg5MTI=", "rest_id": "824237032167718912", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1734774572711559168/IN8W4PAD_normal.jpg"}, "core": {"created_at": "Wed Jan 25 12:46:36 +0000 2017", "name": "<PERSON><PERSON> 'Master' M", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "20 | 🇮🇳 | Esports Athlete | COD Mobile for @RevenantGGWP", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/@masterios", "expanded_url": "https://youtube.com/@masterios", "url": "https://t.co/jWYhS5EOlh", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 4071, "followers_count": 455, "friends_count": 253, "has_custom_timelines": true, "is_translator": false, "listed_count": 5, "media_count": 135, "normal_followers_count": 455, "pinned_tweet_ids_str": ["1852336262037770463"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/824237032167718912/1719808612", "profile_interstitial_type": "", "statuses_count": 686, "translator_type": "none", "url": "https://t.co/jWYhS5EOlh", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1679412279744548864", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1677537058594881541"], "editable_until_msecs": "1688794576000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "372", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1, "bookmarked": true, "created_at": "Sat Jul 08 04:36:16 +0000 2023", "conversation_id_str": "1677329729379004417", "display_text_range": [6, 6], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/YlKCs6L7vA", "expanded_url": "https://x.com/MasterBolte/status/1677537058594881541/photo/1", "id_str": "1677537051246485504", "indices": [7, 30], "media_key": "3_1677537051246485504", "media_url_https": "https://pbs.twimg.com/media/F0fPD2UaYAAbQEP.jpg", "type": "photo", "url": "https://t.co/YlKCs6L7vA", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1153, "w": 2048, "resize": "fit"}, "medium": {"h": 676, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1153, "width": 2048, "focus_rects": [{"x": 0, "y": 0, "w": 2048, "h": 1147}, {"x": 0, "y": 0, "w": 1153, "h": 1153}, {"x": 0, "y": 0, "w": 1011, "h": 1153}, {"x": 70, "y": 0, "w": 577, "h": 1153}, {"x": 0, "y": 0, "w": 2048, "h": 1153}]}, "media_results": {"result": {"media_key": "3_1677537051246485504"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "1536701233033105409", "name": "سردم آيوپ", "screen_name": "9ouya", "indices": [0, 6]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/YlKCs6L7vA", "expanded_url": "https://x.com/MasterBolte/status/1677537058594881541/photo/1", "id_str": "1677537051246485504", "indices": [7, 30], "media_key": "3_1677537051246485504", "media_url_https": "https://pbs.twimg.com/media/F0fPD2UaYAAbQEP.jpg", "type": "photo", "url": "https://t.co/YlKCs6L7vA", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1153, "w": 2048, "resize": "fit"}, "medium": {"h": 676, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1153, "width": 2048, "focus_rects": [{"x": 0, "y": 0, "w": 2048, "h": 1147}, {"x": 0, "y": 0, "w": 1153, "h": 1153}, {"x": 0, "y": 0, "w": 1011, "h": 1153}, {"x": 70, "y": 0, "w": 577, "h": 1153}, {"x": 0, "y": 0, "w": 2048, "h": 1153}]}, "media_results": {"result": {"media_key": "3_1677537051246485504"}}}]}, "favorite_count": 4, "favorited": false, "full_text": "@9ouya https://t.co/YlKCs6L7vA", "in_reply_to_screen_name": "9ouya", "in_reply_to_status_id_str": "1677504683601010690", "in_reply_to_user_id_str": "1536701233033105409", "is_quote_status": false, "lang": "qme", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "824237032167718912", "id_str": "1677537058594881541"}, "twe_private_fields": {"created_at": 1688790976000, "updated_at": 1748554188924, "media_count": 1}}}]