[{"id": "1540228762427588610", "created_at": "2022-06-24 10:02:07 +03:00", "full_text": "I wish I had known about this before...\n\n https://t.co/GHxK81NSrv", "media": [{"type": "video", "url": "https://t.co/GHxK81NSrv", "thumbnail": "https://pbs.twimg.com/amplify_video_thumb/1253395192050319361/img/K0r5vAGptsi3wVYZ.jpg?name=thumb", "original": "https://video.twimg.com/amplify_video/1253395192050319361/vid/400x400/MNVKYfvh-WHZ4Su1.mp4?tag=13"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 20095, "retweet_count": 5346, "bookmark_count": 2575, "quote_count": 199, "reply_count": 99, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1540228762427588610", "metadata": {"__typename": "Tweet", "rest_id": "1540228762427588610", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NDU0MzgwNA==", "rest_id": "********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1908358126870409216/B4efXE9z_normal.jpg"}, "core": {"created_at": "Fri Dec 04 11:56:23 +0000 2009", "name": "<PERSON><PERSON>", "screen_name": "TansuYegen"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Exploring the intersections of AI, startups, and the economy 🌐 | Top 100 Most Influential X Accounts | All opinions are my own #AI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yegentansu.com", "expanded_url": "https://yegentansu.com", "url": "https://t.co/6PEuuxhuuE", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8992, "followers_count": 1537677, "friends_count": 1951, "has_custom_timelines": true, "is_translator": false, "listed_count": 12147, "media_count": 31577, "normal_followers_count": 1537677, "pinned_tweet_ids_str": ["1927037175436070949"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/********/**********", "profile_interstitial_type": "", "statuses_count": 47983, "translator_type": "none", "url": "https://t.co/6PEuuxhuuE", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Turkey"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1540228762427588610"], "editable_until_msecs": "1656055927000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2575, "bookmarked": true, "created_at": "Fri Jun 24 07:02:07 +0000 2022", "conversation_id_str": "1540228762427588610", "display_text_range": [0, 65], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/GHxK81NSrv", "expanded_url": "https://x.com/TansuYegen/status/1253397565988634627/video/1", "id_str": "1253395192050319361", "indices": [42, 65], "media_key": "13_1253395192050319361", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1253395192050319361/img/K0r5vAGptsi3wVYZ.jpg", "source_status_id_str": "1253397565988634627", "source_user_id_str": "********", "type": "video", "url": "https://t.co/GHxK81NSrv", "additional_media_info": {"title": "", "description": "", "embeddable": true, "monetizable": false, "source_user": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NDU0MzgwNA==", "rest_id": "********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1908358126870409216/B4efXE9z_normal.jpg"}, "core": {"created_at": "Fri Dec 04 11:56:23 +0000 2009", "name": "<PERSON><PERSON>", "screen_name": "TansuYegen"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Exploring the intersections of AI, startups, and the economy 🌐 | Top 100 Most Influential X Accounts | All opinions are my own #AI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yegentansu.com", "expanded_url": "https://yegentansu.com", "url": "https://t.co/6PEuuxhuuE", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8992, "followers_count": 1537677, "friends_count": 1951, "has_custom_timelines": true, "is_translator": false, "listed_count": 12147, "media_count": 31577, "normal_followers_count": 1537677, "pinned_tweet_ids_str": ["1927037175436070949"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/********/**********", "profile_interstitial_type": "", "statuses_count": 47983, "translator_type": "none", "url": "https://t.co/6PEuuxhuuE", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Turkey"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 400, "w": 400, "resize": "fit"}, "medium": {"h": 400, "w": 400, "resize": "fit"}, "small": {"h": 400, "w": 400, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 400, "width": 400, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "duration_millis": 208676, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1253395192050319361/pl/DcapqIg7SDBz2_F6.m3u8?tag=13"}, {"bitrate": 432000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1253395192050319361/vid/320x320/WGvvlXjhxodMmv4w.mp4?tag=13"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1253395192050319361/vid/400x400/MNVKYfvh-WHZ4Su1.mp4?tag=13"}]}, "media_results": {"result": {"media_key": "13_1253395192050319361"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/GHxK81NSrv", "expanded_url": "https://x.com/TansuYegen/status/1253397565988634627/video/1", "id_str": "1253395192050319361", "indices": [42, 65], "media_key": "13_1253395192050319361", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1253395192050319361/img/K0r5vAGptsi3wVYZ.jpg", "source_status_id_str": "1253397565988634627", "source_user_id_str": "********", "type": "video", "url": "https://t.co/GHxK81NSrv", "additional_media_info": {"title": "", "description": "", "embeddable": true, "monetizable": false, "source_user": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NDU0MzgwNA==", "rest_id": "********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1908358126870409216/B4efXE9z_normal.jpg"}, "core": {"created_at": "Fri Dec 04 11:56:23 +0000 2009", "name": "<PERSON><PERSON>", "screen_name": "TansuYegen"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Exploring the intersections of AI, startups, and the economy 🌐 | Top 100 Most Influential X Accounts | All opinions are my own #AI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yegentansu.com", "expanded_url": "https://yegentansu.com", "url": "https://t.co/6PEuuxhuuE", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8992, "followers_count": 1537677, "friends_count": 1951, "has_custom_timelines": true, "is_translator": false, "listed_count": 12147, "media_count": 31577, "normal_followers_count": 1537677, "pinned_tweet_ids_str": ["1927037175436070949"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/********/**********", "profile_interstitial_type": "", "statuses_count": 47983, "translator_type": "none", "url": "https://t.co/6PEuuxhuuE", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Turkey"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 400, "w": 400, "resize": "fit"}, "medium": {"h": 400, "w": 400, "resize": "fit"}, "small": {"h": 400, "w": 400, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 400, "width": 400, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "duration_millis": 208676, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1253395192050319361/pl/DcapqIg7SDBz2_F6.m3u8?tag=13"}, {"bitrate": 432000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1253395192050319361/vid/320x320/WGvvlXjhxodMmv4w.mp4?tag=13"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1253395192050319361/vid/400x400/MNVKYfvh-WHZ4Su1.mp4?tag=13"}]}, "media_results": {"result": {"media_key": "13_1253395192050319361"}}}]}, "favorite_count": 20095, "favorited": false, "full_text": "I wish I had known about this before...\n\n https://t.co/GHxK81NSrv", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 199, "reply_count": 99, "retweet_count": 5346, "retweeted": false, "user_id_str": "********", "id_str": "1540228762427588610"}, "twe_private_fields": {"created_at": 1656054127000, "updated_at": 1748554267519, "media_count": 1}}}, {"id": "1541109381030461442", "created_at": "2022-06-26 20:21:23 +03:00", "full_text": "The situation with the Glazers and the dividends at #manutd explained 🧵 \n\nUnless you’ve been living under a rock, you'll know that £11 million has been taken from the club in dividends. How are they doing this and what are the implications? I'm going to highlight all ⬇️", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2013, "retweet_count": 499, "bookmark_count": 189, "quote_count": 91, "reply_count": 129, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1541109381030461442", "metadata": {"__typename": "Tweet", "rest_id": "1541109381030461442", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NTMzMjI2MTI1NDA3MDI3MjE=", "rest_id": "953322612540702721", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915422396145684480/tcWax3mO_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 16 17:46:37 +0000 2018", "name": "<PERSON>", "screen_name": "shaunconnolly85"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Editor-in-chief @theatre_of_red | European football correspondent for @BTLVid | Freelance writer and broadcaster", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "theatre-of-red.com", "expanded_url": "http://theatre-of-red.com", "url": "https://t.co/HfrRMcM55R", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 9009, "followers_count": 6362, "friends_count": 312, "has_custom_timelines": true, "is_translator": false, "listed_count": 60, "media_count": 617, "normal_followers_count": 6362, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/953322612540702721/1693331461", "profile_interstitial_type": "", "statuses_count": 14228, "translator_type": "none", "url": "https://t.co/HfrRMcM55R", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1514029393982771209", "professional_type": "Creator", "category": [{"id": 955, "name": "Journalist", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1541109381030461442"], "editable_until_msecs": "1656265883000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 189, "bookmarked": true, "created_at": "Sun Jun 26 17:21:23 +0000 2022", "conversation_id_str": "1541109381030461442", "display_text_range": [0, 270], "entities": {"hashtags": [{"indices": [52, 59], "text": "man<PERSON>d"}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2013, "favorited": false, "full_text": "The situation with the Glazers and the dividends at #manutd explained 🧵 \n\nUnless you’ve been living under a rock, you'll know that £11 million has been taken from the club in dividends. How are they doing this and what are the implications? I'm going to highlight all ⬇️", "is_quote_status": false, "lang": "en", "quote_count": 91, "reply_count": 129, "retweet_count": 499, "retweeted": false, "user_id_str": "953322612540702721", "id_str": "1541109381030461442"}, "twe_private_fields": {"created_at": 1656264083000, "updated_at": 1748554262696, "media_count": 0}}}]