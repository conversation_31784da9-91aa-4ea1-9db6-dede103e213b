[{"id": "1406676639023521792", "created_at": "2021-06-20 21:13:40 +03:00", "full_text": "<PERSON> - <PERSON> Church In The Wild\n\n[@WayneRooney]\nhttps://t.co/CA6lUHg2G8", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2316, "retweet_count": 594, "bookmark_count": 190, "quote_count": 122, "reply_count": 45, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1406676639023521792", "metadata": {"__typename": "Tweet", "rest_id": "1406676639023521792", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTk2MzkzOTE3NzQ0MzMyODAx", "rest_id": "1196393917744332801", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1715870698281877504/KDyPAYJa_normal.jpg"}, "core": {"created_at": "Mon Nov 18 11:45:22 +0000 2019", "name": "🏴󠁧󠁢󠁥󠁮󠁧󠁿", "screen_name": "hxMUFC"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Boxing. Manchester United. WWF. To Be The Man, <PERSON> Gotta Beat The Man. Historian. 🇬🇧 🏴󠁧󠁢󠁥󠁮󠁧󠁿", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 21235, "followers_count": 2996, "friends_count": 867, "has_custom_timelines": true, "is_translator": false, "listed_count": 18, "media_count": 7374, "normal_followers_count": 2996, "pinned_tweet_ids_str": ["1398001141636603905"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1196393917744332801/1669248741", "profile_interstitial_type": "", "statuses_count": 33797, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Moston, Manchester "}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1406676639023521792"], "editable_until_msecs": "1624214620188", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 190, "bookmarked": true, "created_at": "Sun Jun 20 18:13:40 +0000 2021", "conversation_id_str": "1406676639023521792", "display_text_range": [0, 76], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/LancsOG/status…", "expanded_url": "https://x.com/LancsOG/status/1406659266996588552/video/1", "url": "https://t.co/CA6lUHg2G8", "indices": [53, 76]}], "user_mentions": [{"id_str": "285332860", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>", "indices": [39, 51]}]}, "favorite_count": 2316, "favorited": false, "full_text": "<PERSON> - <PERSON> Church In The Wild\n\n[@WayneRooney]\nhttps://t.co/CA6lUHg2G8", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 122, "reply_count": 45, "retweet_count": 594, "retweeted": false, "user_id_str": "1196393917744332801", "id_str": "1406676639023521792"}, "twe_private_fields": {"created_at": 1624212820000, "updated_at": 1748554407656, "media_count": 0}}}, {"id": "1407110230648012801", "created_at": "2021-06-22 01:56:36 +03:00", "full_text": "Python Functions for beginners 🧵\n\nFunctions are a block of code that can be executed in a standardised way\nAll Python frameworks, like Django or Flask, rely on them.\nKnowing the basics is essential\n\nWhat we covered so far 👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 6638, "retweet_count": 1167, "bookmark_count": 1807, "quote_count": 36, "reply_count": 116, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1407110230648012801", "metadata": {"__typename": "Tweet", "rest_id": "1407110230648012801", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NDY2NjI0NjAzNzkwNzQ1NjA=", "rest_id": "946662460379074560", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/Scroll_ZKP", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1696531511519150080/Fq5O0LeN_bigger.jpg"}, "description": "<PERSON><PERSON>", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1925217433247207424/idDnXjZ5_normal.jpg"}, "core": {"created_at": "Fri Dec 29 08:41:33 +0000 2017", "name": "Raza", "screen_name": "razacodes"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Head of Growth @Scroll_ZKP • building the @_openeconomy", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "razacodes.com", "expanded_url": "https://www.razacodes.com", "url": "https://t.co/PWQEf6Hh6J", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 41425, "followers_count": 67131, "friends_count": 1930, "has_custom_timelines": true, "is_translator": false, "listed_count": 427, "media_count": 1738, "normal_followers_count": 67131, "pinned_tweet_ids_str": ["1925641124141490278"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/946662460379074560/1711193661", "profile_interstitial_type": "", "statuses_count": 17631, "translator_type": "none", "url": "https://t.co/PWQEf6Hh6J", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "The Hague, The Netherlands"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1407110230648012801"], "editable_until_msecs": "1624317996487", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1807, "bookmarked": true, "created_at": "Mon Jun 21 22:56:36 +0000 2021", "conversation_id_str": "1407110230648012801", "display_text_range": [0, 223], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 6638, "favorited": false, "full_text": "Python Functions for beginners 🧵\n\nFunctions are a block of code that can be executed in a standardised way\nAll Python frameworks, like Django or Flask, rely on them.\nKnowing the basics is essential\n\nWhat we covered so far 👇", "is_quote_status": false, "lang": "en", "quote_count": 36, "reply_count": 116, "retweet_count": 1167, "retweeted": false, "user_id_str": "946662460379074560", "id_str": "1407110230648012801"}, "twe_private_fields": {"created_at": 1624316196000, "updated_at": 1748554391966, "media_count": 0}}}]