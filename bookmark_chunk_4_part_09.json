[{"id": "1610571991068680192", "created_at": "2023-01-04 12:41:00 +03:00", "full_text": "Free certifications in 2023:\n\nPython, Data Science, ML, AI:\n~ https://t.co/cwZyia9NCq\n~ https://t.co/UTmBk1cfiW\n~ https://t.co/BkOUHsMGxX\n~ https://t.co/TM1PreZsY7\n\nSoftware Engineering:\n~ https://t.co/kIC9deWyJ4\n~ https://t.co/8jeQCUE8TQ\n\nMiscellaneous:\n~ https://t.co/z7C2wPyp8e", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3049, "retweet_count": 1047, "bookmark_count": 1877, "quote_count": 6, "reply_count": 49, "views_count": 256237, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1610571991068680192", "metadata": {"__typename": "Tweet", "rest_id": "1610571991068680192", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5MDMzNzQxOTQ4Mjk4MTk5MDU=", "rest_id": "903374194829819905", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1759706558844248064/dFKF9hDk_normal.jpg"}, "core": {"created_at": "Thu Aug 31 21:49:26 +0000 2017", "name": "Ezekiel", "screen_name": "ezekiel_aleke"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Data Analyst | Author: DATA ANALYSIS MADE EASY | Visit: https://t.co/ubk4F6Uzcx | For 1:1 Training and Promotion DM", "entities": {"description": {"urls": [{"display_url": "selar.com/bpr5", "expanded_url": "https://selar.com/bpr5", "url": "https://t.co/ubk4F6Uzcx", "indices": [56, 79]}]}, "url": {"urls": [{"display_url": "ezekielaleke.substack.com", "expanded_url": "http://ezekielaleke.substack.com", "url": "https://t.co/hBRBJN38cJ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 37649, "followers_count": 104910, "friends_count": 117, "has_custom_timelines": true, "is_translator": false, "listed_count": 871, "media_count": 3472, "normal_followers_count": 104910, "pinned_tweet_ids_str": ["1644045988045422592"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/903374194829819905/1714058812", "profile_interstitial_type": "", "statuses_count": 16525, "translator_type": "none", "url": "https://t.co/hBRBJN38cJ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Receive my weekly message ⬇️"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1511413429231308812", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/z7C2wPyp8e", "legacy": {"binding_values": [{"key": "thumbnail_image", "value": {"image_value": {"height": 144, "width": 144, "url": "https://pbs.twimg.com/card_img/1926887913289121792/XDrofgTe?format=png&name=144x144_2"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Explore training and tools to grow your business and online presence and learn digital skills to grow your career and qualify for in-demand jobs.", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "grow.google", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 420, "width": 420, "url": "https://pbs.twimg.com/card_img/1926887913289121792/XDrofgTe?format=png&name=420x420_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 1200, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926887913289121792/XDrofgTe?format=png&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "20536157", "path": []}}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 100, "width": 100, "url": "https://pbs.twimg.com/card_img/1926887913289121792/XDrofgTe?format=png&name=100x100_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 1200, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926887913289121792/XDrofgTe?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "grow.google", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 97.76}, {"rgb": {"blue": 250, "green": 206, "red": 179}, "percentage": 0.38}, {"rgb": {"blue": 245, "green": 147, "red": 88}, "percentage": 0.35}, {"rgb": {"blue": 79, "green": 91, "red": 236}, "percentage": 0.23}, {"rgb": {"blue": 39, "green": 196, "red": 250}, "percentage": 0.15}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "Grow with Google - Training to Grow Your Business & Career- Grow with Google", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/z7C2wPyp8e", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary", "url": "https://t.co/z7C2wPyp8e", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoyMDUzNjE1Nw==", "rest_id": "20536157", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1754606338460487681/bWupXdxo_normal.jpg"}, "core": {"created_at": "Tue Feb 10 19:14:39 +0000 2009", "name": "Google", "screen_name": "Google"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Here to help. https://t.co/uo9YCpoo4q", "entities": {"description": {"urls": [{"display_url": "io.google", "expanded_url": "http://io.google", "url": "https://t.co/uo9YCpoo4q", "indices": [14, 37]}]}, "url": {"urls": [{"display_url": "blog.google/newsletter-sub…", "expanded_url": "https://blog.google/newsletter-subscribe/", "url": "https://t.co/RS3iU873QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 3752, "followers_count": 32627717, "friends_count": 284, "has_custom_timelines": true, "is_translator": false, "listed_count": 89720, "media_count": 37428, "normal_followers_count": 32627717, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/20536157/1747843765", "profile_interstitial_type": "", "statuses_count": 234130, "translator_type": "regular", "url": "https://t.co/RS3iU873QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Mountain View, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1504886752259059716", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1610571991068680192"], "editable_until_msecs": "1672827060000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "256237", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1877, "bookmarked": true, "created_at": "Wed Jan 04 09:41:00 +0000 2023", "conversation_id_str": "1610571991068680192", "display_text_range": [0, 280], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "netacad.com/courses/progra…", "expanded_url": "http://netacad.com/courses/programming/pcap-programming-essentials-python", "url": "https://t.co/BkOUHsMGxX", "indices": [114, 137]}, {"display_url": "data-flair.training/python-course/", "expanded_url": "http://data-flair.training/python-course/", "url": "https://t.co/TM1PreZsY7", "indices": [140, 163]}, {"display_url": "testautomationu.applitools.com", "expanded_url": "http://testautomationu.applitools.com", "url": "https://t.co/8jeQCUE8TQ", "indices": [215, 238]}, {"display_url": "kaggle.com/learn", "expanded_url": "http://kaggle.com/learn", "url": "https://t.co/cwZyia9NCq", "indices": [62, 85]}, {"display_url": "learndigital.withgoogle.com/digitalgarage/", "expanded_url": "http://learndigital.withgoogle.com/digitalgarage/", "url": "https://t.co/z7C2wPyp8e", "indices": [257, 280]}, {"display_url": "freecodecamp.org", "expanded_url": "http://freecodecamp.org", "url": "https://t.co/kIC9deWyJ4", "indices": [189, 212]}, {"display_url": "cognitiveclass.ai/learn", "expanded_url": "http://cognitiveclass.ai/learn", "url": "https://t.co/UTmBk1cfiW", "indices": [88, 111]}], "user_mentions": []}, "favorite_count": 3049, "favorited": false, "full_text": "Free certifications in 2023:\n\nPython, Data Science, ML, AI:\n~ https://t.co/cwZyia9NCq\n~ https://t.co/UTmBk1cfiW\n~ https://t.co/BkOUHsMGxX\n~ https://t.co/TM1PreZsY7\n\nSoftware Engineering:\n~ https://t.co/kIC9deWyJ4\n~ https://t.co/8jeQCUE8TQ\n\nMiscellaneous:\n~ https://t.co/z7C2wPyp8e", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 49, "retweet_count": 1047, "retweeted": false, "user_id_str": "903374194829819905", "id_str": "1610571991068680192"}, "twe_private_fields": {"created_at": 1672825260000, "updated_at": 1748554204513, "media_count": 0}}}, {"id": "1612104085330608128", "created_at": "2023-01-08 18:09:00 +03:00", "full_text": "💡 Here's a list of Python projects for beginners!\n\n#Python #Beginners #DataScience https://t.co/LJeqP2NPb1", "media": [{"type": "photo", "url": "https://t.co/LJeqP2NPb1", "thumbnail": "https://pbs.twimg.com/media/Fl9YH4VWQAIbek3?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Fl9YH4VWQAIbek3?format=jpg&name=orig", "ext_alt_text": "python-beginners-data science"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3807, "retweet_count": 779, "bookmark_count": 1547, "quote_count": 12, "reply_count": 43, "views_count": 310525, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1612104085330608128", "metadata": {"__typename": "Tweet", "rest_id": "1612104085330608128", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzE4OTg1MjQw", "rest_id": "1318985240", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1740442175081717761/bodOvWRp_normal.jpg"}, "core": {"created_at": "Sun Mar 31 19:25:57 +0000 2013", "name": "Data Science Dojo", "screen_name": "DataScienceDojo"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "We make learning data science and LLMs easy! Join the community of 10,000+ professionals.\n#DSDojo", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "datasciencedojo.com", "expanded_url": "https://datasciencedojo.com/", "url": "https://t.co/xSbtUkHXSC", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7121, "followers_count": 210090, "friends_count": 954, "has_custom_timelines": true, "is_translator": false, "listed_count": 2354, "media_count": 11493, "normal_followers_count": 210090, "pinned_tweet_ids_str": ["1849092667306594392"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1318985240/1667935586", "profile_interstitial_type": "", "statuses_count": 58323, "translator_type": "none", "url": "https://t.co/xSbtUkHXSC", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Seattle, WA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1451017961360216066", "professional_type": "Business", "category": [{"id": 145, "name": "Education Company", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1612104085330608128"], "editable_until_msecs": "1673192340000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "310525", "state": "EnabledWithCount"}, "source": "<a href=\"http://www.hubspot.com/\" rel=\"nofollow\">HubSpot</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1547, "bookmarked": true, "created_at": "Sun Jan 08 15:09:00 +0000 2023", "conversation_id_str": "1612104085330608128", "display_text_range": [0, 82], "entities": {"hashtags": [{"indices": [51, 58], "text": "Python"}, {"indices": [59, 69], "text": "Beginners"}, {"indices": [70, 82], "text": "DataScience"}], "media": [{"display_url": "pic.x.com/LJeqP2NPb1", "expanded_url": "https://x.com/DataScienceDojo/status/1612104085330608128/photo/1", "ext_alt_text": "python-beginners-data science", "id_str": "1612104084026179586", "indices": [83, 106], "media_key": "3_1612104084026179586", "media_url_https": "https://pbs.twimg.com/media/Fl9YH4VWQAIbek3.jpg", "type": "photo", "url": "https://t.co/LJeqP2NPb1", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 647, "w": 563, "resize": "fit"}, "medium": {"h": 647, "w": 563, "resize": "fit"}, "small": {"h": 647, "w": 563, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 647, "width": 563, "focus_rects": [{"x": 0, "y": 0, "w": 563, "h": 315}, {"x": 0, "y": 0, "w": 563, "h": 563}, {"x": 0, "y": 0, "w": 563, "h": 642}, {"x": 145, "y": 0, "w": 324, "h": 647}, {"x": 0, "y": 0, "w": 563, "h": 647}]}, "media_results": {"result": {"media_key": "3_1612104084026179586"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/LJeqP2NPb1", "expanded_url": "https://x.com/DataScienceDojo/status/1612104085330608128/photo/1", "ext_alt_text": "python-beginners-data science", "id_str": "1612104084026179586", "indices": [83, 106], "media_key": "3_1612104084026179586", "media_url_https": "https://pbs.twimg.com/media/Fl9YH4VWQAIbek3.jpg", "type": "photo", "url": "https://t.co/LJeqP2NPb1", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 647, "w": 563, "resize": "fit"}, "medium": {"h": 647, "w": 563, "resize": "fit"}, "small": {"h": 647, "w": 563, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 647, "width": 563, "focus_rects": [{"x": 0, "y": 0, "w": 563, "h": 315}, {"x": 0, "y": 0, "w": 563, "h": 563}, {"x": 0, "y": 0, "w": 563, "h": 642}, {"x": 145, "y": 0, "w": 324, "h": 647}, {"x": 0, "y": 0, "w": 563, "h": 647}]}, "media_results": {"result": {"media_key": "3_1612104084026179586"}}}]}, "favorite_count": 3807, "favorited": false, "full_text": "💡 Here's a list of Python projects for beginners!\n\n#Python #Beginners #DataScience https://t.co/LJeqP2NPb1", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 12, "reply_count": 43, "retweet_count": 779, "retweeted": false, "user_id_str": "1318985240", "id_str": "1612104085330608128"}, "twe_private_fields": {"created_at": 1673190540000, "updated_at": 1748554204513, "media_count": 1}}}]