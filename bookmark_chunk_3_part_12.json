[{"id": "1532330240621821953", "created_at": "2022-06-02 14:56:13 +03:00", "full_text": "👩‍💻 Practice 150+ Problems\n\nUsing your favourite language\n    ❍  C\n    ❍  C++\n    ❍  Python\n    ❍  Java\n    ❍  C#\n    ❍  PHP\n    ❍  Golang\n    ❍  JavaScript\n\nCrack your next Interview.\n\n⇩", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1628, "retweet_count": 422, "bookmark_count": 799, "quote_count": 11, "reply_count": 73, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532330240621821953", "metadata": {"__typename": "Tweet", "rest_id": "1532330240621821953", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204229, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204229, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532330240621821953"], "editable_until_msecs": "1654172773000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 799, "bookmarked": true, "created_at": "Thu Jun 02 11:56:13 +0000 2022", "conversation_id_str": "1532330240621821953", "display_text_range": [0, 187], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1628, "favorited": false, "full_text": "👩‍💻 Practice 150+ Problems\n\nUsing your favourite language\n    ❍  C\n    ❍  C++\n    ❍  Python\n    ❍  Java\n    ❍  C#\n    ❍  PHP\n    ❍  Golang\n    ❍  JavaScript\n\nCrack your next Interview.\n\n⇩", "is_quote_status": false, "lang": "en", "quote_count": 11, "reply_count": 73, "retweet_count": 422, "retweeted": false, "user_id_str": "69941978", "id_str": "1532330240621821953"}, "twe_private_fields": {"created_at": 1654170973000, "updated_at": 1748554271915, "media_count": 0}}}, {"id": "1532482041303822336", "created_at": "2022-06-03 00:59:25 +03:00", "full_text": "You don’t have to learn to code alone.\n\nYou can get involved in a community.\n\nYou can find a mentor to guide &amp; encourage you.\n\nYou got this 💪💪💪", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 293, "retweet_count": 20, "bookmark_count": 9, "quote_count": 3, "reply_count": 26, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532482041303822336", "metadata": {"__typename": "Tweet", "rest_id": "1532482041303822336", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMDEzNjk1ODE=", "rest_id": "101369581", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/Sourcegraph", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1885750906899079169/p98Ru-Pl_bigger.jpg"}, "description": "Sourcegraph", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1732269446713217024/OrNokBIn_normal.jpg"}, "core": {"created_at": "Sun Jan 03 02:10:09 +0000 2010", "name": "<PERSON><PERSON> aka CS Dojo 📺🐦", "screen_name": "yk<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "- Developer Experience Manager @Sourcegraph & @AmpCode\n- CS Dojo on YT (1.9M+ subs)\n- Coding with AI, next-level: https://t.co/8xoqGiUxV4", "entities": {"description": {"urls": [{"display_url": "agenticcoding.substack.com", "expanded_url": "https://agenticcoding.substack.com/", "url": "https://t.co/8xoqGiUxV4", "indices": [114, 137]}]}, "url": {"urls": [{"display_url": "agenticcoding.substack.com", "expanded_url": "https://agenticcoding.substack.com/", "url": "https://t.co/8xoqGiUxV4", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 46943, "followers_count": 149236, "friends_count": 474, "has_custom_timelines": true, "is_translator": false, "listed_count": 749, "media_count": 526, "normal_followers_count": 149236, "pinned_tweet_ids_str": ["1924539279147196572"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/101369581/1734243728", "profile_interstitial_type": "", "statuses_count": 12633, "translator_type": "none", "url": "https://t.co/8xoqGiUxV4", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Sunny Francisco & Raincouver"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532482041303822336"], "editable_until_msecs": "1654208965000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 9, "bookmarked": true, "created_at": "Thu Jun 02 21:59:25 +0000 2022", "conversation_id_str": "1532482041303822336", "display_text_range": [0, 147], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 293, "favorited": false, "full_text": "You don’t have to learn to code alone.\n\nYou can get involved in a community.\n\nYou can find a mentor to guide &amp; encourage you.\n\nYou got this 💪💪💪", "is_quote_status": false, "lang": "en", "quote_count": 3, "reply_count": 26, "retweet_count": 20, "retweeted": false, "user_id_str": "101369581", "id_str": "1532482041303822336"}, "twe_private_fields": {"created_at": 1654207165000, "updated_at": 1748554267520, "media_count": 0}}}]