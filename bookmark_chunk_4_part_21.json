[{"id": "1650498539519180805", "created_at": "2023-04-24 16:54:51 +03:00", "full_text": "17 FREE Courses from <PERSON>ggle:\n\nData Cleaning\nIntro+Advanced SQL\nPython\nPandas\nData Visualization\nFeature Engineering\nIntro+Intermediate ML\nIntro Deep Learning\nComputer Vision\nAI Ethics\nGeospatial Analysis\nML Explainability\nNLP\nIntro to Game AI\nTime Series\n\nhttps://t.co/cwZyia9NCq", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1118, "retweet_count": 361, "bookmark_count": 911, "quote_count": 0, "reply_count": 9, "views_count": 126570, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1650498539519180805", "metadata": {"__typename": "Tweet", "rest_id": "1650498539519180805", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5MDMzNzQxOTQ4Mjk4MTk5MDU=", "rest_id": "903374194829819905", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1759706558844248064/dFKF9hDk_normal.jpg"}, "core": {"created_at": "Thu Aug 31 21:49:26 +0000 2017", "name": "Ezekiel", "screen_name": "ezekiel_aleke"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Data Analyst | Author: DATA ANALYSIS MADE EASY | Visit: https://t.co/ubk4F6Uzcx | For 1:1 Training and Promotion DM", "entities": {"description": {"urls": [{"display_url": "selar.com/bpr5", "expanded_url": "https://selar.com/bpr5", "url": "https://t.co/ubk4F6Uzcx", "indices": [56, 79]}]}, "url": {"urls": [{"display_url": "ezekielaleke.substack.com", "expanded_url": "http://ezekielaleke.substack.com", "url": "https://t.co/hBRBJN38cJ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 37649, "followers_count": 104912, "friends_count": 117, "has_custom_timelines": true, "is_translator": false, "listed_count": 871, "media_count": 3472, "normal_followers_count": 104912, "pinned_tweet_ids_str": ["1644045988045422592"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/903374194829819905/1714058812", "profile_interstitial_type": "", "statuses_count": 16525, "translator_type": "none", "url": "https://t.co/hBRBJN38cJ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Receive my weekly message ⬇️"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1511413429231308812", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/cwZyia9NCq", "legacy": {"binding_values": [{"key": "thumbnail_image", "value": {"image_value": {"height": 144, "width": 144, "url": "https://pbs.twimg.com/card_img/1927234691523813376/s-48_q8B?format=png&name=144x144_2"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Practical data skills you can apply immediately: that's what you'll learn in these no-cost courses. They're the fastest (and most fun) way to become a data scientist or improve your current skills.", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "www.kaggle.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 420, "width": 420, "url": "https://pbs.twimg.com/card_img/1927234691523813376/s-48_q8B?format=png&name=420x420_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 512, "width": 512, "url": "https://pbs.twimg.com/card_img/1927234691523813376/s-48_q8B?format=png&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "80422885", "path": []}}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 100, "width": 100, "url": "https://pbs.twimg.com/card_img/1927234691523813376/s-48_q8B?format=png&name=100x100_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 512, "width": 512, "url": "https://pbs.twimg.com/card_img/1927234691523813376/s-48_q8B?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "kaggle.com", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 67, "green": 64, "red": 60}, "percentage": 88.23}, {"rgb": {"blue": 255, "green": 190, "red": 32}, "percentage": 11.77}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "Learn Python, Data Viz, Pandas & More | Tutorials | Kaggle", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/cwZyia9NCq", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary", "url": "https://t.co/cwZyia9NCq", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjo4MDQyMjg4NQ==", "rest_id": "80422885", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1573129499343978498/03a7wgfE_normal.jpg"}, "core": {"created_at": "<PERSON>e Oct 06 22:42:08 +0000 2009", "name": "<PERSON><PERSON>", "screen_name": "kaggle"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The world's largest community of data scientists. Join us to compete, collaborate, learn, and share your work.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "kaggle.com", "expanded_url": "http://kaggle.com", "url": "https://t.co/tvevctHn0D", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2504, "followers_count": 298208, "friends_count": 285, "has_custom_timelines": false, "is_translator": false, "listed_count": 5229, "media_count": 1306, "normal_followers_count": 298208, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/80422885/1588784887", "profile_interstitial_type": "", "statuses_count": 5162, "translator_type": "none", "url": "https://t.co/tvevctHn0D", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "San Francisco"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1650498539519180805"], "editable_until_msecs": "1682346291000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "126570", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 911, "bookmarked": true, "created_at": "Mon Apr 24 13:54:51 +0000 2023", "conversation_id_str": "1650498539519180805", "display_text_range": [0, 279], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "kaggle.com/learn", "expanded_url": "http://kaggle.com/learn", "url": "https://t.co/cwZyia9NCq", "indices": [256, 279]}], "user_mentions": []}, "favorite_count": 1118, "favorited": false, "full_text": "17 FREE Courses from <PERSON>ggle:\n\nData Cleaning\nIntro+Advanced SQL\nPython\nPandas\nData Visualization\nFeature Engineering\nIntro+Intermediate ML\nIntro Deep Learning\nComputer Vision\nAI Ethics\nGeospatial Analysis\nML Explainability\nNLP\nIntro to Game AI\nTime Series\n\nhttps://t.co/cwZyia9NCq", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 9, "retweet_count": 361, "retweeted": false, "user_id_str": "903374194829819905", "id_str": "1650498539519180805"}, "twe_private_fields": {"created_at": 1682344491000, "updated_at": 1748554197457, "media_count": 0}}}, {"id": "1650808767691526144", "created_at": "2023-04-25 13:27:35 +03:00", "full_text": "Get Ripped at Home: Explosive Chest and Abs Workout to Sculpt Your Dream Physique https://t.co/aSOi9uPa80", "media": [{"type": "video", "url": "https://t.co/aSOi9uPa80", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1650808741355298817/pu/img/dUqtKIdDbViiKGF8.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/588x576/y5mKcEYNDz7r_pAM.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 14404, "retweet_count": 2207, "bookmark_count": 7530, "quote_count": 37, "reply_count": 63, "views_count": 3099952, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1650808767691526144", "metadata": {"__typename": "Tweet", "rest_id": "1650808767691526144", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzQ0ODkyOTM0MDg5NDQxMjgy", "rest_id": "1344892934089441282", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1390536079896322052/tywXphAl_normal.jpg"}, "core": {"created_at": "Fri Jan 01 06:27:41 +0000 2021", "name": "Aesthetic WorkOut", "screen_name": "aesthetics_way"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Aesthetics Physiques 🏋️ Bodybuilding Motivation 💪", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 633, "followers_count": 408416, "friends_count": 17, "has_custom_timelines": false, "is_translator": false, "listed_count": 2813, "media_count": 2136, "normal_followers_count": 408416, "pinned_tweet_ids_str": ["1921006885479514322"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1344892934089441282/**********", "profile_interstitial_type": "", "statuses_count": 2178, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "<PERSON><PERSON><PERSON> Account"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1650808767691526144"], "editable_until_msecs": "*************", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "3099952", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 7530, "bookmarked": true, "created_at": "<PERSON><PERSON> Apr 25 10:27:35 +0000 2023", "conversation_id_str": "1650808767691526144", "display_text_range": [0, 81], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/aSOi9uPa80", "expanded_url": "https://x.com/aesthetics_way/status/1650808767691526144/video/1", "id_str": "1650808741355298817", "indices": [82, 105], "media_key": "7_1650808741355298817", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1650808741355298817/pu/img/dUqtKIdDbViiKGF8.jpg", "type": "video", "url": "https://t.co/aSOi9uPa80", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 576, "w": 588, "resize": "fit"}, "medium": {"h": 576, "w": 588, "resize": "fit"}, "small": {"h": 576, "w": 588, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 576, "width": 588, "focus_rects": []}, "video_info": {"aspect_ratio": [49, 48], "duration_millis": 7615, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/pl/1-6ngFhOO1KZ74M0.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/274x270/D1UdQhDuYNSF_M2e.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/366x360/BD94Os6NIJ5FyGsW.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/588x576/y5mKcEYNDz7r_pAM.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1650808741355298817"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/aSOi9uPa80", "expanded_url": "https://x.com/aesthetics_way/status/1650808767691526144/video/1", "id_str": "1650808741355298817", "indices": [82, 105], "media_key": "7_1650808741355298817", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1650808741355298817/pu/img/dUqtKIdDbViiKGF8.jpg", "type": "video", "url": "https://t.co/aSOi9uPa80", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 576, "w": 588, "resize": "fit"}, "medium": {"h": 576, "w": 588, "resize": "fit"}, "small": {"h": 576, "w": 588, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 576, "width": 588, "focus_rects": []}, "video_info": {"aspect_ratio": [49, 48], "duration_millis": 7615, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/pl/1-6ngFhOO1KZ74M0.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/274x270/D1UdQhDuYNSF_M2e.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/366x360/BD94Os6NIJ5FyGsW.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650808741355298817/pu/vid/588x576/y5mKcEYNDz7r_pAM.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1650808741355298817"}}}]}, "favorite_count": 14404, "favorited": false, "full_text": "Get Ripped at Home: Explosive Chest and Abs Workout to Sculpt Your Dream Physique https://t.co/aSOi9uPa80", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 37, "reply_count": 63, "retweet_count": 2207, "retweeted": false, "user_id_str": "1344892934089441282", "id_str": "1650808767691526144"}, "twe_private_fields": {"created_at": 1682418455000, "updated_at": 1748554197457, "media_count": 1}}}]