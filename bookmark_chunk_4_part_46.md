# Bookmarks from bookmark_chunk_4_part_46.json

## Category: Technology / Android / Samsung / Windows / Link to Windows / Troubleshooting / App Ops / Shizuku

*   **Author:** @<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    **Date:** 2024-01-04 05:28:27 +03:00
    **Quoted Tweet:** [https://twitter.com/MishaalRahman/status/1742626814927778057](https://twitter.com/MishaalRahman/status/1742626814927778057)
    **Content:** A fix has been found for this problem! 

If you want stream apps from your Samsung device running Android 14 to your Windows PC without having to constantly regrant Link to Windows permission to record the screen, you just need to do a few simple steps.

1) Download the "App Ops - Permission Manager" app from Google Play.

2) Go through its setup process, which involves downloading a secondary app called Shizuku that acts as a helper to enable the App Ops app to access certain normally inaccessible APIs (in this case, AppOpsManager) using the shell UID.

3) Find "Link to Windows" in the list and scroll down until you see "Project media". Change it from "Ignore" to "Allow".

4) That's it! You should no longer see a MediaProjection consent dialog when you try to stream an app from your phone. 

Under the hood, what you're doing is changing the OP_PROJECT_MEDIA appop from "ignore" to "allow".

Before:

After:

(Sidenote: I tried changing the appop using the shell interface, but it didn't persist after a reboot. Changing the appop via the App Op app did. I don't know why, but it may be because the app is using the API directly instead of through this CLI.)

Thanks to @Abdelrahman5T for the tip! My complaints have led to oddly productive results today, lol.
    **URL:** [https://twitter.com/MishaalRahman/status/1742734709501632841](https://twitter.com/MishaalRahman/status/1742734709501632841)
    **Media:**
      - Type: photo
      - URL: https://t.co/cEeIP532tj
      - Thumbnail: https://pbs.twimg.com/media/GC9vbN8XoAAXrLc?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/GC9vbN8XoAAXrLc?format=jpg&name=orig
      - Type: photo
      - URL: https://t.co/cEeIP532tj
      - Thumbnail: https://pbs.twimg.com/media/GC9vnnaWIAAhTBy?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/GC9vnnaWIAAhTBy?format=jpg&name=orig
      - Type: photo
      - URL: https://t.co/cEeIP532tj
      - Thumbnail: https://pbs.twimg.com/media/GC9vwOOWsAMm3Xp?format=jpg&name=thumb
      - Original: https://pbs.twimg.com/media/GC9vwOOWsAMm3Xp?format=jpg&name=orig

---

## Category: Technology / AI / Productivity Tools / AI Tools / List / Resources

*   **Retweeted by:** @MishaalRahman
    **Original Author:** @levelsio
    **Original Date:** 2024-01-04 17:00:00 +03:00
    **Content:** My AI workflow in 2024:

Brainstorming/Research:
Perplexity Pro + ChatGPT 4

Visuals:
Midjourney for images
Krea for upscaling/enhancing/logo ideas
Pika Labs / Runway for video generation

Audio:
ElevenLabs for voice cloning/TTS
Descript for audio editing

Video:
Opus for repurposing long video to short clips
Captions for auto captions

Presentations:
Gamma / Tome for generating basic slides

Web design:
Relume for website structure sitemaps + wireframes
Framer for landing pages (with its AI)
Durable for basic sites for non-techies

Branding:
Looka / Namelix for logo/brand names

Copywriting:
Copy AI / Jasper for short copy
ChatGPT for long-form blog posts

SEO:
SurferSEO / Semrush for keyword research

It's a good time to be alive if you like to build stuff.
    **URL:** [https://twitter.com/levelsio/status/1742909883806949599](https://twitter.com/levelsio/status/1742909883806949599)
    **External Links:**
      - [https://perplexity.ai](https://perplexity.ai)
      - [https://chat.openai.com](https://chat.openai.com)
      - [https://midjourney.com](https://midjourney.com)
      - [https://krea.ai](https://krea.ai)
      - [https://pika.art](https://pika.art)
      - [https://runwayml.com](https://runwayml.com)
      - [https://elevenlabs.io](https://elevenlabs.io)
      - [https://descript.com](https://descript.com)
      - [https://opus.pro](https://opus.pro)
      - [https://captions.ai](https://captions.ai)
      - [https://gamma.app](https://gamma.app)
      - [https://tome.app](https://tome.app)
      - [https://relume.io](https://relume.io)
      - [https://framer.com](https://framer.com)
      - [https://durable.co](https://durable.co)
      - [https://looka.com](https://looka.com)
      - [https://namelix.com](https://namelix.com)
      - [https://copy.ai](https://copy.ai)
      - [https://jasper.ai](https://jasper.ai)
      - [https://surferseo.com](https://surferseo.com)
      - [https://semrush.com](https://semrush.com)

---