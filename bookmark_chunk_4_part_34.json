[{"id": "1702272855801741606", "created_at": "2023-09-14 13:47:30 +03:00", "full_text": "Step by Step guide DSA in 100 days with resources :\n\nDay ♾️to 0:\n\n- Stick  to a programming language like C/C++ or Java. \n- Make sure you're comfortable with pointers/objects.\n\nDay 1:\n\n- Understanding the concept of algorithmic complexity . \n- Skip the theory for now, but for every piece of code you write, you should be able to derive both time and space complexity.\n\nDay 2-10 :\n\nStart with some simple data structures,\n- Arrays\n- Linked List\n- Strings \n- Stacks \n- Queues\n\nUnderstand their basic operations ( insert, delete, search, traversal) and their complexity - Big O Algorithm Complexity Cheat sheet, and code them all.\n\nDay 11-25 :\n\nNow learn some simple algorithms :\n- Sorting \n- Search\n- Prime Numbers\n- Strings\n- Miscellaneous\n\nResources:\nFor this you look to <PERSON>'s youtube channel. He is simply brilliant at explaining algorithms. Watch his algorithm playlist in order\n\nDay 26-50 :\n\nOnce you are comfortable with everything above, start doing problems from\n- Leetcode\n- GeeksforGeeks\n- HackerRank\n- Codeforces\n- InterviewBit\n- Cracking the coding interview\n- Elements of programming Interviews\n\nStick to chapters of arrays, linked list, strings, stacks, queues, and complexity.\n\nDay 51-60 :\n\nLearn some non-linear data structures now\n\n- Tree\n      ~ Binary Tree, Binary Search Tree\n      ~ Heaps\n- Hash Table\n- Graph\n\nDay 61-90 :\n\nRefer to the previous resources and start doing problems from trees, hash tables, heaps and graphs.\n\nDay 91-100:\n\nUnderstand:\n- Computational complexity theory\n- and NP- completeness\n- Knapsack problem\n- Travelling Salesman problem\n- SAT problem\n\nDay 101-♾️:\n\nYou are now better than most of the CS undergrads.\n\nKeep revising the previous topics and start competitive programming! Good luck!", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 797, "retweet_count": 153, "bookmark_count": 1488, "quote_count": 3, "reply_count": 20, "views_count": 131753, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1702272855801741606", "metadata": {"rest_id": "1702272855801741606", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDkyMzY0OTQwMTkxMzQ2Njky", "rest_id": "1492364940191346692", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1908246792589729792/Ez81z59T_normal.jpg"}, "core": {"created_at": "Sat Feb 12 05:08:49 +0000 2022", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "21 | Building @codeyourcareer_ | \nReach out: https://t.co/mFRdOCNzie", "entities": {"description": {"urls": [{"display_url": "linktr.ee/ainasanghi", "expanded_url": "https://linktr.ee/ainasanghi", "url": "https://t.co/mFRdOCNzie", "indices": [45, 68]}]}, "url": {"urls": [{"display_url": "code-your-career.beehiiv.com/subscribe", "expanded_url": "https://code-your-career.beehiiv.com/subscribe", "url": "https://t.co/jqm5SbdN8T", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 3079, "followers_count": 16326, "friends_count": 215, "has_custom_timelines": false, "is_translator": false, "listed_count": 109, "media_count": 406, "normal_followers_count": 16326, "pinned_tweet_ids_str": ["1773354278276432362"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1492364940191346692/1720082058", "profile_interstitial_type": "", "statuses_count": 4784, "translator_type": "none", "url": "https://t.co/jqm5SbdN8T", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "subscribe:"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1660908826219139073", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1702272855801741606"], "editable_until_msecs": "1694692050000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "131753", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3MDIyNzI4NTU2NjMzODY2MjQ=", "text": "Step by Step guide DSA in 100 days with resources :\n\nDay ♾️to 0:\n\n- Stick  to a programming language like C/C++ or Java. \n- Make sure you're comfortable with pointers/objects.\n\nDay 1:\n\n- Understanding the concept of algorithmic complexity . \n- Skip the theory for now, but for every piece of code you write, you should be able to derive both time and space complexity.\n\nDay 2-10 :\n\nStart with some simple data structures,\n- Arrays\n- Linked List\n- Strings \n- Stacks \n- Queues\n\nUnderstand their basic operations ( insert, delete, search, traversal) and their complexity - Big O Algorithm Complexity Cheat sheet, and code them all.\n\nDay 11-25 :\n\nNow learn some simple algorithms :\n- Sorting \n- Search\n- Prime Numbers\n- Strings\n- Miscellaneous\n\nResources:\nFor this you look to <PERSON>'s youtube channel. He is simply brilliant at explaining algorithms. Watch his algorithm playlist in order\n\nDay 26-50 :\n\nOnce you are comfortable with everything above, start doing problems from\n- Leetcode\n- GeeksforGeeks\n- HackerRank\n- Codeforces\n- InterviewBit\n- Cracking the coding interview\n- Elements of programming Interviews\n\nStick to chapters of arrays, linked list, strings, stacks, queues, and complexity.\n\nDay 51-60 :\n\nLearn some non-linear data structures now\n\n- Tree\n      ~ Binary Tree, Binary Search Tree\n      ~ Heaps\n- Hash Table\n- Graph\n\nDay 61-90 :\n\nRefer to the previous resources and start doing problems from trees, hash tables, heaps and graphs.\n\nDay 91-100:\n\nUnderstand:\n- Computational complexity theory\n- and NP- completeness\n- Knapsack problem\n- Travelling Salesman problem\n- SAT problem\n\nDay 101-♾️:\n\nYou are now better than most of the CS undergrads.\n\nKeep revising the previous topics and start competitive programming! Good luck!", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 53, "to_index": 64, "richtext_types": ["Bold"]}, {"from_index": 177, "to_index": 183, "richtext_types": ["Bold"]}, {"from_index": 216, "to_index": 241, "richtext_types": ["Italic"]}, {"from_index": 370, "to_index": 380, "richtext_types": ["Bold"]}, {"from_index": 570, "to_index": 609, "richtext_types": ["Italic"]}, {"from_index": 630, "to_index": 641, "richtext_types": ["Bold"]}, {"from_index": 741, "to_index": 751, "richtext_types": ["Italic"]}, {"from_index": 891, "to_index": 902, "richtext_types": ["Bold"]}, {"from_index": 1200, "to_index": 1211, "richtext_types": ["Bold"]}, {"from_index": 1339, "to_index": 1350, "richtext_types": ["Bold"]}, {"from_index": 1453, "to_index": 1464, "richtext_types": ["Bold"]}, {"from_index": 1599, "to_index": 1610, "richtext_types": ["Bold"]}, {"from_index": 1664, "to_index": 1677, "richtext_types": ["Bold"]}, {"from_index": 1733, "to_index": 1743, "richtext_types": ["Italic"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 1488, "bookmarked": true, "created_at": "Thu Sep 14 10:47:30 +0000 2023", "conversation_control": {"policy": "ByInvitation", "conversation_owner_results": {"result": {"__typename": "User", "core": {"screen_name": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "conversation_id_str": "1702272855801741606", "display_text_range": [0, 276], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 797, "favorited": false, "full_text": "Step by Step guide DSA in 100 days with resources :\n\nDay ♾️to 0:\n\n- Stick  to a programming language like C/C++ or Java. \n- Make sure you're comfortable with pointers/objects.\n\nDay 1:\n\n- Understanding the concept of algorithmic complexity . \n- Skip the theory for now, but for", "is_quote_status": false, "lang": "en", "quote_count": 3, "reply_count": 20, "retweet_count": 153, "retweeted": false, "user_id_str": "1492364940191346692", "id_str": "1702272855801741606"}, "twe_private_fields": {"created_at": 1694688450000, "updated_at": 1748554188923, "media_count": 0}}}, {"id": "1703556207573946860", "created_at": "2023-09-18 02:47:05 +03:00", "full_text": "@TansuYegen It’s all about the vinegar.\n\nAnd something called “dwell time”.\nVinegar is a pretty strong acid but it needs some dwell time to eat into where you put it.\nThe soap and tissue paper holds it in place so it does not immediately run off.\n\n(I run cleaning companies)", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1703430593961189759", "retweeted_status": null, "quoted_status": null, "favorite_count": 884, "retweet_count": 47, "bookmark_count": 127, "quote_count": 0, "reply_count": 13, "views_count": 115875, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1703556207573946860", "metadata": {"__typename": "Tweet", "rest_id": "1703556207573946860", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTczNDUxMTk1MzgzMjg3ODA4", "rest_id": "1173451195383287808", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1607644414901616642/62YF9SoP_normal.jpg"}, "core": {"created_at": "Mon Sep 16 04:19:32 +0000 2019", "name": "<PERSON><PERSON>", "screen_name": "ericson4smith"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "<PERSON>. <PERSON>eloper. Explorer. Living in Asia. \nGet off the cloud with: https://t.co/m1p9O8eXmL\n 🇺🇸 🇯🇲 🇹🇭 🇻🇳", "entities": {"description": {"urls": [{"display_url": "cloudtovps.com", "expanded_url": "https://www.cloudtovps.com/", "url": "https://t.co/m1p9O8eXmL", "indices": [70, 93]}]}, "url": {"urls": [{"display_url": "biznitos.com", "expanded_url": "https://www.biznitos.com", "url": "https://t.co/2Tf34CjPmr", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 13841, "followers_count": 835, "friends_count": 368, "has_custom_timelines": true, "is_translator": false, "listed_count": 11, "media_count": 1111, "normal_followers_count": 835, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1173451195383287808/1718292660", "profile_interstitial_type": "", "statuses_count": 10093, "translator_type": "none", "url": "https://t.co/2Tf34CjPmr", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Bangkok, Thailand"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1607644112978866177", "professional_type": "Business", "category": [{"id": 958, "name": "Entrepreneur", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1703556207573946860"], "editable_until_msecs": "1694998025000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "115875", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 127, "bookmarked": true, "created_at": "Sun Sep 17 23:47:05 +0000 2023", "conversation_id_str": "1703430593961189759", "display_text_range": [12, 274], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "94543804", "name": "<PERSON><PERSON>", "screen_name": "TansuYegen", "indices": [0, 11]}]}, "favorite_count": 884, "favorited": false, "full_text": "@TansuYegen It’s all about the vinegar.\n\nAnd something called “dwell time”.\nVinegar is a pretty strong acid but it needs some dwell time to eat into where you put it.\nThe soap and tissue paper holds it in place so it does not immediately run off.\n\n(I run cleaning companies)", "in_reply_to_screen_name": "TansuYegen", "in_reply_to_status_id_str": "1703430593961189759", "in_reply_to_user_id_str": "94543804", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 13, "retweet_count": 47, "retweeted": false, "user_id_str": "1173451195383287808", "id_str": "1703556207573946860"}, "twe_private_fields": {"created_at": 1694994425000, "updated_at": 1748554188923, "media_count": 0}}}]