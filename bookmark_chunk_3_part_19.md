# Bookmarks from bookmark_chunk极_part_19.json

## Category: Education / Technology / Learning Path / Web Development / Cloud Computing / UI-UX / Career Development

*   **Author:** apt<PERSON><PERSON><PERSON> (@aptlearn_io)
    **Date:** 2022-06-08 12:25:05 +03:00
    **Content:** Frontend: HTML, CSS, JavaScript, React

    Backend: Python, Node.Js, Ruby, Java, PHP

    Database: Mysql, MongoDB, Oracle, PostgreSQL

    Cloud: AWS, Azure, Google Cloud, Digital Ocean

    UI/UX: Figma, Adobe XD, User Experience 

    Which of these do you wish to learn? Follow us let’s help
    **URL:** [https://twitter.com/aptlearn_io/status/1534466535770705921](https://twitter.com/aptlearn_io/status/153446653577极5921)

---

## Category: Education / Programming / Web Development / Learning Plan / Roadmap / HTML / CSS / JavaScript / DOM / Project-Based Learning

*   **Author:** <PERSON><PERSON><PERSON> (@Prathkum)
    **Date:** 2022-06-08 21:14:16 +03:00
    **Content:** Divide your 100 days of code effectively:

    • HTML (1 - 5 days)
    • CSS (6 - 30 days)
    • JavaScript (31 - 80 days)
    • DOM (81- 100 days)

    Be sure to build projects. This will help you understand better. 
    **URL:** [https://twitter.com/Prathkum/status/1534599708806504448](https://twitter.com/Prathkum/status/1534599708806504448)

---
