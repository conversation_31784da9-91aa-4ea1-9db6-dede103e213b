[{"id": "1707962461159149880", "created_at": "2023-09-30 06:35:57 +03:00", "full_text": "800 free #ComputerScience classes you can take online right now, most have video lectures: https://t.co/gI4ckSenph\n—\n#BigData #DataScience #AI #ArtificialIntelligence #MachineLearning #DeepLearning #ComputerVision #Robotics #QuantumComputing #WebDevelopment #Databases #Algorithms https://t.co/soZV58F1xp", "media": [{"type": "photo", "url": "https://t.co/soZV58F1xp", "thumbnail": "https://pbs.twimg.com/media/F7PmznJX0AAX4DF?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/F7PmznJX0AAX4DF?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1059, "retweet_count": 360, "bookmark_count": 1272, "quote_count": 6, "reply_count": 10, "views_count": 206706, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1707962461159149880", "metadata": {"__typename": "Tweet", "rest_id": "1707962461159149880", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1MzQ1NjM5NzY=", "rest_id": "534563976", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1112733580948635648/s-8d1avb_normal.jpg"}, "core": {"created_at": "Fri Mar 23 16:35:17 +0000 2012", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Advisor to startups. Freelancer. Founder of @LeadershipData. Global Speaker. Top influencer #BigData #DataScience #AI #IoT #ML #B2B. PhD Astrophysics @Caltech", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linkedin.com/in/kirkdborne", "expanded_url": "http://www.linkedin.com/in/kirkdborne", "url": "https://t.co/g46xALu4Eu", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 238745, "followers_count": 463503, "friends_count": 5532, "has_custom_timelines": true, "is_translator": false, "listed_count": 10132, "media_count": 94041, "normal_followers_count": 463503, "pinned_tweet_ids_str": ["1913816024513732984"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/534563976/1706648943", "profile_interstitial_type": "", "statuses_count": 184820, "translator_type": "none", "url": "https://t.co/g46xALu4Eu", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Maryland, USA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1494394757388378113", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************", "venmo_handle": "kirk-borne"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"initial_tweet_id": "1707962357027107000", "edit_control_initial": {"edit_tweet_ids": ["1707962357027107000", "1707962461159149880"], "editable_until_msecs": "1696048532000", "is_edit_eligible": true, "edits_remaining": "4"}}, "previous_counts": {"bookmark_count": 0, "favorite_count": 0, "quote_count": 0, "reply_count": 0, "retweet_count": 0}, "is_translatable": false, "views": {"count": "206706", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1272, "bookmarked": true, "created_at": "Sat Sep 30 03:35:57 +0000 2023", "conversation_id_str": "1707962461159149880", "display_text_range": [0, 280], "entities": {"hashtags": [{"indices": [9, 25], "text": "ComputerScience"}, {"indices": [117, 125], "text": "BigData"}, {"indices": [126, 138], "text": "DataScience"}, {"indices": [139, 142], "text": "AI"}, {"indices": [143, 166], "text": "ArtificialIntelligence"}, {"indices": [167, 183], "text": "MachineLearning"}, {"indices": [184, 197], "text": "DeepLearning"}, {"indices": [198, 213], "text": "ComputerVision"}, {"indices": [214, 223], "text": "Robotics"}, {"indices": [224, 241], "text": "QuantumComputing"}, {"indices": [242, 257], "text": "WebDevelopment"}, {"indices": [258, 268], "text": "Databases"}, {"indices": [269, 280], "text": "Algorithms"}], "media": [{"display_url": "pic.x.com/soZV58F1xp", "expanded_url": "https://x.com/KirkDBorne/status/1707962461159149880/photo/1", "id_str": "1707962458059558912", "indices": [281, 304], "media_key": "3_1707962458059558912", "media_url_https": "https://pbs.twimg.com/media/F7PmznJX0AAX4DF.jpg", "type": "photo", "url": "https://t.co/soZV58F1xp", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1265, "w": 950, "resize": "fit"}, "medium": {"h": 1200, "w": 901, "resize": "fit"}, "small": {"h": 680, "w": 511, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1265, "width": 950, "focus_rects": [{"x": 0, "y": 208, "w": 950, "h": 532}, {"x": 0, "y": 0, "w": 950, "h": 950}, {"x": 0, "y": 0, "w": 950, "h": 1083}, {"x": 0, "y": 0, "w": 633, "h": 1265}, {"x": 0, "y": 0, "w": 950, "h": 1265}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1707962458059558912"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "bit.ly/3472Iia", "expanded_url": "http://bit.ly/3472Iia", "url": "https://t.co/gI4ckSenph", "indices": [91, 114]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/soZV58F1xp", "expanded_url": "https://x.com/KirkDBorne/status/1707962461159149880/photo/1", "id_str": "1707962458059558912", "indices": [281, 304], "media_key": "3_1707962458059558912", "media_url_https": "https://pbs.twimg.com/media/F7PmznJX0AAX4DF.jpg", "type": "photo", "url": "https://t.co/soZV58F1xp", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1265, "w": 950, "resize": "fit"}, "medium": {"h": 1200, "w": 901, "resize": "fit"}, "small": {"h": 680, "w": 511, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1265, "width": 950, "focus_rects": [{"x": 0, "y": 208, "w": 950, "h": 532}, {"x": 0, "y": 0, "w": 950, "h": 950}, {"x": 0, "y": 0, "w": 950, "h": 1083}, {"x": 0, "y": 0, "w": 633, "h": 1265}, {"x": 0, "y": 0, "w": 950, "h": 1265}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1707962458059558912"}}}]}, "favorite_count": 1059, "favorited": false, "full_text": "800 free #ComputerScience classes you can take online right now, most have video lectures: https://t.co/gI4ckSenph\n—\n#BigData #DataScience #AI #ArtificialIntelligence #MachineLearning #DeepLearning #ComputerVision #Robotics #QuantumComputing #WebDevelopment #Databases #Algorithms https://t.co/soZV58F1xp", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 10, "retweet_count": 360, "retweeted": false, "user_id_str": "534563976", "id_str": "1707962461159149880"}, "twe_private_fields": {"created_at": 1696044957000, "updated_at": 1748554186109, "media_count": 1}}}, {"id": "1708475354711535933", "created_at": "2023-10-01 16:34:01 +03:00", "full_text": "5 super dirty datasets for you to practice your data cleaning on using either Excel, Power Query, SQL, Python or R.\n\nKindly like and retweet.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1760, "retweet_count": 628, "bookmark_count": 2896, "quote_count": 7, "reply_count": 25, "views_count": 189041, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1708475354711535933", "metadata": {"__typename": "Tweet", "rest_id": "1708475354711535933", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxOTkzNTY1MDg=", "rest_id": "199356508", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1916869204696793088/FpFRNqvJ_normal.jpg"}, "core": {"created_at": "Wed Oct 06 17:18:00 +0000 2010", "name": "<PERSON><PERSON>", "screen_name": "thenaijacarguy"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Data Analyst - Session Lead @Udacity, @EducativeInc @Trainer_Central @lighthall_co | 👉🏽 https://t.co/DCEMPo3TkZ", "entities": {"description": {"urls": [{"display_url": "datafrik.co/bootcamp/begin…", "expanded_url": "https://www.datafrik.co/bootcamp/beginner-data-analytics/?source=main", "url": "https://t.co/DCEMPo3TkZ", "indices": [88, 111]}]}, "url": {"urls": [{"display_url": "thenaijacarguy.github.io", "expanded_url": "http://thenaijacarguy.github.io", "url": "https://t.co/TBwOAeOF6G", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10722, "followers_count": 56632, "friends_count": 946, "has_custom_timelines": true, "is_translator": false, "listed_count": 273, "media_count": 1222, "normal_followers_count": 56632, "pinned_tweet_ids_str": ["1882767050411745464"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/199356508/1728299292", "profile_interstitial_type": "", "statuses_count": 12678, "translator_type": "none", "url": "https://t.co/TBwOAeOF6G", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Home"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1522626802014986241", "professional_type": "Creator", "category": [{"id": 144, "name": "Education", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1708475354711535933"], "editable_until_msecs": "1696170841000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "189041", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2896, "bookmarked": true, "created_at": "Sun Oct 01 13:34:01 +0000 2023", "conversation_id_str": "1708475354711535933", "display_text_range": [0, 141], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1760, "favorited": false, "full_text": "5 super dirty datasets for you to practice your data cleaning on using either Excel, Power Query, SQL, Python or R.\n\nKindly like and retweet.", "is_quote_status": false, "lang": "en", "quote_count": 7, "reply_count": 25, "retweet_count": 628, "retweeted": false, "user_id_str": "199356508", "id_str": "1708475354711535933"}, "twe_private_fields": {"created_at": 1696167241000, "updated_at": 1748554186108, "media_count": 0}}}]