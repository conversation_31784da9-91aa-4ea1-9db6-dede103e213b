# Bookmark Chunk 5 Part 05

## Tweet 1

**Author:** @7etsuo
**Tweet URL:** [https://twitter.com/7etsuo/status/1847951330809352463](https://twitter.com/7etsuo/status/1847951330809352463)
**Timestamp:** 2024-10-20 13:41:45 +03:00
**Category:** Technology / Computer Science / Algorithms / Data Structures / Hashing / Education / Video

**Content:**
<PERSON>, MIT, explains how hashing maps keys to slots in a table and the issue of collisions when multiple keys land in the same slot.

Source: Introduction to Algorithms, MIT 6.046J. https://t.co/5nT9Umud3m

**Media:**
- Type: video
- URL: https://t.co/5nT9Umud3m

---

## Tweet 2

**Author:** @Alex_TheAnalyst
**Tweet URL:** [https://twitter.com/Alex_TheAnalyst/status/1848003111800070239](https://twitter.com/Alex_TheAnalyst/status/1848003111800070239)
**Timestamp:** 2024-10-20 17:07:31 +03:00
**Category:** Technology / Data Analysis / Career / Portfolio / Freebie / Link

**Content:**
I'm giving away my Data Analyst Portfolio Starter Pack for FREE!

It includes:
- 2 Portfolio Project Datasets (with questions and project walkthroughs)
- Portfolio Website Template (HTML and CSS)
- Resume Template

Link in bio to download for free!

#data #dataanalytics #portfolio #excel #sql #python #tableau #powerbi https://t.co/2jY8X7tq8F

---