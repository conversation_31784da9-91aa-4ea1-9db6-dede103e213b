[{"id": "1561805920115752960", "created_at": "2022-08-22 23:02:02 +03:00", "full_text": "Data Structure Cheatsheet\n\n #Python #MachineLearning #DataScience #BigData #Analytics #AI #JavaScript #ReactJS #CloudComputing #Serverless #DataScientist #Linux #Programming #Coding #100DaysofCode #NLP #webdevelopment #100DaysOfCode  #ArtificialIntelligence #Python3 #pythoncode https://t.co/sSJjN7iWMl", "media": [{"type": "photo", "url": "https://t.co/sSJjN7iWMl", "thumbnail": "https://pbs.twimg.com/media/FaymLDcagAABns1?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FaymLDcagAABns1?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 127, "retweet_count": 70, "bookmark_count": 29, "quote_count": 2, "reply_count": 3, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1561805920115752960", "metadata": {"__typename": "Tweet", "rest_id": "1561805920115752960", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDYzNTI5MjE3MzcxNDc1OTY4", "rest_id": "1463529217371475968", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1548385632262578176/tVEDTt_7_normal.jpg"}, "core": {"created_at": "Wed Nov 24 15:25:45 +0000 2021", "name": "Manish", "screen_name": "<PERSON><PERSON>_kumar3_1"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Learning in public ,learning for public\n𝐜𝐥𝐞𝐚𝐫𝐞𝐝 𝐠𝐨𝐨𝐠𝐥𝐞 𝐡𝐚𝐬𝐡𝐜𝐨𝐝𝐞  𝐟𝐢𝐫𝐬𝐭 𝐫𝐨𝐮𝐧𝐝", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 827, "followers_count": 1602, "friends_count": 49, "has_custom_timelines": false, "is_translator": false, "listed_count": 25, "media_count": 597, "normal_followers_count": 1602, "pinned_tweet_ids_str": ["1563730756740222976"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1463529217371475968/1657998965", "profile_interstitial_type": "", "statuses_count": 840, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1560913085589700608", "professional_type": "Creator", "category": [{"id": 1009, "name": "Community", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1561805920115752960"], "editable_until_msecs": "1661200322000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 29, "bookmarked": true, "created_at": "Mon Aug 22 20:02:02 +0000 2022", "conversation_id_str": "1561805920115752960", "display_text_range": [0, 278], "entities": {"hashtags": [{"indices": [28, 35], "text": "Python"}, {"indices": [36, 52], "text": "MachineLearning"}, {"indices": [53, 65], "text": "DataScience"}, {"indices": [66, 74], "text": "BigData"}, {"indices": [75, 85], "text": "Analytics"}, {"indices": [86, 89], "text": "AI"}, {"indices": [90, 101], "text": "JavaScript"}, {"indices": [102, 110], "text": "ReactJS"}, {"indices": [111, 126], "text": "CloudComputing"}, {"indices": [127, 138], "text": "Serverless"}, {"indices": [139, 153], "text": "DataScientist"}, {"indices": [154, 160], "text": "Linux"}, {"indices": [161, 173], "text": "Programming"}, {"indices": [174, 181], "text": "Coding"}, {"indices": [182, 196], "text": "100DaysofCode"}, {"indices": [197, 201], "text": "NLP"}, {"indices": [202, 217], "text": "webdevelopment"}, {"indices": [218, 232], "text": "100DaysOfCode"}, {"indices": [234, 257], "text": "ArtificialIntelligence"}, {"indices": [258, 266], "text": "Python3"}, {"indices": [267, 278], "text": "pythoncode"}], "media": [{"display_url": "pic.x.com/sSJjN7iWMl", "expanded_url": "https://x.com/Manish_kumar3_1/status/1561805920115752960/photo/1", "id_str": "1561805879623974912", "indices": [279, 302], "media_key": "3_1561805879623974912", "media_url_https": "https://pbs.twimg.com/media/FaymLDcagAABns1.jpg", "type": "photo", "url": "https://t.co/sSJjN7iWMl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 596, "y": 142, "h": 84, "w": 84}]}, "medium": {"faces": [{"x": 596, "y": 142, "h": 84, "w": 84}]}, "small": {"faces": [{"x": 450, "y": 107, "h": 63, "w": 63}]}, "orig": {"faces": [{"x": 596, "y": 142, "h": 84, "w": 84}]}}, "sizes": {"large": {"h": 900, "w": 696, "resize": "fit"}, "medium": {"h": 900, "w": 696, "resize": "fit"}, "small": {"h": 680, "w": 526, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 900, "width": 696, "focus_rects": [{"x": 0, "y": 0, "w": 696, "h": 390}, {"x": 0, "y": 0, "w": 696, "h": 696}, {"x": 0, "y": 0, "w": 696, "h": 793}, {"x": 0, "y": 0, "w": 450, "h": 900}, {"x": 0, "y": 0, "w": 696, "h": 900}]}, "media_results": {"result": {"media_key": "3_1561805879623974912"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/sSJjN7iWMl", "expanded_url": "https://x.com/Manish_kumar3_1/status/1561805920115752960/photo/1", "id_str": "1561805879623974912", "indices": [279, 302], "media_key": "3_1561805879623974912", "media_url_https": "https://pbs.twimg.com/media/FaymLDcagAABns1.jpg", "type": "photo", "url": "https://t.co/sSJjN7iWMl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 596, "y": 142, "h": 84, "w": 84}]}, "medium": {"faces": [{"x": 596, "y": 142, "h": 84, "w": 84}]}, "small": {"faces": [{"x": 450, "y": 107, "h": 63, "w": 63}]}, "orig": {"faces": [{"x": 596, "y": 142, "h": 84, "w": 84}]}}, "sizes": {"large": {"h": 900, "w": 696, "resize": "fit"}, "medium": {"h": 900, "w": 696, "resize": "fit"}, "small": {"h": 680, "w": 526, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 900, "width": 696, "focus_rects": [{"x": 0, "y": 0, "w": 696, "h": 390}, {"x": 0, "y": 0, "w": 696, "h": 696}, {"x": 0, "y": 0, "w": 696, "h": 793}, {"x": 0, "y": 0, "w": 450, "h": 900}, {"x": 0, "y": 0, "w": 696, "h": 900}]}, "media_results": {"result": {"media_key": "3_1561805879623974912"}}}]}, "favorite_count": 127, "favorited": false, "full_text": "Data Structure Cheatsheet\n\n #Python #MachineLearning #DataScience #BigData #Analytics #AI #JavaScript #ReactJS #CloudComputing #Serverless #DataScientist #Linux #Programming #Coding #100DaysofCode #NLP #webdevelopment #100DaysOfCode  #ArtificialIntelligence #Python3 #pythoncode https://t.co/sSJjN7iWMl", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 3, "retweet_count": 70, "retweeted": false, "user_id_str": "1463529217371475968", "id_str": "1561805920115752960"}, "twe_private_fields": {"created_at": 1661198522000, "updated_at": 1748554247987, "media_count": 1}}}, {"id": "1561930897506635778", "created_at": "2022-08-23 07:18:39 +03:00", "full_text": "Learn For Free 👇🏻:\n\nHTML ➡️ Codecademy\nCSS ➡️ FreeCodeCamp\nGithub ➡️ lab.github\nGit ➡️Atlassian\nJavascript ➡️ FreeCodeCamp\nUI/UX ➡️ Hackdesign\nHosting ➡️ Wpbeginner\nSQL ➡️ Sqlbolt\nAPI ➡️ FreeCodeCamp\nReact ➡️ Scrimba\nTailwind ➡️ Scrimba\nNode ➡️ Skillup", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 8275, "retweet_count": 2041, "bookmark_count": 5436, "quote_count": 34, "reply_count": 118, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1561930897506635778", "metadata": {"__typename": "Tweet", "rest_id": "1561930897506635778", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNDgyNjg2MDY3", "rest_id": "2482686067", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1810304146475470848/vdARHoRN_normal.jpg"}, "core": {"created_at": "Wed May 07 19:48:53 +0000 2014", "name": "• nanou •", "screen_name": "NanouuSymeon"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "🎮 || Game Team Lead @vaslabs \n🍂 || Coding Tutor & Content Creator\n🦢 || For Collaboration: <EMAIL>", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "beacons.ai/nanouusymeon", "expanded_url": "https://beacons.ai/nanouusymeon", "url": "https://t.co/iYRVMAuhkW", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 86638, "followers_count": 128655, "friends_count": 1272, "has_custom_timelines": true, "is_translator": false, "listed_count": 774, "media_count": 6523, "normal_followers_count": 128655, "pinned_tweet_ids_str": ["1928166290058813798"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2482686067/1736353312", "profile_interstitial_type": "", "statuses_count": 37379, "translator_type": "none", "url": "https://t.co/iYRVMAuhkW", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Office Items Links ⬇️"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1590612328801771520", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1561930897506635778"], "editable_until_msecs": "1661230119000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5436, "bookmarked": true, "created_at": "<PERSON><PERSON> Aug 23 04:18:39 +0000 2022", "conversation_id_str": "1561930897506635778", "display_text_range": [0, 252], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 8275, "favorited": false, "full_text": "Learn For Free 👇🏻:\n\nHTML ➡️ Codecademy\nCSS ➡️ FreeCodeCamp\nGithub ➡️ lab.github\nGit ➡️Atlassian\nJavascript ➡️ FreeCodeCamp\nUI/UX ➡️ Hackdesign\nHosting ➡️ Wpbeginner\nSQL ➡️ Sqlbolt\nAPI ➡️ FreeCodeCamp\nReact ➡️ Scrimba\nTailwind ➡️ Scrimba\nNode ➡️ Skillup", "is_quote_status": false, "lang": "en", "quote_count": 34, "reply_count": 118, "retweet_count": 2041, "retweeted": false, "user_id_str": "2482686067", "id_str": "1561930897506635778"}, "twe_private_fields": {"created_at": 1661228319000, "updated_at": 1748554247988, "media_count": 0}}}]