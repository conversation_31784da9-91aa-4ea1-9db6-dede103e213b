# Bookmark Chunk 4 Part 88

## Tweet 1

**Author:** @quantscience_
**Tweet URL:** [https://twitter.com/quantscience_/status/1840364328978895114](https://twitter.com/quantscience_/status/1840364328978895114)
**Timestamp:** 2024-09-29 15:13:43 +03:00
**Category:** Finance / Algorithmic Trading / Python / Open Source / Tools

**Content:**
The math behind the Quant Scientist Stack:

• SciPy: $0
• Zipline: $0
• Python: $0
• NumPy: $0
• PyFolio: $0
• pandas : $0
• OpenBB: $0
• Empyrical: $0
• AlphaLens: $0
• Statsmodels: $0
• RiskFolio-Lib: $0

You can start algorithmic trading for free. https://t.co/CjJ6fv0ZcG

---

## Tweet 2

**Author:** @quantscience_
**Tweet URL:** [https://twitter.com/quantscience_/status/1840420451358507089](https://twitter.com/quantscience_/status/1840420451358507089)
**Timestamp:** 2024-09-29 18:56:43 +03:00
**Category:** Finance / Trading / Tools / Open Source

**Content:**
OpenBB: A free alternative to the $20,000 Bloomberg Terminal

Available 100% free on GitHub: https://t.co/rVf9riF9Tv

**External Links:**
- [OpenBB GitHub](https://github.com/OpenBB-finance/OpenBBTerminal)

---