[{"id": "1535234012792475657", "created_at": "2022-06-10 15:14:46 +03:00", "full_text": "The New York Times recently released an op-ed entitled The Greatest Life Hacks in the World (for Now).\n\nIt had some absolute gems of wisdom.\n\nHere were my favorites:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 8288, "retweet_count": 1668, "bookmark_count": 5516, "quote_count": 72, "reply_count": 116, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1535234012792475657", "metadata": {"__typename": "Tweet", "rest_id": "1535234012792475657", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTI2ODE5NTM=", "rest_id": "312681953", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1586859332104343552/V1HRpbP1_normal.jpg"}, "core": {"created_at": "<PERSON>e Jun 07 14:14:17 +0000 2011", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "NYT Bestselling Author of The 5 Types of Wealth. Gave up a grand slam on ESPN in 2012 and still waiting for it to land. Order my book below 👇", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "the5typesofwealth.com", "expanded_url": "https://www.the5typesofwealth.com", "url": "https://t.co/exGtZduEvq", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 151244, "followers_count": 1080264, "friends_count": 278, "has_custom_timelines": true, "is_translator": false, "listed_count": 16659, "media_count": 4870, "normal_followers_count": 1080264, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/312681953/1739539616", "profile_interstitial_type": "", "statuses_count": 69302, "translator_type": "none", "url": "https://t.co/exGtZduEvq", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "New York, USA"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1675186015294611456", "professional_type": "Business", "category": [{"id": 957, "name": "Author", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "<PERSON><PERSON><PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1535234012792475657"], "editable_until_msecs": "1654865086000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://itunes.apple.com/us/app/twitter/id409789998?mt=12\" rel=\"nofollow\">Twitter for Mac</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5516, "bookmarked": true, "created_at": "Fri Jun 10 12:14:46 +0000 2022", "conversation_id_str": "1535234012792475657", "display_text_range": [0, 165], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 8288, "favorited": false, "full_text": "The New York Times recently released an op-ed entitled The Greatest Life Hacks in the World (for Now).\n\nIt had some absolute gems of wisdom.\n\nHere were my favorites:", "is_quote_status": false, "lang": "en", "quote_count": 72, "reply_count": 116, "retweet_count": 1668, "retweeted": false, "user_id_str": "312681953", "id_str": "1535234012792475657"}, "twe_private_fields": {"created_at": 1654863286000, "updated_at": 1748554267519, "media_count": 0}}}, {"id": "1535734057899786240", "created_at": "2022-06-12 00:21:46 +03:00", "full_text": "#FPL \nJust noticed that @vaastav05's FPL repository is currently at 999 stars. It's an amazing resource that has data all the way from 2016-17 season. Player prices, points, stats, fixtures, transfer counts, etc... Certainly something you need to bookmark!\nhttps://t.co/yIJjoS4eG8", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 53, "retweet_count": 4, "bookmark_count": 16, "quote_count": 0, "reply_count": 4, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1535734057899786240", "metadata": {"__typename": "Tweet", "rest_id": "1535734057899786240", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDA1NzIwNw==", "rest_id": "14057207", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1450481583887159296/nfHjQYIt_normal.jpg"}, "core": {"created_at": "Thu Feb 28 21:25:54 +0000 2008", "name": "Sertalp B. Çay", "screen_name": "sertalpbilal"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Co-founder and Chair of @SolioAnalytics | \nOR Specialist @SASSoftware | \nBluesky 🦋https://t.co/kByWdrMaOy | \nhttps://t.co/myqnJA4YmO", "entities": {"description": {"urls": [{"display_url": "bsky.app/profile/sertal…", "expanded_url": "https://bsky.app/profile/sertalpbilal.com", "url": "https://t.co/kByWdrMaOy", "indices": [82, 105]}, {"display_url": "fploptimized.com", "expanded_url": "http://fploptimized.com", "url": "https://t.co/myqnJA4YmO", "indices": [109, 132]}]}, "url": {"urls": [{"display_url": "sertalpbilal.com", "expanded_url": "https://sertalpbilal.com", "url": "https://t.co/bRduWIgW9e", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 24688, "followers_count": 20453, "friends_count": 1378, "has_custom_timelines": false, "is_translator": false, "listed_count": 548, "media_count": 2946, "normal_followers_count": 20453, "pinned_tweet_ids_str": ["1886080711788056608"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/14057207/1648046431", "profile_interstitial_type": "", "statuses_count": 16238, "translator_type": "none", "url": "https://t.co/bRduWIgW9e", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "North Carolina, USA"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/yIJjoS4eG8", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 200, "width": 400, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Creates a .csv file of all players in the English Player League with their respective team and total fantasy points - vaastav/Fantasy-Premier-League", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 600, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=600x600"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 72, "width": 144, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "Creates a .csv file of all players in the English Player League with their respective team and total fantasy points - vaastav/Fantasy-Premier-League", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "Creates a .csv file of all players in the English Player League with their respective team and total fantasy points - vaastav/Fantasy-Premier-League", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 90.71}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 3.17}, {"rgb": {"blue": 8, "green": 5, "red": 3}, "percentage": 1.68}, {"rgb": {"blue": 62, "green": 112, "red": 173}, "percentage": 0.73}, {"rgb": {"blue": 192, "green": 204, "red": 221}, "percentage": 0.73}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "GitHub - vaastav/Fantasy-Premier-League: Creates a .csv file of all players in the English Player...", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 90.71}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 3.17}, {"rgb": {"blue": 8, "green": 5, "red": 3}, "percentage": 1.68}, {"rgb": {"blue": 62, "green": 112, "red": 173}, "percentage": 0.73}, {"rgb": {"blue": 192, "green": 204, "red": 221}, "percentage": 0.73}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 90.71}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 3.17}, {"rgb": {"blue": 8, "green": 5, "red": 3}, "percentage": 1.68}, {"rgb": {"blue": 62, "green": 112, "red": 173}, "percentage": 0.73}, {"rgb": {"blue": 192, "green": 204, "red": 221}, "percentage": 0.73}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/yIJjoS4eG8", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926563424932081664/fjQM6Jyb?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/yIJjoS4eG8", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620092, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17801, "media_count": 2646, "normal_followers_count": 2620092, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1535734057899786240"], "editable_until_msecs": "1654984306000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 16, "bookmarked": true, "created_at": "Sat Jun 11 21:21:46 +0000 2022", "conversation_id_str": "1535734057899786240", "display_text_range": [0, 280], "entities": {"hashtags": [{"indices": [0, 4], "text": "FPL"}], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/vaastav/Fantas…", "expanded_url": "https://github.com/vaastav/Fantasy-Premier-League", "url": "https://t.co/yIJjoS4eG8", "indices": [257, 280]}], "user_mentions": [{"id_str": "1720094096", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "vaastav05", "indices": [24, 34]}]}, "favorite_count": 53, "favorited": false, "full_text": "#FPL \nJust noticed that @vaastav05's FPL repository is currently at 999 stars. It's an amazing resource that has data all the way from 2016-17 season. Player prices, points, stats, fixtures, transfer counts, etc... Certainly something you need to bookmark!\nhttps://t.co/yIJjoS4eG8", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 4, "retweet_count": 4, "retweeted": false, "user_id_str": "14057207", "id_str": "1535734057899786240"}, "twe_private_fields": {"created_at": 1654982506000, "updated_at": 1748554267519, "media_count": 0}}}]