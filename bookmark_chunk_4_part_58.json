[{"id": "1786557732922728785", "created_at": "2024-05-04 03:45:30 +03:00", "full_text": "Given the absurdly unscientific and un-american ban of lab meat by @RonDeSantis yesterday (who clearly hasn’t done his homework)… here’s the ACTUAL science behind the technology: \n\nhttps://t.co/vmTcPVfCkO", "media": [{"type": "video", "url": "https://t.co/vmTcPVfCkO", "thumbnail": "https://pbs.twimg.com/amplify_video_thumb/1757863137636737025/img/8NO6hqjCrFCjF34m.jpg?name=thumb", "original": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/1280x720/q1inVpdSodWllNXJ.mp4?tag=14"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1584, "retweet_count": 218, "bookmark_count": 707, "quote_count": 104, "reply_count": 287, "views_count": 2948948, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1786557732922728785", "metadata": {"__typename": "Tweet", "rest_id": "1786557732922728785", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNTQyMjMwMg==", "rest_id": "25422302", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1786159247685754880/8pw2d-65_normal.jpg"}, "core": {"created_at": "Fri Mar 20 00:40:42 +0000 2009", "name": "<PERSON>", "screen_name": "<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Host of the Win-Win Podcast.  Slaying Moloch & Chasing Horizons 🚀 🌳🦾", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "winwinpodcast.com", "expanded_url": "http://winwinpodcast.com", "url": "https://t.co/ogHlEfLKkZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 26299, "followers_count": 265439, "friends_count": 542, "has_custom_timelines": true, "is_translator": false, "listed_count": 2531, "media_count": 2727, "normal_followers_count": 265439, "pinned_tweet_ids_str": ["1913297712453390447"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/25422302/1741722017", "profile_interstitial_type": "", "statuses_count": 23060, "translator_type": "none", "url": "https://t.co/ogHlEfLKkZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1679671864132767746", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1786557732922728785"], "editable_until_msecs": "1714787130000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "2948948", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 707, "bookmarked": true, "created_at": "Sat May 04 00:45:30 +0000 2024", "conversation_id_str": "1786557732922728785", "display_text_range": [0, 204], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/vmTcPVfCkO", "expanded_url": "https://x.com/Liv_Boeree/status/1757876855737033090/video/1", "id_str": "1757863137636737025", "indices": [181, 204], "media_key": "13_1757863137636737025", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1757863137636737025/img/8NO6hqjCrFCjF34m.jpg", "source_status_id_str": "1757876855737033090", "source_user_id_str": "25422302", "type": "video", "url": "https://t.co/vmTcPVfCkO", "additional_media_info": {"monetizable": false, "source_user": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNTQyMjMwMg==", "rest_id": "25422302", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1786159247685754880/8pw2d-65_normal.jpg"}, "core": {"created_at": "Fri Mar 20 00:40:42 +0000 2009", "name": "<PERSON>", "screen_name": "<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Host of the Win-Win Podcast.  Slaying Moloch & Chasing Horizons 🚀 🌳🦾", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "winwinpodcast.com", "expanded_url": "http://winwinpodcast.com", "url": "https://t.co/ogHlEfLKkZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 26299, "followers_count": 265439, "friends_count": 542, "has_custom_timelines": true, "is_translator": false, "listed_count": 2531, "media_count": 2727, "normal_followers_count": 265439, "pinned_tweet_ids_str": ["1913297712453390447"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/25422302/1741722017", "profile_interstitial_type": "", "statuses_count": 23060, "translator_type": "none", "url": "https://t.co/ogHlEfLKkZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1679671864132767746", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1080, "w": 1920, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 1920, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [16, 9], "duration_millis": 1495869, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1757863137636737025/pl/98WNPL_8N0bm0c12.m3u8?tag=14&v=94d"}, {"bitrate": 288000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/480x270/AceI7UR6ES-dAlk-.mp4?tag=14"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/640x360/llPd5CZYUaKIWpE_.mp4?tag=14"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/1280x720/q1inVpdSodWllNXJ.mp4?tag=14"}]}, "media_results": {"result": {"media_key": "13_1757863137636737025"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "487297085", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indices": [67, 79]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/vmTcPVfCkO", "expanded_url": "https://x.com/Liv_Boeree/status/1757876855737033090/video/1", "id_str": "1757863137636737025", "indices": [181, 204], "media_key": "13_1757863137636737025", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1757863137636737025/img/8NO6hqjCrFCjF34m.jpg", "source_status_id_str": "1757876855737033090", "source_user_id_str": "25422302", "type": "video", "url": "https://t.co/vmTcPVfCkO", "additional_media_info": {"monetizable": false, "source_user": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNTQyMjMwMg==", "rest_id": "25422302", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1786159247685754880/8pw2d-65_normal.jpg"}, "core": {"created_at": "Fri Mar 20 00:40:42 +0000 2009", "name": "<PERSON>", "screen_name": "<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Host of the Win-Win Podcast.  Slaying Moloch & Chasing Horizons 🚀 🌳🦾", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "winwinpodcast.com", "expanded_url": "http://winwinpodcast.com", "url": "https://t.co/ogHlEfLKkZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 26299, "followers_count": 265439, "friends_count": 542, "has_custom_timelines": true, "is_translator": false, "listed_count": 2531, "media_count": 2727, "normal_followers_count": 265439, "pinned_tweet_ids_str": ["1913297712453390447"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/25422302/1741722017", "profile_interstitial_type": "", "statuses_count": 23060, "translator_type": "none", "url": "https://t.co/ogHlEfLKkZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1679671864132767746", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1080, "w": 1920, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 1920, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [16, 9], "duration_millis": 1495869, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1757863137636737025/pl/98WNPL_8N0bm0c12.m3u8?tag=14&v=94d"}, {"bitrate": 288000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/480x270/AceI7UR6ES-dAlk-.mp4?tag=14"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/640x360/llPd5CZYUaKIWpE_.mp4?tag=14"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1757863137636737025/vid/avc1/1280x720/q1inVpdSodWllNXJ.mp4?tag=14"}]}, "media_results": {"result": {"media_key": "13_1757863137636737025"}}}]}, "favorite_count": 1584, "favorited": false, "full_text": "Given the absurdly unscientific and un-american ban of lab meat by @RonDeSantis yesterday (who clearly hasn’t done his homework)… here’s the ACTUAL science behind the technology: \n\nhttps://t.co/vmTcPVfCkO", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 104, "reply_count": 287, "retweet_count": 218, "retweeted": false, "user_id_str": "25422302", "id_str": "1786557732922728785"}, "twe_private_fields": {"created_at": 1714783530000, "updated_at": 1748554172119, "media_count": 1}}}, {"id": "1787947598780751889", "created_at": "2024-05-07 23:48:20 +03:00", "full_text": "Vencord is a Discord client mod\n\n-Features\n\n•Super easy to install (Download Installer, open, click install button, done)\n\n•100+ plugins built in: See a list\n◦Some highlights: SpotifyControls, MessageLogger, Experiments, GameActivityToggle, Translate, NoTrack, QuickReply, Free Emotes/Stickers, PermissionsViewer, CustomCommands, ShowHiddenChannels, PronounDB\n\n•Fairly lightweight despite the many inbuilt plugins\n\n•Excellent Browser Support: Run Vencord in your Browser via extension or UserScript\n\n•Works on any Discord branch: Stable, Canary or PTB all work (though for the best experience I recommend stable!)\n\n•Custom CSS and Themes: Inbuilt css editor with support to import any css files (including BetterDiscord themes)\n\n•Privacy friendly, blocks Discord analytics & crash reporting out of the box and has no telemetry\n\n•Maintained very actively, broken plugins are usually fixed within 12 hours\n\n•Settings sync: Keep your plugins and their settings synchronised between devices / apps (optional)\n\nhttps://t.co/6WopJUkHam", "media": [{"type": "photo", "url": "https://t.co/objhMv3NHn", "thumbnail": "https://pbs.twimg.com/media/GNAQe1ZXcAEJ_fG?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GNAQe1ZXcAEJ_fG?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1788, "retweet_count": 142, "bookmark_count": 2078, "quote_count": 6, "reply_count": 31, "views_count": 150194, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1787947598780751889", "metadata": {"__typename": "Tweet", "rest_id": "1787947598780751889", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTQ3OTI5NzI3OTYwMTk5MTgw", "rest_id": "1547929727960199180", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1795139938586890240/fQtS5oQr_normal.jpg"}, "core": {"created_at": "Fri Jul 15 13:03:09 +0000 2022", "name": "Pirat_Nation 🔴", "screen_name": "Pirat_Nation"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Gaming news | Films | Politics | Emulation | Shiposting | PC Help | Dc: https://t.co/9jNP5Aekf7 support: https://t.co/2Zl8cjtg07 @piratnation", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 34351, "followers_count": 196301, "friends_count": 37, "has_custom_timelines": true, "is_translator": false, "listed_count": 511, "media_count": 8850, "normal_followers_count": 196301, "pinned_tweet_ids_str": ["1800371012480946422"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1547929727960199180/1714831454", "profile_interstitial_type": "", "statuses_count": 17170, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"initial_tweet_id": "1787947179132228060", "edit_control_initial": {"edit_tweet_ids": ["1787947179132228060", "1787947358589698173", "1787947598780751889"], "editable_until_msecs": "1715118400000", "is_edit_eligible": false, "edits_remaining": "3"}}, "previous_counts": {"bookmark_count": 0, "favorite_count": 1, "quote_count": 0, "reply_count": 0, "retweet_count": 0}, "is_translatable": false, "views": {"count": "150194", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3ODc5NDc1OTg2NTA2OTE1ODU=", "text": "Vencord is a Discord client mod\n\n-Features\n\n•Super easy to install (Download Installer, open, click install button, done)\n\n•100+ plugins built in: See a list\n◦Some highlights: SpotifyControls, MessageLogger, Experiments, GameActivityToggle, Translate, NoTrack, QuickReply, Free Emotes/Stickers, PermissionsViewer, CustomCommands, ShowHiddenChannels, PronounDB\n\n•Fairly lightweight despite the many inbuilt plugins\n\n•Excellent Browser Support: Run Vencord in your Browser via extension or UserScript\n\n•Works on any Discord branch: Stable, Canary or PTB all work (though for the best experience I recommend stable!)\n\n•Custom CSS and Themes: Inbuilt css editor with support to import any css files (including BetterDiscord themes)\n\n•Privacy friendly, blocks Discord analytics & crash reporting out of the box and has no telemetry\n\n•Maintained very actively, broken plugins are usually fixed within 12 hours\n\n•Settings sync: Keep your plugins and their settings synchronised between devices / apps (optional)\n\nhttps://t.co/6WopJUkHam", "entity_set": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/Vendicated/Ven…", "expanded_url": "https://github.com/Vendicated/Vencord", "url": "https://t.co/6WopJUkHam", "indices": [1006, 1029]}], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 2078, "bookmarked": true, "created_at": "<PERSON><PERSON> May 07 20:48:20 +0000 2024", "conversation_id_str": "1787947598780751889", "display_text_range": [0, 277], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/objhMv3NHn", "expanded_url": "https://x.com/Pirat_Nation/status/1787947598780751889/photo/1", "id_str": "1787947173981679617", "indices": [278, 301], "media_key": "3_1787947173981679617", "media_url_https": "https://pbs.twimg.com/media/GNAQe1ZXcAEJ_fG.jpg", "type": "photo", "url": "https://t.co/objhMv3NHn", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 207, "y": 1125, "h": 52, "w": 52}, {"x": 452, "y": 1013, "h": 57, "w": 57}, {"x": 204, "y": 896, "h": 58, "w": 58}, {"x": 80, "y": 896, "h": 60, "w": 60}, {"x": 54, "y": 1082, "h": 102, "w": 102}, {"x": 617, "y": 663, "h": 223, "w": 223}, {"x": 645, "y": 124, "h": 470, "w": 470}]}, "medium": {"faces": [{"x": 190, "y": 1037, "h": 47, "w": 47}, {"x": 416, "y": 934, "h": 52, "w": 52}, {"x": 188, "y": 826, "h": 53, "w": 53}, {"x": 73, "y": 826, "h": 55, "w": 55}, {"x": 49, "y": 997, "h": 94, "w": 94}, {"x": 569, "y": 611, "h": 205, "w": 205}, {"x": 594, "y": 114, "h": 433, "w": 433}]}, "small": {"faces": [{"x": 108, "y": 588, "h": 27, "w": 27}, {"x": 236, "y": 529, "h": 29, "w": 29}, {"x": 106, "y": 468, "h": 30, "w": 30}, {"x": 41, "y": 468, "h": 31, "w": 31}, {"x": 28, "y": 565, "h": 53, "w": 53}, {"x": 322, "y": 346, "h": 116, "w": 116}, {"x": 337, "y": 64, "h": 245, "w": 245}]}, "orig": {"faces": [{"x": 207, "y": 1125, "h": 52, "w": 52}, {"x": 452, "y": 1013, "h": 57, "w": 57}, {"x": 204, "y": 896, "h": 58, "w": 58}, {"x": 80, "y": 896, "h": 60, "w": 60}, {"x": 54, "y": 1082, "h": 102, "w": 102}, {"x": 617, "y": 663, "h": 223, "w": 223}, {"x": 645, "y": 124, "h": 470, "w": 470}]}}, "sizes": {"large": {"h": 1301, "w": 1170, "resize": "fit"}, "medium": {"h": 1200, "w": 1079, "resize": "fit"}, "small": {"h": 680, "w": 612, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1301, "width": 1170, "focus_rects": [{"x": 0, "y": 355, "w": 1170, "h": 655}, {"x": 0, "y": 97, "w": 1170, "h": 1170}, {"x": 0, "y": 0, "w": 1141, "h": 1301}, {"x": 0, "y": 0, "w": 651, "h": 1301}, {"x": 0, "y": 0, "w": 1170, "h": 1301}]}, "media_results": {"result": {"media_key": "3_1787947173981679617"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/objhMv3NHn", "expanded_url": "https://x.com/Pirat_Nation/status/1787947598780751889/photo/1", "id_str": "1787947173981679617", "indices": [278, 301], "media_key": "3_1787947173981679617", "media_url_https": "https://pbs.twimg.com/media/GNAQe1ZXcAEJ_fG.jpg", "type": "photo", "url": "https://t.co/objhMv3NHn", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 207, "y": 1125, "h": 52, "w": 52}, {"x": 452, "y": 1013, "h": 57, "w": 57}, {"x": 204, "y": 896, "h": 58, "w": 58}, {"x": 80, "y": 896, "h": 60, "w": 60}, {"x": 54, "y": 1082, "h": 102, "w": 102}, {"x": 617, "y": 663, "h": 223, "w": 223}, {"x": 645, "y": 124, "h": 470, "w": 470}]}, "medium": {"faces": [{"x": 190, "y": 1037, "h": 47, "w": 47}, {"x": 416, "y": 934, "h": 52, "w": 52}, {"x": 188, "y": 826, "h": 53, "w": 53}, {"x": 73, "y": 826, "h": 55, "w": 55}, {"x": 49, "y": 997, "h": 94, "w": 94}, {"x": 569, "y": 611, "h": 205, "w": 205}, {"x": 594, "y": 114, "h": 433, "w": 433}]}, "small": {"faces": [{"x": 108, "y": 588, "h": 27, "w": 27}, {"x": 236, "y": 529, "h": 29, "w": 29}, {"x": 106, "y": 468, "h": 30, "w": 30}, {"x": 41, "y": 468, "h": 31, "w": 31}, {"x": 28, "y": 565, "h": 53, "w": 53}, {"x": 322, "y": 346, "h": 116, "w": 116}, {"x": 337, "y": 64, "h": 245, "w": 245}]}, "orig": {"faces": [{"x": 207, "y": 1125, "h": 52, "w": 52}, {"x": 452, "y": 1013, "h": 57, "w": 57}, {"x": 204, "y": 896, "h": 58, "w": 58}, {"x": 80, "y": 896, "h": 60, "w": 60}, {"x": 54, "y": 1082, "h": 102, "w": 102}, {"x": 617, "y": 663, "h": 223, "w": 223}, {"x": 645, "y": 124, "h": 470, "w": 470}]}}, "sizes": {"large": {"h": 1301, "w": 1170, "resize": "fit"}, "medium": {"h": 1200, "w": 1079, "resize": "fit"}, "small": {"h": 680, "w": 612, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1301, "width": 1170, "focus_rects": [{"x": 0, "y": 355, "w": 1170, "h": 655}, {"x": 0, "y": 97, "w": 1170, "h": 1170}, {"x": 0, "y": 0, "w": 1141, "h": 1301}, {"x": 0, "y": 0, "w": 651, "h": 1301}, {"x": 0, "y": 0, "w": 1170, "h": 1301}]}, "media_results": {"result": {"media_key": "3_1787947173981679617"}}}]}, "favorite_count": 1788, "favorited": false, "full_text": "Vencord is a Discord client mod\n\n-Features\n\n•Super easy to install (Download Installer, open, click install button, done)\n\n•100+ plugins built in: See a list\n◦Some highlights: SpotifyControls, MessageLogger, Experiments, GameActivityToggle, Translate, NoTrack, QuickReply, Free https://t.co/objhMv3NHn", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 31, "retweet_count": 142, "retweeted": false, "user_id_str": "1547929727960199180", "id_str": "1787947598780751889"}, "twe_private_fields": {"created_at": 1715114900000, "updated_at": 1748554172119, "media_count": 1}}}]