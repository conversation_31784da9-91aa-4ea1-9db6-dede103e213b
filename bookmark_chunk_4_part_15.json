[{"id": "1630569335570812929", "created_at": "2023-02-28 17:03:18 +03:00", "full_text": "If you sit for more than 6 hours a day, read this: https://t.co/CYUr79mYF9", "media": [{"type": "photo", "url": "https://t.co/CYUr79mYF9", "thumbnail": "https://pbs.twimg.com/media/FqDyK05WcAAx7Od?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FqDyK05WcAAx7Od?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 339120, "retweet_count": 57391, "bookmark_count": 197160, "quote_count": 4432, "reply_count": 2196, "views_count": 47879850, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1630569335570812929", "metadata": {"__typename": "Tweet", "rest_id": "1630569335570812929", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NTQzMzYyODAzODAzNDYzNjg=", "rest_id": "954336280380346368", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1279092409587163137/eN82f_KT_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Jan 19 12:54:34 +0000 2018", "name": "<PERSON>", "screen_name": "FitFounder"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Health Performance Coach To Entrepreneurs | Tweets on Fat Loss and Optimizing The Body | On a Mission to Transform A Billion Lives Through Health and Fitness", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "dango.co/privatecoaching", "expanded_url": "https://www.dango.co/privatecoaching", "url": "https://t.co/ity0SuP5bz", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 223439, "followers_count": 765386, "friends_count": 461, "has_custom_timelines": true, "is_translator": false, "listed_count": 8642, "media_count": 3547, "normal_followers_count": 765386, "pinned_tweet_ids_str": ["1927387176360137182"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/954336280380346368/**********", "profile_interstitial_type": "", "statuses_count": 102638, "translator_type": "none", "url": "https://t.co/ity0SuP5bz", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Sign up for a strategy call →"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1589029186592804865", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1630569335570812929"], "editable_until_msecs": "1677594798000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "47879850", "state": "EnabledWithCount"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 197160, "bookmarked": true, "created_at": "<PERSON><PERSON> Feb 28 14:03:18 +0000 2023", "conversation_id_str": "1630569335570812929", "display_text_range": [0, 50], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/CYUr79mYF9", "expanded_url": "https://x.com/FitFounder/status/1630569335570812929/photo/1", "id_str": "1630569332919922688", "indices": [51, 74], "media_key": "3_1630569332919922688", "media_url_https": "https://pbs.twimg.com/media/FqDyK05WcAAx7Od.jpg", "type": "photo", "url": "https://t.co/CYUr79mYF9", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 300, "w": 259, "resize": "fit"}, "medium": {"h": 300, "w": 259, "resize": "fit"}, "small": {"h": 300, "w": 259, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 300, "width": 259, "focus_rects": [{"x": 0, "y": 0, "w": 259, "h": 145}, {"x": 0, "y": 0, "w": 259, "h": 259}, {"x": 0, "y": 0, "w": 259, "h": 295}, {"x": 82, "y": 0, "w": 150, "h": 300}, {"x": 0, "y": 0, "w": 259, "h": 300}]}, "media_results": {"result": {"media_key": "3_1630569332919922688"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/CYUr79mYF9", "expanded_url": "https://x.com/FitFounder/status/1630569335570812929/photo/1", "id_str": "1630569332919922688", "indices": [51, 74], "media_key": "3_1630569332919922688", "media_url_https": "https://pbs.twimg.com/media/FqDyK05WcAAx7Od.jpg", "type": "photo", "url": "https://t.co/CYUr79mYF9", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 300, "w": 259, "resize": "fit"}, "medium": {"h": 300, "w": 259, "resize": "fit"}, "small": {"h": 300, "w": 259, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 300, "width": 259, "focus_rects": [{"x": 0, "y": 0, "w": 259, "h": 145}, {"x": 0, "y": 0, "w": 259, "h": 259}, {"x": 0, "y": 0, "w": 259, "h": 295}, {"x": 82, "y": 0, "w": 150, "h": 300}, {"x": 0, "y": 0, "w": 259, "h": 300}]}, "media_results": {"result": {"media_key": "3_1630569332919922688"}}}]}, "favorite_count": 339120, "favorited": false, "full_text": "If you sit for more than 6 hours a day, read this: https://t.co/CYUr79mYF9", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 4432, "reply_count": 2196, "retweet_count": 57391, "retweeted": false, "user_id_str": "954336280380346368", "id_str": "1630569335570812929"}, "twe_private_fields": {"created_at": 1677592998000, "updated_at": 1748554204513, "media_count": 1}}}, {"id": "1630853594915254274", "created_at": "2023-03-01 11:52:51 +03:00", "full_text": "If you want to become better at DevOps &amp; SRE, you have to practice, practice, practice 🛠️\n\nI found a repository containing exercises and questions, including answers to help you understand DevOps better and even prepare for interviews. 💪\n\nYou can find the link here 👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 907, "retweet_count": 226, "bookmark_count": 764, "quote_count": 2, "reply_count": 40, "views_count": 145965, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1630853594915254274", "metadata": {"__typename": "Tweet", "rest_id": "1630853594915254274", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTcyMDc5NjU0ODUwNzQ0MzIw", "rest_id": "1172079654850744320", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1177851392876318720/1LlZtDTx_normal.jpg"}, "core": {"created_at": "Thu Sep 12 09:29:08 +0000 2019", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "AI Enthusiast, Coder, Worked for Big Tech  | Music Producer", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "bio.link/simonholdorf", "expanded_url": "https://bio.link/simonholdorf", "url": "https://t.co/AtbkJcHgcS", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16188, "followers_count": 131188, "friends_count": 272, "has_custom_timelines": true, "is_translator": false, "listed_count": 2277, "media_count": 474, "normal_followers_count": 131188, "pinned_tweet_ids_str": ["1592425692670689282"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1172079654850744320/1743439484", "profile_interstitial_type": "", "statuses_count": 7430, "translator_type": "none", "url": "https://t.co/AtbkJcHgcS", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🎧"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1630853594915254274"], "editable_until_msecs": "1677662571000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "145965", "state": "EnabledWithCount"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 764, "bookmarked": true, "created_at": "Wed Mar 01 08:52:51 +0000 2023", "conversation_id_str": "1630853594915254274", "display_text_range": [0, 271], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 907, "favorited": false, "full_text": "If you want to become better at DevOps &amp; SRE, you have to practice, practice, practice 🛠️\n\nI found a repository containing exercises and questions, including answers to help you understand DevOps better and even prepare for interviews. 💪\n\nYou can find the link here 👇", "is_quote_status": false, "lang": "en", "quote_count": 2, "reply_count": 40, "retweet_count": 226, "retweeted": false, "user_id_str": "1172079654850744320", "id_str": "1630853594915254274"}, "twe_private_fields": {"created_at": 1677660771000, "updated_at": 1748554197458, "media_count": 0}}}]