[{"id": "1856202181168250908", "created_at": "2024-11-12 08:07:41 +03:00", "full_text": "Bash scripting loops basics https://t.co/1bRcHNIAPL", "media": [{"type": "photo", "url": "https://t.co/1bRcHNIAPL", "thumbnail": "https://pbs.twimg.com/media/GcKN_xaWYAAAC7L?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GcKN_xaWYAAAC7L?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2667, "retweet_count": 380, "bookmark_count": 2514, "quote_count": 6, "reply_count": 10, "views_count": 42, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1856202181168250908", "metadata": {"__typename": "Tweet", "rest_id": "1856202181168250908", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjU1MTQ4MDYzNTY2OTk1NDU2", "rest_id": "1655148063566995456", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1855587394470195200/tahq2U-3_normal.jpg"}, "core": {"created_at": "Sun May 07 09:50:46 +0000 2023", "name": "sysxplore", "screen_name": "sysxplore"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Everything about Linux🐧, DevOps, Networking, Automation, sysadmin🐱‍💻 & beginner tips |💡Newsletter → https://t.co/IDO9SJB4Ow | 🛍Merch → https://t.co/92R3mYzT7m", "entities": {"description": {"urls": [{"display_url": "sysxplore.substack.com", "expanded_url": "http://sysxplore.substack.com", "url": "https://t.co/IDO9SJB4Ow", "indices": [100, 123]}, {"display_url": "shop.sysxplore.com", "expanded_url": "http://shop.sysxplore.com", "url": "https://t.co/92R3mYzT7m", "indices": [135, 158]}]}, "url": {"urls": [{"display_url": "sysxplore.com", "expanded_url": "http://sysxplore.com", "url": "https://t.co/6bdjD3DbOg", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1878, "followers_count": 63428, "friends_count": 8, "has_custom_timelines": false, "is_translator": false, "listed_count": 394, "media_count": 846, "normal_followers_count": 63428, "pinned_tweet_ids_str": ["1916413780294066391"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1655148063566995456/1731243216", "profile_interstitial_type": "", "statuses_count": 2841, "translator_type": "none", "url": "https://t.co/6bdjD3DbOg", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1856202181168250908"], "editable_until_msecs": "1731391661000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "42", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2514, "bookmarked": true, "created_at": "<PERSON><PERSON> Nov 12 05:07:41 +0000 2024", "conversation_id_str": "1856202181168250908", "display_text_range": [0, 27], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/1bRcHNIAPL", "expanded_url": "https://x.com/sysxplore/status/1856202181168250908/photo/1", "id_str": "1856202123165196288", "indices": [28, 51], "media_key": "3_1856202123165196288", "media_url_https": "https://pbs.twimg.com/media/GcKN_xaWYAAAC7L.jpg", "type": "photo", "url": "https://t.co/1bRcHNIAPL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 1530, "y": 1392, "h": 178, "w": 178}]}, "medium": {"faces": [{"x": 896, "y": 815, "h": 104, "w": 104}]}, "small": {"faces": [{"x": 508, "y": 462, "h": 59, "w": 59}]}, "orig": {"faces": [{"x": 3060, "y": 2784, "h": 356, "w": 356}]}}, "sizes": {"large": {"h": 2048, "w": 2048, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 4096, "width": 4096, "focus_rects": [{"x": 0, "y": 0, "w": 4096, "h": 2294}, {"x": 0, "y": 0, "w": 4096, "h": 4096}, {"x": 0, "y": 0, "w": 3593, "h": 4096}, {"x": 716, "y": 0, "w": 2048, "h": 4096}, {"x": 0, "y": 0, "w": 4096, "h": 4096}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1856202123165196288"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/1bRcHNIAPL", "expanded_url": "https://x.com/sysxplore/status/1856202181168250908/photo/1", "id_str": "1856202123165196288", "indices": [28, 51], "media_key": "3_1856202123165196288", "media_url_https": "https://pbs.twimg.com/media/GcKN_xaWYAAAC7L.jpg", "type": "photo", "url": "https://t.co/1bRcHNIAPL", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 1530, "y": 1392, "h": 178, "w": 178}]}, "medium": {"faces": [{"x": 896, "y": 815, "h": 104, "w": 104}]}, "small": {"faces": [{"x": 508, "y": 462, "h": 59, "w": 59}]}, "orig": {"faces": [{"x": 3060, "y": 2784, "h": 356, "w": 356}]}}, "sizes": {"large": {"h": 2048, "w": 2048, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 4096, "width": 4096, "focus_rects": [{"x": 0, "y": 0, "w": 4096, "h": 2294}, {"x": 0, "y": 0, "w": 4096, "h": 4096}, {"x": 0, "y": 0, "w": 3593, "h": 4096}, {"x": 716, "y": 0, "w": 2048, "h": 4096}, {"x": 0, "y": 0, "w": 4096, "h": 4096}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1856202123165196288"}}}]}, "favorite_count": 2667, "favorited": false, "full_text": "Bash scripting loops basics https://t.co/1bRcHNIAPL", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 10, "retweet_count": 380, "retweeted": false, "user_id_str": "1655148063566995456", "id_str": "1856202181168250908"}, "twe_private_fields": {"created_at": 1731388061000, "updated_at": 1748554133840, "media_count": 1}}}, {"id": "1856348594355241142", "created_at": "2024-11-12 17:49:28 +03:00", "full_text": "In my new article I describe how to write and compile C code to get a pure standalone shellcode in easy way.\nIndirect API calling in C and Python script to automate the whole process included too! Enjoy :)\n\nhttps://t.co/MGY3TRw8yp", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 225, "retweet_count": 42, "bookmark_count": 172, "quote_count": 0, "reply_count": 3, "views_count": 12020, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1856348594355241142", "metadata": {"__typename": "Tweet", "rest_id": "1856348594355241142", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzgzMDg1NjY0NDY1OTkzNzI4", "rest_id": "1783085664465993728", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1783086379582226432/Ox3rMH7r_normal.jpg"}, "core": {"created_at": "Wed Apr 24 10:50:08 +0000 2024", "name": "Print3M", "screen_name": "Print3M_"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Red Team operator / programmer. Author of https://t.co/lGsbMY7MZZ", "entities": {"description": {"urls": [{"display_url": "sectube.tv", "expanded_url": "https://sectube.tv", "url": "https://t.co/lGsbMY7MZZ", "indices": [42, 65]}]}, "url": {"urls": [{"display_url": "print3m.github.io/blog", "expanded_url": "http://print3m.github.io/blog", "url": "https://t.co/leKJGz7MGu", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 601, "followers_count": 708, "friends_count": 82, "has_custom_timelines": false, "is_translator": false, "listed_count": 8, "media_count": 18, "normal_followers_count": 708, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1783085664465993728/1739810769", "profile_interstitial_type": "", "statuses_count": 120, "translator_type": "none", "url": "https://t.co/leKJGz7MGu", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/MGY3TRw8yp", "legacy": {"binding_values": [{"key": "description", "value": {"string_value": "In this post I demonstrate how to write and compile C code to get a pure standalone shellcode in easy way.", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "print3m.github.io", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "print3m.github.io", "type": "STRING"}}, {"key": "title", "value": {"string_value": "From C to shellcode (simple way)", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/MGY3TRw8yp", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/MGY3TRw8yp", "user_refs_results": []}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1856348594355241142"], "editable_until_msecs": "1731426568000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "12020", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "community_results": {"result": {"__typename": "Community", "id_str": "17839905***********", "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}}}, "author_community_relationship": {"community_results": {"result": {"__typename": "Community", "id_str": "17839905***********", "name": "C and Assembly Developers", "description": "This community is geared towards accelerated knowledge of low-level computing concepts, and active contribution to projects in C and ASM.", "created_at": *************, "question": "THIS IS MANDITORY:\n\nYour GitHub account or a snippet of code you've written in C or Assembly that you're proud of.", "search_tags": ["programming", "c", "asm", "softwaredevelopment", "cprogramming", "lowlevel", "code", "math", "ai", "ml"], "is_nsfw": false, "primary_community_topic": {"topic_id": "303", "topic_name": "Software"}, "actions": {"delete_action_result": {"__typename": "CommunityDeleteActionUnavailable", "reason": "Unavailable"}, "join_action_result": {"__typename": "CommunityJoinActionUnavailable", "reason": "ViewerIsMember", "message": "You are already a member."}, "leave_action_result": {"__typename": "CommunityLeaveAction"}, "pin_action_result": {"__typename": "CommunityTweetPinActionUnavailable"}}, "admin_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "creator_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, "invites_result": {"__typename": "CommunityInvites", "remaining_invite_count": 10, "users_to_invite_slice": {"items": [], "slice_info": {}}}, "join_policy": "Open", "invites_policy": "MemberInvitesAllowed", "is_pinned": true, "members_facepile_results": [{"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDk1ODMzNzcxNTI4NDg2OTEy", "rest_id": "1095833771528486912", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1852982863295361024/nt6Wkcdk_normal.jpg"}, "core": {"created_at": "Wed Feb 13 23:55:05 +0000 2019", "name": "<PERSON><PERSON><PERSON>", "screen_name": "DisperseControl"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I am potato. e/acc. Bulltard.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "brrr.money", "expanded_url": "https://brrr.money/", "url": "https://t.co/LnifTg9G7X", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 638143, "followers_count": 4496, "friends_count": 7347, "has_custom_timelines": true, "is_translator": false, "listed_count": 23, "media_count": 26419, "normal_followers_count": 4496, "pinned_tweet_ids_str": ["1917289486897029355"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1095833771528486912/1652435782", "profile_interstitial_type": "", "statuses_count": 55745, "translator_type": "none", "url": "https://t.co/LnifTg9G7X", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Canazuela"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoyMTkyMDM0MTI=", "rest_id": "219203412", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/7etsuo", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_bigger.jpg"}, "description": "tetsuo.sol (mog/acc)", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1920040820654432256/68AZgHjH_normal.jpg"}, "core": {"created_at": "Wed Nov 24 06:15:09 +0000 2010", "name": "Erroneous Input", "screen_name": "<PERSON>_<PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "what?", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 62246, "followers_count": 1341, "friends_count": 202, "has_custom_timelines": false, "is_translator": false, "listed_count": 63, "media_count": 18475, "normal_followers_count": 1341, "pinned_tweet_ids_str": ["1777265833670262905"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/219203412/1721665890", "profile_interstitial_type": "", "statuses_count": 79937, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxMDQxNjUxNjg5MDQyNzg4MzUy", "rest_id": "1041651689042788352", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1846890999634681856/iixBL8be_normal.jpg"}, "core": {"created_at": "Mon Sep 17 11:34:50 +0000 2018", "name": "faulty *ptrrr", "screen_name": "0x_shaq"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Chief pager engineer 📟", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pwner.gg/blog/", "expanded_url": "https://pwner.gg/blog/", "url": "https://t.co/VVHKg0umzt", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10259, "followers_count": 4694, "friends_count": 364, "has_custom_timelines": true, "is_translator": false, "listed_count": 24, "media_count": 604, "normal_followers_count": 4694, "pinned_tweet_ids_str": ["1794342802173890682"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1041651689042788352/1659977667", "profile_interstitial_type": "", "statuses_count": 2346, "translator_type": "none", "url": "https://t.co/VVHKg0umzt", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🇮🇱Israel"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}, {"result": {"__typename": "User", "id": "VXNlcjoxNTAxMTU2Mzk2NTA2OTM5Mzk1", "rest_id": "1501156396506939395", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1810298224382763008/VVGhTJi4_normal.jpg"}, "core": {"created_at": "<PERSON>e Mar 08 11:23:25 +0000 2022", "name": "<PERSON>raw 🍓", "screen_name": "ddnnddc"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Technologist, major node in the cat distribution system.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "youtube.com/playlist?list=…", "expanded_url": "https://www.youtube.com/playlist?list=PLdVyg0SqO9v_FMdp7oPhmlMSP0kRpziVg", "url": "https://t.co/Itnc8qT9Ow", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 49644, "followers_count": 434, "friends_count": 951, "has_custom_timelines": false, "is_translator": false, "listed_count": 2, "media_count": 960, "normal_followers_count": 434, "pinned_tweet_ids_str": ["1832506180339716148"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1501156396506939395/1720444898", "profile_interstitial_type": "", "statuses_count": 8050, "translator_type": "none", "url": "https://t.co/Itnc8qT9Ow", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Melbourne, Australia"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1693828904694563112", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}], "moderator_count": 9, "member_count": 49694, "role": "Member", "rules": [{"rest_id": "1840492394057511216", "name": "Keep posts on topic.", "description": "Focus discussions on C, Assembly programming, and related topics. Avoid posting unrelated content to maintain quality. This implies no politics.."}, {"rest_id": "1783990929403363417", "name": "Explore and share.", "description": "Encourage curiosity and learning. Share your experiences, code snippets, and resources. Help others by contributing to the collective knowledge."}, {"rest_id": "1783990992099848586", "name": "Provide constructive feedback.", "description": "When critiquing code or concepts, offer clear, constructive, and polite feedback. Aim to help others improve and learn."}, {"rest_id": "1785457160471908430", "name": "Privacy and security.", "description": "Respect the privacy and the integrity of the community."}, {"rest_id": "1795107937150730598", "name": "Low effort bait questions lead to permanent removal.", "description": "Posts that use shallow questions to boost engagement risk permanent removal to uphold discussion quality."}, {"rest_id": "1840493930280014028", "name": "Suggestion-Only Policy: DM @7etsuo for a groupchat invite.", "description": "By joining the community group chat, you agree to keep it a space for suggestions, C-related questions, sharing work, and community suggestions."}], "custom_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 21, "green": 194, "blue": 225}, "percentage": 25.43}, {"rgb": {"red": 58, "green": 154, "blue": 255}, "percentage": 11.73}, {"rgb": {"red": 243, "green": 244, "blue": 250}, "percentage": 10.8}, {"rgb": {"red": 20, "green": 190, "blue": 12}, "percentage": 8.56}, {"rgb": {"red": 226, "green": 97, "blue": 162}, "percentage": 8.44}]}, "original_img_url": "https://pbs.twimg.com/community_banner_img/1827118393209618432/I8no5S8w?format=jpg&name=orig", "original_img_width": 1200, "original_img_height": 480, "salient_rect": {"left": 601, "top": 240, "width": 1, "height": 1}}}, "default_banner_media": {"media_info": {"color_info": {"palette": [{"rgb": {"red": 1, "green": 161, "blue": 155}, "percentage": 79.35}, {"rgb": {"red": 248, "green": 120, "blue": 132}, "percentage": 11.83}, {"rgb": {"red": 212, "green": 133, "blue": 146}, "percentage": 2.97}, {"rgb": {"red": 129, "green": 175, "blue": 168}, "percentage": 1.95}, {"rgb": {"red": 244, "green": 84, "blue": 97}, "percentage": 0.81}]}, "original_img_url": "https://pbs.twimg.com/media/FECQY8MVEAEnZBg.jpg", "original_img_width": 1200, "original_img_height": 480}}, "viewer_relationship": {"moderation_state": {"__typename": "CommunityUserDefaultModerationState"}}, "join_requests_result": {"__typename": "CommunityJoinRequestsUnavailable"}}}, "role": "Member", "user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzgzMDg1NjY0NDY1OTkzNzI4", "rest_id": "1783085664465993728", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1783086379582226432/Ox3rMH7r_normal.jpg"}, "core": {"created_at": "Wed Apr 24 10:50:08 +0000 2024", "name": "Print3M", "screen_name": "Print3M_"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Red Team operator / programmer. Author of https://t.co/lGsbMY7MZZ", "entities": {"description": {"urls": [{"display_url": "sectube.tv", "expanded_url": "https://sectube.tv", "url": "https://t.co/lGsbMY7MZZ", "indices": [42, 65]}]}, "url": {"urls": [{"display_url": "print3m.github.io/blog", "expanded_url": "http://print3m.github.io/blog", "url": "https://t.co/leKJGz7MGu", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 601, "followers_count": 708, "friends_count": 82, "has_custom_timelines": false, "is_translator": false, "listed_count": 8, "media_count": 18, "normal_followers_count": 708, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1783085664465993728/1739810769", "profile_interstitial_type": "", "statuses_count": 120, "translator_type": "none", "url": "https://t.co/leKJGz7MGu", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "legacy": {"bookmark_count": 172, "bookmarked": true, "created_at": "<PERSON><PERSON> Nov 12 14:49:28 +0000 2024", "conversation_id_str": "1856348594355241142", "display_text_range": [0, 230], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "print3m.github.io/blog/from-c-to…", "expanded_url": "https://print3m.github.io/blog/from-c-to-shellcode", "url": "https://t.co/MGY3TRw8yp", "indices": [207, 230]}], "user_mentions": []}, "favorite_count": 225, "favorited": false, "full_text": "In my new article I describe how to write and compile C code to get a pure standalone shellcode in easy way.\nIndirect API calling in C and Python script to automate the whole process included too! Enjoy :)\n\nhttps://t.co/MGY3TRw8yp", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 3, "retweet_count": 42, "retweeted": false, "user_id_str": "1783085664465993728", "id_str": "1856348594355241142"}, "twe_private_fields": {"created_at": 1731422968000, "updated_at": 1748554133840, "media_count": 0}}}]