# Bookmarks from bookmark_chunk_4_part_33.json

## Category: Education / Programming / Data Structures / Algorithms / Learning Tips / #100DaysOfCode

*   **Author:** @maybeshalinii
    **Date:** 2023-09-08 15:41:12 +03:00
    **Content:** Learning Data Structures and Algorithms (DSA) is a crucial part of becoming a proficient programmer.
Here are some important things that we often overlook while learning DSA:

1. Build a Strong Foundation:
Choose a language and start with the basics. Understand the fundamentals of data structures and algorithms thoroughly. These are the building blocks of DSA.

2. Understand Time and Space Complexity: 
Learn to analyse the time and space complexity of algorithms. This understanding is critical for evaluating the efficiency of your code.

3. Practice Regularly: 
DSA is a skill that improves with practice. Solve a wide range of problems, from simple to complex. Online platforms like @LeetCode , @hackerrank and @codechef etc offer a variety of problems.

4. Learn Multiple Approaches: 
Don't settle for one solution to a problem. Explore multiple approaches and understand the trade-offs between them. This helps you become a more versatile problem solver.

5. Teach Others: 
This is one of the most effective ways I can vouch for to solidify one's understanding. Try explaining DSA concepts to others.

6. Participate in Coding Contests: 
It helps you practice problem-solving, algorithmic thinking, and coding under time constraints, which are essential skills for becoming proficient in DSA.

7. Study Well-Documented Code:
Review well-documented code examples and open-source projects that use DSA. This can provide valuable insights into best practices and real-world applications.

The most important skill to develop while learning DSA is patience and consistency. At the beginning, it's common not to solve many questions in a day. It takes time and practice to reach your full potential. Feeling discouraged when you can't solve a problem and thinking about giving up should not even be considered as options.
Remember, the people who inspire you were once beginners like you, and with hard work, you can achieve what they have. Best of luck, everyone!
#100DaysOfCode
    **URL:** [https://twitter.com/maybeshalinii/status/1700127142636376391](https://twitter.com/maybeshalinii/status/1700127142636376391)

---

## Category: Fitness / Exercise Equipment / Home Gym / Workout Bench / Kenya / E-commerce

*   **Author:** @KhairaUmmahFit
    **Date:** 2023-09-11 12:24:25 +03:00
    **Content:** Full Body Bench 45 Exercises in 1 bench
The only workout bench you will ever need to train your whole body. 
CLICK ON THE LINK to see all the exercises  that you can do with it.
get 36kg FREE weights and a 1.2m bar only for 30,000 KES
call/whatsapp 0726696926
    **URL:** [https://twitter.com/KhairaUmmahFit/status/1701164784429773274](https://twitter.com/KhairaUmmahFit/status/1701164784429773274)
    **Card:**
        *   **Title:** FULL BODY workout bench with 36kg weights @30k ONLY
        *   **Description:** khairaummah.co.ke
        *   **URL:** [https://khairaummah.co.ke/product/full-body-workout-bench/](https://khairaummah.co.ke/product/full-body-workout-bench/)
        *   **Media:** 
            *   Type: video
            *   Thumbnail: https://pbs.twimg.com/amplify_video_thumb/1696884435163955200/img/3MeBkD0Xx_izShah.jpg
            *   Original: https://video.twimg.com/amplify_video/1696884435163955200/vid/1280x720/6A7LzsAMx_oScAF7.mp4?tag=14

---