[{"id": "1788176526862590026", "created_at": "2024-05-08 14:58:01 +03:00", "full_text": "Windows 11 24H2 will enable BitLocker encryption for everyone, happens on both clean installs and reinstalls.\n\nBitLocker has been proven to impact system performance, particularly SSD performance.\n SSD performance can drop by up to 45% depending on the workload\n\nEven worse, if you are using the software form of BitLocker, all the encryption and decryption tasks get loaded onto the CPU, which can potentially reduce system performance as well.\n\nAnything storage-related goes wrong with a machine that has BitLocker turned on, users can lose all access to their drive contents due to encryption.\n\n——\n\nHow to disable Bitlocket for Windows Home:\n\n1 Type and search [Device encryption settings] in the Windows search bar①, then click [Open]\n\n2 On the Device encryption field, set the option to [Off]③.\n\n3 Confirm whether you need to turn off device encryption, select [Turn off] to disable the device encryption function④.\n\nArticle: https://t.co/CGyJUbW1ze\n\nTutorial to disable: https://t.co/hhEtfy6zqw", "media": [{"type": "photo", "url": "https://t.co/soKqn6ycRD", "thumbnail": "https://pbs.twimg.com/media/GNDhEkQXkAAEKUI?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GNDhEkQXkAAEKUI?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/soKqn6ycRD", "thumbnail": "https://pbs.twimg.com/media/GNDhEkSXYAA6Nk5?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GNDhEkSXYAA6Nk5?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/soKqn6ycRD", "thumbnail": "https://pbs.twimg.com/media/GNDhEkLW4AAUFuo?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GNDhEkLW4AAUFuo?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5866, "retweet_count": 879, "bookmark_count": 7883, "quote_count": 136, "reply_count": 161, "views_count": 1014839, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1788176526862590026", "metadata": {"__typename": "Tweet", "rest_id": "1788176526862590026", "birdwatch_pivot": {"destinationUrl": "https://twitter.com/i/birdwatch/t/1788176526862590026?source=6", "title": "Rate proposed Community Notes", "visualStyle": "<PERSON><PERSON><PERSON>", "iconType": "BirdwatchV1Icon", "footerIconType": "BirdwatchEyeOff"}, "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTQ3OTI5NzI3OTYwMTk5MTgw", "rest_id": "1547929727960199180", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1795139938586890240/fQtS5oQr_normal.jpg"}, "core": {"created_at": "Fri Jul 15 13:03:09 +0000 2022", "name": "Pirat_Nation 🔴", "screen_name": "Pirat_Nation"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Gaming news | Films | Politics | Emulation | Shiposting | PC Help | Dc: https://t.co/9jNP5Aekf7 support: https://t.co/2Zl8cjtg07 @piratnation", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 34351, "followers_count": 196301, "friends_count": 37, "has_custom_timelines": true, "is_translator": false, "listed_count": 511, "media_count": 8850, "normal_followers_count": 196301, "pinned_tweet_ids_str": ["1800371012480946422"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1547929727960199180/1714831454", "profile_interstitial_type": "", "statuses_count": 17170, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1788176526862590026"], "editable_until_msecs": "1715173081000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1014839", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3ODgxNzY1MjY2MjM2MDQ3MzY=", "text": "Windows 11 24H2 will enable BitLocker encryption for everyone, happens on both clean installs and reinstalls.\n\nBitLocker has been proven to impact system performance, particularly SSD performance.\n SSD performance can drop by up to 45% depending on the workload\n\nEven worse, if you are using the software form of BitLocker, all the encryption and decryption tasks get loaded onto the CPU, which can potentially reduce system performance as well.\n\nAnything storage-related goes wrong with a machine that has BitLocker turned on, users can lose all access to their drive contents due to encryption.\n\n——\n\nHow to disable Bitlocket for Windows Home:\n\n1 Type and search [Device encryption settings] in the Windows search bar①, then click [Open]\n\n2 On the Device encryption field, set the option to [Off]③.\n\n3 Confirm whether you need to turn off device encryption, select [Turn off] to disable the device encryption function④.\n\nArticle: https://t.co/CGyJUbW1ze\n\nTutorial to disable: https://t.co/hhEtfy6zqw", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "tomshardware.com/software/windo…", "expanded_url": "https://www.tomshardware.com/software/windows/windows-11-24h2-will-enable-bitlocker-encryption-for-everyone-happens-on-both-clean-installs-and-reinstalls?utm_content=tomsguide&utm_medium=social&utm_campaign=socialflow&utm_source=facebook.com", "url": "https://t.co/CGyJUbW1ze", "indices": [931, 954]}, {"display_url": "asus.com/support/faq/10…", "expanded_url": "https://www.asus.com/support/faq/1047461/", "url": "https://t.co/hhEtfy6zqw", "indices": [977, 1000]}], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 7883, "bookmarked": true, "created_at": "Wed May 08 11:58:01 +0000 2024", "conversation_id_str": "1788176526862590026", "display_text_range": [0, 277], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/soKqn6ycRD", "expanded_url": "https://x.com/Pirat_Nation/status/1788176526862590026/photo/1", "id_str": "1788176520634142720", "indices": [278, 301], "media_key": "3_1788176520634142720", "media_url_https": "https://pbs.twimg.com/media/GNDhEkQXkAAEKUI.jpg", "type": "photo", "url": "https://t.co/soKqn6ycRD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1011, "w": 1164, "resize": "fit"}, "medium": {"h": 1011, "w": 1164, "resize": "fit"}, "small": {"h": 591, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1011, "width": 1164, "focus_rects": [{"x": 0, "y": 226, "w": 1164, "h": 652}, {"x": 153, "y": 0, "w": 1011, "h": 1011}, {"x": 226, "y": 0, "w": 887, "h": 1011}, {"x": 416, "y": 0, "w": 506, "h": 1011}, {"x": 0, "y": 0, "w": 1164, "h": 1011}]}, "media_results": {"result": {"media_key": "3_1788176520634142720"}}}, {"display_url": "pic.x.com/soKqn6ycRD", "expanded_url": "https://x.com/Pirat_Nation/status/1788176526862590026/photo/1", "id_str": "1788176520642519040", "indices": [278, 301], "media_key": "3_1788176520642519040", "media_url_https": "https://pbs.twimg.com/media/GNDhEkSXYAA6Nk5.jpg", "type": "photo", "url": "https://t.co/soKqn6ycRD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 951, "w": 1380, "resize": "fit"}, "medium": {"h": 827, "w": 1200, "resize": "fit"}, "small": {"h": 469, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 951, "width": 1380, "focus_rects": [{"x": 0, "y": 0, "w": 1380, "h": 773}, {"x": 429, "y": 0, "w": 951, "h": 951}, {"x": 512, "y": 0, "w": 834, "h": 951}, {"x": 691, "y": 0, "w": 476, "h": 951}, {"x": 0, "y": 0, "w": 1380, "h": 951}]}, "media_results": {"result": {"media_key": "3_1788176520642519040"}}}, {"display_url": "pic.x.com/soKqn6ycRD", "expanded_url": "https://x.com/Pirat_Nation/status/1788176526862590026/photo/1", "id_str": "1788176520613126144", "indices": [278, 301], "media_key": "3_1788176520613126144", "media_url_https": "https://pbs.twimg.com/media/GNDhEkLW4AAUFuo.jpg", "type": "photo", "url": "https://t.co/soKqn6ycRD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 322, "w": 1024, "resize": "fit"}, "medium": {"h": 322, "w": 1024, "resize": "fit"}, "small": {"h": 214, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 322, "width": 1024, "focus_rects": [{"x": 225, "y": 0, "w": 575, "h": 322}, {"x": 351, "y": 0, "w": 322, "h": 322}, {"x": 371, "y": 0, "w": 282, "h": 322}, {"x": 432, "y": 0, "w": 161, "h": 322}, {"x": 0, "y": 0, "w": 1024, "h": 322}]}, "media_results": {"result": {"media_key": "3_1788176520613126144"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/soKqn6ycRD", "expanded_url": "https://x.com/Pirat_Nation/status/1788176526862590026/photo/1", "id_str": "1788176520634142720", "indices": [278, 301], "media_key": "3_1788176520634142720", "media_url_https": "https://pbs.twimg.com/media/GNDhEkQXkAAEKUI.jpg", "type": "photo", "url": "https://t.co/soKqn6ycRD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1011, "w": 1164, "resize": "fit"}, "medium": {"h": 1011, "w": 1164, "resize": "fit"}, "small": {"h": 591, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1011, "width": 1164, "focus_rects": [{"x": 0, "y": 226, "w": 1164, "h": 652}, {"x": 153, "y": 0, "w": 1011, "h": 1011}, {"x": 226, "y": 0, "w": 887, "h": 1011}, {"x": 416, "y": 0, "w": 506, "h": 1011}, {"x": 0, "y": 0, "w": 1164, "h": 1011}]}, "media_results": {"result": {"media_key": "3_1788176520634142720"}}}, {"display_url": "pic.x.com/soKqn6ycRD", "expanded_url": "https://x.com/Pirat_Nation/status/1788176526862590026/photo/1", "id_str": "1788176520642519040", "indices": [278, 301], "media_key": "3_1788176520642519040", "media_url_https": "https://pbs.twimg.com/media/GNDhEkSXYAA6Nk5.jpg", "type": "photo", "url": "https://t.co/soKqn6ycRD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 951, "w": 1380, "resize": "fit"}, "medium": {"h": 827, "w": 1200, "resize": "fit"}, "small": {"h": 469, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 951, "width": 1380, "focus_rects": [{"x": 0, "y": 0, "w": 1380, "h": 773}, {"x": 429, "y": 0, "w": 951, "h": 951}, {"x": 512, "y": 0, "w": 834, "h": 951}, {"x": 691, "y": 0, "w": 476, "h": 951}, {"x": 0, "y": 0, "w": 1380, "h": 951}]}, "media_results": {"result": {"media_key": "3_1788176520642519040"}}}, {"display_url": "pic.x.com/soKqn6ycRD", "expanded_url": "https://x.com/Pirat_Nation/status/1788176526862590026/photo/1", "id_str": "1788176520613126144", "indices": [278, 301], "media_key": "3_1788176520613126144", "media_url_https": "https://pbs.twimg.com/media/GNDhEkLW4AAUFuo.jpg", "type": "photo", "url": "https://t.co/soKqn6ycRD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 322, "w": 1024, "resize": "fit"}, "medium": {"h": 322, "w": 1024, "resize": "fit"}, "small": {"h": 214, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 322, "width": 1024, "focus_rects": [{"x": 225, "y": 0, "w": 575, "h": 322}, {"x": 351, "y": 0, "w": 322, "h": 322}, {"x": 371, "y": 0, "w": 282, "h": 322}, {"x": 432, "y": 0, "w": 161, "h": 322}, {"x": 0, "y": 0, "w": 1024, "h": 322}]}, "media_results": {"result": {"media_key": "3_1788176520613126144"}}}]}, "favorite_count": 5866, "favorited": false, "full_text": "Windows 11 24H2 will enable BitLocker encryption for everyone, happens on both clean installs and reinstalls.\n\nBitLocker has been proven to impact system performance, particularly SSD performance.\n SSD performance can drop by up to 45% depending on the workload\n\nEven worse, if https://t.co/soKqn6ycRD", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 136, "reply_count": 161, "retweet_count": 879, "retweeted": false, "user_id_str": "1547929727960199180", "id_str": "1788176526862590026"}, "twe_private_fields": {"created_at": 1715169481000, "updated_at": 1748554172119, "media_count": 3}}}, {"id": "1788909354655371619", "created_at": "2024-05-10 15:30:01 +03:00", "full_text": "10 great Python packages for Data Science not known to many:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1863, "retweet_count": 332, "bookmark_count": 4544, "quote_count": 7, "reply_count": 27, "views_count": 549482, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1788909354655371619", "metadata": {"__typename": "Tweet", "rest_id": "1788909354655371619", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MDM2MDE5NzI=", "rest_id": "703601972", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1578327351544360960/YFpWSWIX_normal.jpg"}, "core": {"created_at": "Wed Jul 18 18:58:39 +0000 2012", "name": "<PERSON><PERSON><PERSON> 🚀", "screen_name": "akshay_pachaar"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Simplifying LLMs, AI Agents, RAGs and Machine Learning for you! • Co-founder @dailydoseofds_• BITS Pilani • 3 Patents • ex-AI Engineer @ LightningAI", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "join.dailydoseofds.com", "expanded_url": "http://join.dailydoseofds.com", "url": "https://t.co/TLsKA1fohN", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 20351, "followers_count": 206134, "friends_count": 470, "has_custom_timelines": true, "is_translator": false, "listed_count": 2743, "media_count": 3895, "normal_followers_count": 206134, "pinned_tweet_ids_str": ["1724322731376918560"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/703601972/1733646485", "profile_interstitial_type": "", "statuses_count": 17580, "translator_type": "none", "url": "https://t.co/TLsKA1fohN", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Learn AI Engineering 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1476572433905717251", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1788909354655371619"], "editable_until_msecs": "1715347801000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "549482", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 4544, "bookmarked": true, "created_at": "Fri May 10 12:30:01 +0000 2024", "conversation_id_str": "1788909354655371619", "display_text_range": [0, 60], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1863, "favorited": false, "full_text": "10 great Python packages for Data Science not known to many:", "is_quote_status": false, "lang": "en", "quote_count": 7, "reply_count": 27, "retweet_count": 332, "retweeted": false, "user_id_str": "703601972", "id_str": "1788909354655371619"}, "twe_private_fields": {"created_at": 1715344201000, "updated_at": 1748554172119, "media_count": 0}}}]