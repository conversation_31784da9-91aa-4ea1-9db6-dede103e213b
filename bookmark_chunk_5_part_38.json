[{"id": "1875916583664865644", "created_at": "2025-01-05 17:45:41 +03:00", "full_text": "You can just build things https://t.co/eyNwO4I4KY", "media": [{"type": "photo", "url": "https://t.co/eyNwO4I4KY", "thumbnail": "https://pbs.twimg.com/media/GgiYJWuXcAAVTKE?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GgiYJWuXcAAVTKE?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 835, "retweet_count": 48, "bookmark_count": 1176, "quote_count": 1, "reply_count": 9, "views_count": 59707, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1875916583664865644", "metadata": {"__typename": "Tweet", "rest_id": "1875916583664865644", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDcwMTQ1NDY3OTMzNjcxNDI0", "rest_id": "1470145467933671424", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1722295652451856385/m2SB4PkY_normal.jpg"}, "core": {"created_at": "Sun Dec 12 21:36:27 +0000 2021", "name": "Glitchbyte", "screen_name": "0xglitchbyte"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "10+ years Infra Engineer exploring the bounds of Computing and Christianity. <PERSON> is King. Follow for the journey.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "glitchbyte.io", "expanded_url": "http://glitchbyte.io", "url": "https://t.co/7JTvbHjJzP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 12983, "followers_count": 9213, "friends_count": 378, "has_custom_timelines": false, "is_translator": false, "listed_count": 62, "media_count": 624, "normal_followers_count": 9213, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1470145467933671424/1697467836", "profile_interstitial_type": "", "statuses_count": 5081, "translator_type": "none", "url": "https://t.co/7JTvbHjJzP", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Read my writings at:"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1875916583664865644"], "editable_until_msecs": "1736091941000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "59707", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1176, "bookmarked": true, "created_at": "Sun Jan 05 14:45:41 +0000 2025", "conversation_id_str": "1875916583664865644", "display_text_range": [0, 25], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/eyNwO4I4KY", "expanded_url": "https://x.com/0xglitchbyte/status/1875916583664865644/photo/1", "id_str": "1875916531286110208", "indices": [26, 49], "media_key": "3_1875916531286110208", "media_url_https": "https://pbs.twimg.com/media/GgiYJWuXcAAVTKE.jpg", "type": "photo", "url": "https://t.co/eyNwO4I4KY", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 839, "y": 717, "h": 129, "w": 129}]}, "medium": {"faces": [{"x": 492, "y": 420, "h": 76, "w": 76}]}, "small": {"faces": [{"x": 278, "y": 238, "h": 43, "w": 43}]}, "orig": {"faces": [{"x": 1345, "y": 1149, "h": 208, "w": 208}]}}, "sizes": {"large": {"h": 1258, "w": 2048, "resize": "fit"}, "medium": {"h": 737, "w": 1200, "resize": "fit"}, "small": {"h": 418, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2014, "width": 3280, "focus_rects": [{"x": 0, "y": 0, "w": 3280, "h": 1837}, {"x": 387, "y": 0, "w": 2014, "h": 2014}, {"x": 511, "y": 0, "w": 1767, "h": 2014}, {"x": 891, "y": 0, "w": 1007, "h": 2014}, {"x": 0, "y": 0, "w": 3280, "h": 2014}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1875916531286110208"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/eyNwO4I4KY", "expanded_url": "https://x.com/0xglitchbyte/status/1875916583664865644/photo/1", "id_str": "1875916531286110208", "indices": [26, 49], "media_key": "3_1875916531286110208", "media_url_https": "https://pbs.twimg.com/media/GgiYJWuXcAAVTKE.jpg", "type": "photo", "url": "https://t.co/eyNwO4I4KY", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 839, "y": 717, "h": 129, "w": 129}]}, "medium": {"faces": [{"x": 492, "y": 420, "h": 76, "w": 76}]}, "small": {"faces": [{"x": 278, "y": 238, "h": 43, "w": 43}]}, "orig": {"faces": [{"x": 1345, "y": 1149, "h": 208, "w": 208}]}}, "sizes": {"large": {"h": 1258, "w": 2048, "resize": "fit"}, "medium": {"h": 737, "w": 1200, "resize": "fit"}, "small": {"h": 418, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2014, "width": 3280, "focus_rects": [{"x": 0, "y": 0, "w": 3280, "h": 1837}, {"x": 387, "y": 0, "w": 2014, "h": 2014}, {"x": 511, "y": 0, "w": 1767, "h": 2014}, {"x": 891, "y": 0, "w": 1007, "h": 2014}, {"x": 0, "y": 0, "w": 3280, "h": 2014}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1875916531286110208"}}}]}, "favorite_count": 835, "favorited": false, "full_text": "You can just build things https://t.co/eyNwO4I4KY", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 9, "retweet_count": 48, "retweeted": false, "user_id_str": "1470145467933671424", "id_str": "1875916583664865644"}, "twe_private_fields": {"created_at": 1736088341000, "updated_at": 1748554112350, "media_count": 1}}}, {"id": "1876432435618951326", "created_at": "2025-01-07 03:55:29 +03:00", "full_text": "Video2X is an open-source tool that uses machine learning to upscale video resolution, utilizing algorithms like waifu2X and Anime4K, and supports frame interpolation and audio/subtitle stream preservation https://t.co/sR3w9hMtcC", "media": [{"type": "photo", "url": "https://t.co/sR3w9hMtcC", "thumbnail": "https://pbs.twimg.com/media/GgptVALWcAIiOux?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GgptVALWcAIiOux?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1254, "retweet_count": 135, "bookmark_count": 1151, "quote_count": 3, "reply_count": 8, "views_count": 64936, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1876432435618951326", "metadata": {"__typename": "Tweet", "rest_id": "1876432435618951326", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzI2MTgwNzU2MzEwMzMxMzk5", "rest_id": "1326180756310331399", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1905090420142379008/Ydq5So7B_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Nov 10 15:13:32 +0000 2020", "name": "<PERSON>", "screen_name": "tom_doerr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Follow for posts about GitHub repos, DSPy, and agents\nSubscribe for top posts\nDM to share your AI project (Due to volume of DMs I'll prioritize subscribers)", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "tom-doerr.github.io/repo_posts/", "expanded_url": "https://tom-doerr.github.io/repo_posts/", "url": "https://t.co/WreHYiW9xe", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 52917, "followers_count": 84560, "friends_count": 2135, "has_custom_timelines": true, "is_translator": false, "listed_count": 808, "media_count": 6792, "normal_followers_count": 84560, "pinned_tweet_ids_str": ["1886002942962024635"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1326180756310331399/1721336045", "profile_interstitial_type": "", "statuses_count": 20559, "translator_type": "none", "url": "https://t.co/WreHYiW9xe", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1876432435618951326"], "editable_until_msecs": "1736214929000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "64936", "state": "EnabledWithCount"}, "source": "<a href=\"https://composio.dev/\" rel=\"nofollow\">Composio</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1151, "bookmarked": true, "created_at": "<PERSON><PERSON> Jan 07 00:55:29 +0000 2025", "conversation_id_str": "1876432435618951326", "display_text_range": [0, 205], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/sR3w9hMtcC", "expanded_url": "https://x.com/tom_doerr/status/1876432435618951326/photo/1", "id_str": "1876432402345521154", "indices": [206, 229], "media_key": "3_1876432402345521154", "media_url_https": "https://pbs.twimg.com/media/GgptVALWcAIiOux.png", "type": "photo", "url": "https://t.co/sR3w9hMtcC", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 686, "w": 740, "resize": "fit"}, "medium": {"h": 686, "w": 740, "resize": "fit"}, "small": {"h": 630, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 686, "width": 740, "focus_rects": [{"x": 0, "y": 70, "w": 740, "h": 414}, {"x": 0, "y": 0, "w": 686, "h": 686}, {"x": 0, "y": 0, "w": 602, "h": 686}, {"x": 0, "y": 0, "w": 343, "h": 686}, {"x": 0, "y": 0, "w": 740, "h": 686}]}, "media_results": {"result": {"media_key": "3_1876432402345521154"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/sR3w9hMtcC", "expanded_url": "https://x.com/tom_doerr/status/1876432435618951326/photo/1", "id_str": "1876432402345521154", "indices": [206, 229], "media_key": "3_1876432402345521154", "media_url_https": "https://pbs.twimg.com/media/GgptVALWcAIiOux.png", "type": "photo", "url": "https://t.co/sR3w9hMtcC", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 686, "w": 740, "resize": "fit"}, "medium": {"h": 686, "w": 740, "resize": "fit"}, "small": {"h": 630, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 686, "width": 740, "focus_rects": [{"x": 0, "y": 70, "w": 740, "h": 414}, {"x": 0, "y": 0, "w": 686, "h": 686}, {"x": 0, "y": 0, "w": 602, "h": 686}, {"x": 0, "y": 0, "w": 343, "h": 686}, {"x": 0, "y": 0, "w": 740, "h": 686}]}, "media_results": {"result": {"media_key": "3_1876432402345521154"}}}]}, "favorite_count": 1254, "favorited": false, "full_text": "Video2X is an open-source tool that uses machine learning to upscale video resolution, utilizing algorithms like waifu2X and Anime4K, and supports frame interpolation and audio/subtitle stream preservation https://t.co/sR3w9hMtcC", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 3, "reply_count": 8, "retweet_count": 135, "retweeted": false, "user_id_str": "1326180756310331399", "id_str": "1876432435618951326"}, "twe_private_fields": {"created_at": 1736211329000, "updated_at": 1748554112350, "media_count": 1}}}]