[{"id": "1523042875780796416", "created_at": "2022-05-07 23:51:33 +03:00", "full_text": "@MintLynxx @premierleague Exactly they are woeful for that too!!\n\nThat is also something I bang on about a lot too hahaha!\n\nSee my talk at the Statsbomb conference for more information on deflections!\n\nhttps://t.co/iWRoZuxShn", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1523042552085106689", "retweeted_status": null, "quoted_status": null, "favorite_count": 38, "retweet_count": 1, "bookmark_count": 10, "quote_count": 0, "reply_count": 1, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1523042875780796416", "metadata": {"__typename": "Tweet", "rest_id": "1523042875780796416", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTQzMDg2MDIz", "rest_id": "1143086023", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1218236580789456906/UQ6v1p3g_normal.jpg"}, "core": {"created_at": "Sat Feb 02 18:18:06 +0000 2013", "name": "<PERSON>", "screen_name": "Jhdharrison1"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "⚽️📊🧤 Head of Data Science @goalkeeper_com - University of Cambridge Astronomy PhD graduate - Data featured in @TheAthleticFC & @SkySportsPL", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 9493, "followers_count": 37945, "friends_count": 902, "has_custom_timelines": true, "is_translator": false, "listed_count": 453, "media_count": 2174, "normal_followers_count": 37945, "pinned_tweet_ids_str": ["1590038173543170048"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1143086023/1682342997", "profile_interstitial_type": "", "statuses_count": 11715, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "London/Outerspace"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/iWRoZuxShn", "legacy": {"binding_values": [{"key": "player_url", "value": {"string_value": "https://www.youtube.com/embed/bMo3AV2vLNM", "type": "STRING"}}, {"key": "player_image_large", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "player_image", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.67902", "type": "STRING"}}, {"key": "description", "value": {"string_value": "<PERSON> & John <PERSON> present their findings about how using StatsBomb 360 data canbe used to find and train the best goalkeeper for your team. This...", "type": "STRING"}}, {"key": "player_width", "value": {"string_value": "1280", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "www.youtube.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "10228272", "path": []}}}, {"key": "app_num_ratings", "value": {"string_value": "42,060,347", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "player_height", "value": {"string_value": "720", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "youtube.com", "type": "STRING"}}, {"key": "app_name", "value": {"string_value": "YouTube", "type": "STRING"}}, {"key": "player_image_small", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "title", "value": {"string_value": "StatsBomb Conference 2021: Splitting GSAA: Finding the best shot-st...", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/iWRoZuxShn", "type": "STRING"}}, {"key": "player_image_x_large", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "player", "url": "https://t.co/iWRoZuxShn", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMDIyODI3Mg==", "rest_id": "10228272", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915882040353837056/VbhPvueq_normal.jpg"}, "core": {"created_at": "Tue Nov 13 21:43:46 +0000 2007", "name": "YouTube", "screen_name": "YouTube"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "like & subscribe", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yt.be/3NDRz", "expanded_url": "https://yt.be/3NDRz", "url": "https://t.co/WBV5E1Rh1y", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6069, "followers_count": 79009633, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 77696, "media_count": 16005, "normal_followers_count": 79009633, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/10228272/1745416765", "profile_interstitial_type": "", "statuses_count": 60001, "translator_type": "regular", "url": "https://t.co/WBV5E1Rh1y", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Bruno, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1523042875780796416"], "editable_until_msecs": "1651958493000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 10, "bookmarked": true, "created_at": "Sat May 07 20:51:33 +0000 2022", "conversation_id_str": "1523040982450589696", "display_text_range": [26, 225], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/bMo3AV2vLNM", "expanded_url": "https://youtu.be/bMo3AV2vLNM", "url": "https://t.co/iWRoZuxShn", "indices": [202, 225]}], "user_mentions": [{"id_str": "2966637004", "name": "Lynxx", "screen_name": "MintLynxx", "indices": [0, 10]}, {"id_str": "343627165", "name": "Premier League", "screen_name": "premierleague", "indices": [11, 25]}]}, "favorite_count": 38, "favorited": false, "full_text": "@MintLynxx @premierleague Exactly they are woeful for that too!!\n\nThat is also something I bang on about a lot too hahaha!\n\nSee my talk at the Statsbomb conference for more information on deflections!\n\nhttps://t.co/iWRoZuxShn", "in_reply_to_screen_name": "MintLynxx", "in_reply_to_status_id_str": "1523042552085106689", "in_reply_to_user_id_str": "2966637004", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 1, "retweet_count": 1, "retweeted": false, "user_id_str": "1143086023", "id_str": "1523042875780796416"}, "twe_private_fields": {"created_at": 1651956693000, "updated_at": 1748554346019, "media_count": 0}}}, {"id": "1523048557829263363", "created_at": "2022-05-08 00:14:07 +03:00", "full_text": "@coinflipfpl @premierleague I will find it tomorrow \n\nBut from experience it will be way more sensible than Opta but will still under estimate it (SBs xG will be excellent but their PSxG for some reason isn’t quite as reliable!)\n\nFor more comparing Opta &amp; SB &amp; others see this video:\n\nhttps://t.co/iWRoZufJ3f", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1523046777368285185", "retweeted_status": null, "quoted_status": null, "favorite_count": 3, "retweet_count": 0, "bookmark_count": 1, "quote_count": 0, "reply_count": 1, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1523048557829263363", "metadata": {"__typename": "Tweet", "rest_id": "1523048557829263363", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMTQzMDg2MDIz", "rest_id": "1143086023", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1218236580789456906/UQ6v1p3g_normal.jpg"}, "core": {"created_at": "Sat Feb 02 18:18:06 +0000 2013", "name": "<PERSON>", "screen_name": "Jhdharrison1"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "⚽️📊🧤 Head of Data Science @goalkeeper_com - University of Cambridge Astronomy PhD graduate - Data featured in @TheAthleticFC & @SkySportsPL", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 9493, "followers_count": 37945, "friends_count": 902, "has_custom_timelines": true, "is_translator": false, "listed_count": 453, "media_count": 2174, "normal_followers_count": 37945, "pinned_tweet_ids_str": ["1590038173543170048"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1143086023/1682342997", "profile_interstitial_type": "", "statuses_count": 11715, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "London/Outerspace"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/iWRoZufJ3f", "legacy": {"binding_values": [{"key": "player_url", "value": {"string_value": "https://www.youtube.com/embed/bMo3AV2vLNM", "type": "STRING"}}, {"key": "player_image_large", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "player_image", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "app_star_rating", "value": {"string_value": "4.67902", "type": "STRING"}}, {"key": "description", "value": {"string_value": "<PERSON> & John <PERSON> present their findings about how using StatsBomb 360 data canbe used to find and train the best goalkeeper for your team. This...", "type": "STRING"}}, {"key": "player_width", "value": {"string_value": "1280", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "www.youtube.com", "type": "STRING"}}, {"key": "app_is_free", "value": {"string_value": "true", "type": "STRING"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "10228272", "path": []}}}, {"key": "app_num_ratings", "value": {"string_value": "42,060,347", "type": "STRING"}}, {"key": "app_price_amount", "value": {"string_value": "0.0", "type": "STRING"}}, {"key": "player_height", "value": {"string_value": "720", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "youtube.com", "type": "STRING"}}, {"key": "app_name", "value": {"string_value": "YouTube", "type": "STRING"}}, {"key": "player_image_small", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}, {"key": "title", "value": {"string_value": "StatsBomb Conference 2021: Splitting GSAA: Finding the best shot-st...", "type": "STRING"}}, {"key": "app_price_currency", "value": {"string_value": "USD", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/iWRoZufJ3f", "type": "STRING"}}, {"key": "player_image_x_large", "value": {"image_value": {"height": 197, "width": 351, "url": "https://pbs.twimg.com/cards/player-placeholder.png"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "player", "url": "https://t.co/iWRoZufJ3f", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMDIyODI3Mg==", "rest_id": "10228272", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1915882040353837056/VbhPvueq_normal.jpg"}, "core": {"created_at": "Tue Nov 13 21:43:46 +0000 2007", "name": "YouTube", "screen_name": "YouTube"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "like & subscribe", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "yt.be/3NDRz", "expanded_url": "https://yt.be/3NDRz", "url": "https://t.co/WBV5E1Rh1y", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6069, "followers_count": 79009633, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 77696, "media_count": 16005, "normal_followers_count": 79009633, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/10228272/1745416765", "profile_interstitial_type": "", "statuses_count": 60001, "translator_type": "regular", "url": "https://t.co/WBV5E1Rh1y", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Bruno, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1523048557829263363"], "editable_until_msecs": "1651959847000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1, "bookmarked": true, "created_at": "Sat May 07 21:14:07 +0000 2022", "conversation_id_str": "1523040982450589696", "display_text_range": [28, 316], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtu.be/bMo3AV2vLNM", "expanded_url": "https://youtu.be/bMo3AV2vLNM", "url": "https://t.co/iWRoZufJ3f", "indices": [293, 316]}], "user_mentions": [{"id_str": "343627165", "name": "Premier League", "screen_name": "premierleague", "indices": [13, 27]}]}, "favorite_count": 3, "favorited": false, "full_text": "@coinflipfpl @premierleague I will find it tomorrow \n\nBut from experience it will be way more sensible than Opta but will still under estimate it (SBs xG will be excellent but their PSxG for some reason isn’t quite as reliable!)\n\nFor more comparing Opta &amp; SB &amp; others see this video:\n\nhttps://t.co/iWRoZufJ3f", "in_reply_to_screen_name": "coinflipgoesbrr", "in_reply_to_status_id_str": "1523046777368285185", "in_reply_to_user_id_str": "1175709723506675713", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "1143086023", "id_str": "1523048557829263363"}, "twe_private_fields": {"created_at": 1651958047000, "updated_at": 1748554346019, "media_count": 0}}}]