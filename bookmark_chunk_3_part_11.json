[{"id": "1532066608575021056", "created_at": "2022-06-01 21:28:38 +03:00", "full_text": "Web development is a book of 10 chapters:\n\nPart 1: HTML 🧱\nPart 2: CSS 🎨\nPart 3: CSS frameworks 🧮\nPart 4: JS ⚙️\nPart 5: DOM 📃\nPart 6: Git and GitHub 🗂️\nPart 7: React/Angular/Vue ⚛️\nPart 8: Node.js 🔙🔚\nPart 9: API - @Rapid_API 🖇\nPart 10: Database 🗄️", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3328, "retweet_count": 731, "bookmark_count": 879, "quote_count": 28, "reply_count": 100, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532066608575021056", "metadata": {"__typename": "Tweet", "rest_id": "1532066608575021056", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTYxNTU1OTI1NzgxNTg1OTI=", "rest_id": "856155592578158592", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1612507803842932736/PLiJMD_l_normal.jpg"}, "core": {"created_at": "Sun Apr 23 14:39:34 +0000 2017", "name": "<PERSON><PERSON><PERSON>", "screen_name": "Prathku<PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I talk about web and social • DevRel @APILayer • Building https://t.co/niju9j3UA2 & https://t.co/TxBXHrPKDu • Prev @Rapid_API @HyperspaceAI", "entities": {"description": {"urls": [{"display_url": "TrioTech.Dev", "expanded_url": "http://TrioTech.Dev", "url": "https://t.co/niju9j3UA2", "indices": [58, 81]}, {"display_url": "RoastProfile.Social", "expanded_url": "http://RoastProfile.Social", "url": "https://t.co/TxBXHrPKDu", "indices": [84, 107]}]}, "url": {"urls": [{"display_url": "PrathamKumar.com", "expanded_url": "https://www.PrathamKumar.com", "url": "https://t.co/gGjMe9QuIY", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 148812, "followers_count": 435982, "friends_count": 891, "has_custom_timelines": true, "is_translator": false, "listed_count": 8454, "media_count": 5769, "normal_followers_count": 435982, "pinned_tweet_ids_str": ["1917933782612496654"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/856155592578158592/1696346737", "profile_interstitial_type": "", "statuses_count": 34425, "translator_type": "none", "url": "https://t.co/gGjMe9QuIY", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "New Delhi, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1473320674487721988", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "", "patreon_handle": ""}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532066608575021056"], "editable_until_msecs": "1654109918000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 879, "bookmarked": true, "created_at": "Wed Jun 01 18:28:38 +0000 2022", "conversation_id_str": "1532066608575021056", "display_text_range": [0, 246], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "4866498934", "name": "Rapid", "screen_name": "Rapid_API", "indices": [213, 223]}]}, "favorite_count": 3328, "favorited": false, "full_text": "Web development is a book of 10 chapters:\n\nPart 1: HTML 🧱\nPart 2: CSS 🎨\nPart 3: CSS frameworks 🧮\nPart 4: JS ⚙️\nPart 5: DOM 📃\nPart 6: Git and GitHub 🗂️\nPart 7: React/Angular/Vue ⚛️\nPart 8: Node.js 🔙🔚\nPart 9: API - @Rapid_API 🖇\nPart 10: Database 🗄️", "is_quote_status": false, "lang": "en", "quote_count": 28, "reply_count": 100, "retweet_count": 731, "retweeted": false, "user_id_str": "856155592578158592", "id_str": "1532066608575021056"}, "twe_private_fields": {"created_at": 1654108118000, "updated_at": 1748554271915, "media_count": 0}}}, {"id": "1532239085351407616", "created_at": "2022-06-02 08:54:00 +03:00", "full_text": "How I would study Data Analytics if I had to start over :\n📚 Excel : Youtube + Projects\n📚 Tableau : Youtube + Projects\n📚 SQL : SQLBolt + Case Studies\n📚 Python : learnpython .org + Projects\n📚 Portfolio building", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4526, "retweet_count": 1239, "bookmark_count": 2029, "quote_count": 31, "reply_count": 91, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1532239085351407616", "metadata": {"__typename": "Tweet", "rest_id": "1532239085351407616", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzAwODA1MDkzMjEwNTU0MzY4", "rest_id": "1300805093210554368", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1421773130419236867/4_S-_L2i_normal.jpg"}, "core": {"created_at": "Tue Sep 01 14:38:15 +0000 2020", "name": "Analytical Aakriti | The SQL Gal", "screen_name": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Open for DevRel roles!\nData Nerd | Technical Writer | SQL Obsessed | Python Possessed | Let's dig into data the simple, effective and no nonsense way 📊", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/aakriti_sharma", "expanded_url": "https://linktr.ee/aakriti_sharma", "url": "https://t.co/m4DVhDKg9b", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1962, "followers_count": 72439, "friends_count": 159, "has_custom_timelines": true, "is_translator": false, "listed_count": 656, "media_count": 318, "normal_followers_count": 72439, "pinned_tweet_ids_str": ["1565581836629458944"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1300805093210554368/1665297269", "profile_interstitial_type": "", "statuses_count": 2649, "translator_type": "none", "url": "https://t.co/m4DVhDKg9b", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Mumbai, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460810126042882049", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1532239085351407616"], "editable_until_msecs": "1654151040000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2029, "bookmarked": true, "created_at": "Thu Jun 02 05:54:00 +0000 2022", "conversation_id_str": "1532239085351407616", "display_text_range": [0, 208], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 4526, "favorited": false, "full_text": "How I would study Data Analytics if I had to start over :\n📚 Excel : Youtube + Projects\n📚 Tableau : Youtube + Projects\n📚 SQL : SQLBolt + Case Studies\n📚 Python : learnpython .org + Projects\n📚 Portfolio building", "is_quote_status": false, "lang": "en", "quote_count": 31, "reply_count": 91, "retweet_count": 1239, "retweeted": false, "user_id_str": "1300805093210554368", "id_str": "1532239085351407616"}, "twe_private_fields": {"created_at": 1654149240000, "updated_at": 1748554271915, "media_count": 0}}}]