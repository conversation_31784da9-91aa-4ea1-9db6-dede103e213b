[{"id": "1558694634255106054", "created_at": "2022-08-14 08:58:54 +03:00", "full_text": "5 Best Courses to Learn Data Structure\n1. Data Structures Deep Dive Using Java - https://t.co/B6UKy9YjBi\n2. DSA by <PERSON> - https://t.co/eg4jYMlxGw\n3. Algorithms  - https://t.co/fmd5G9Tdhl\n3. Data Structures - https://t.co/NXkh4xROaJ\n4. DSA in Python - https://t.co/XE5YSs4oL9 https://t.co/AI5jquBSKf", "media": [{"type": "photo", "url": "https://t.co/AI5jquBSKf", "thumbnail": "https://pbs.twimg.com/media/FaGXNGyUEAEuOEY?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FaGXNGyUEAEuOEY?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1011, "retweet_count": 275, "bookmark_count": 927, "quote_count": 4, "reply_count": 22, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1558694634255106054", "metadata": {"__typename": "Tweet", "rest_id": "1558694634255106054", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMDY2NDU1OTg=", "rest_id": "206645598", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1114805556370231296/TJSKV7LD_normal.png"}, "core": {"created_at": "Sat Oct 23 12:04:02 +0000 2010", "name": "javi<PERSON><PERSON><PERSON>", "screen_name": "javi<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Blogger - https://t.co/Cxgp9zzN3y\nCreator - https://t.co/GYls4Lx9DW\nnewsletter - https://t.co/P8jiQ5GW16\nyoutube - https://t.co/vs4WjwaEQ6", "entities": {"description": {"urls": [{"display_url": "java67.com", "expanded_url": "http://www.java67.com", "url": "https://t.co/Cxgp9zzN3y", "indices": [10, 33]}, {"display_url": "gumroad.com/javinpaul", "expanded_url": "http://gumroad.com/javinpaul", "url": "https://t.co/GYls4Lx9DW", "indices": [44, 67]}, {"display_url": "bit.ly/4539gIx", "expanded_url": "https://bit.ly/4539gIx", "url": "https://t.co/P8jiQ5GW16", "indices": [81, 104]}, {"display_url": "bit.ly/2H20FjD", "expanded_url": "https://bit.ly/2H20FjD", "url": "https://t.co/vs4WjwaEQ6", "indices": [115, 138]}]}, "url": {"urls": [{"display_url": "javarevisited.blogspot.com", "expanded_url": "http://javarevisited.blogspot.com", "url": "https://t.co/DoVkv5Ltt8", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 25130, "followers_count": 101688, "friends_count": 7230, "has_custom_timelines": true, "is_translator": false, "listed_count": 2208, "media_count": 9475, "normal_followers_count": 101688, "pinned_tweet_ids_str": ["1918566083558277336"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/206645598/1636800233", "profile_interstitial_type": "", "statuses_count": 65585, "translator_type": "none", "url": "https://t.co/DoVkv5Ltt8", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "New York, USA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1558694634255106054"], "editable_until_msecs": "1660458534000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 927, "bookmarked": true, "created_at": "Sun Aug 14 05:58:54 +0000 2022", "conversation_id_str": "1558694634255106054", "display_text_range": [0, 276], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/AI5jquBSKf", "expanded_url": "https://x.com/javinpaul/status/1558694634255106054/photo/1", "id_str": "1558693197462638593", "indices": [277, 300], "media_key": "3_1558693197462638593", "media_url_https": "https://pbs.twimg.com/media/FaGXNGyUEAEuOEY.jpg", "type": "photo", "url": "https://t.co/AI5jquBSKf", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 667, "w": 1024, "resize": "fit"}, "medium": {"h": 667, "w": 1024, "resize": "fit"}, "small": {"h": 443, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 667, "width": 1024, "focus_rects": [{"x": 0, "y": 0, "w": 1024, "h": 573}, {"x": 102, "y": 0, "w": 667, "h": 667}, {"x": 143, "y": 0, "w": 585, "h": 667}, {"x": 268, "y": 0, "w": 334, "h": 667}, {"x": 0, "y": 0, "w": 1024, "h": 667}]}, "media_results": {"result": {"media_key": "3_1558693197462638593"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "pic.x.com/AI5jquBSKf", "expanded_url": "https://x.com/javinpaul/status/1558694634255106054/photo/1", "url": "https://t.co/AI5jquBSKf", "indices": [277, 300]}, {"display_url": "bit.ly/3w5uDtU", "expanded_url": "https://bit.ly/3w5uDtU", "url": "https://t.co/NXkh4xROaJ", "indices": [210, 233]}, {"display_url": "bit.ly/3P45Gqi", "expanded_url": "https://bit.ly/3P45Gqi", "url": "https://t.co/fmd5G9Tdhl", "indices": [165, 188]}, {"display_url": "bit.ly/3JRhqKK", "expanded_url": "https://bit.ly/3JRhqKK", "url": "https://t.co/XE5YSs4oL9", "indices": [253, 276]}, {"display_url": "bit.ly/3JOjH8v", "expanded_url": "https://bit.ly/3JOjH8v", "url": "https://t.co/eg4jYMlxGw", "indices": [124, 147]}, {"display_url": "bit.ly/3QH8Y2R", "expanded_url": "https://bit.ly/3QH8Y2R", "url": "https://t.co/B6UKy9YjBi", "indices": [81, 104]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/AI5jquBSKf", "expanded_url": "https://x.com/javinpaul/status/1558694634255106054/photo/1", "id_str": "1558693197462638593", "indices": [277, 300], "media_key": "3_1558693197462638593", "media_url_https": "https://pbs.twimg.com/media/FaGXNGyUEAEuOEY.jpg", "type": "photo", "url": "https://t.co/AI5jquBSKf", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 667, "w": 1024, "resize": "fit"}, "medium": {"h": 667, "w": 1024, "resize": "fit"}, "small": {"h": 443, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 667, "width": 1024, "focus_rects": [{"x": 0, "y": 0, "w": 1024, "h": 573}, {"x": 102, "y": 0, "w": 667, "h": 667}, {"x": 143, "y": 0, "w": 585, "h": 667}, {"x": 268, "y": 0, "w": 334, "h": 667}, {"x": 0, "y": 0, "w": 1024, "h": 667}]}, "media_results": {"result": {"media_key": "3_1558693197462638593"}}}]}, "favorite_count": 1011, "favorited": false, "full_text": "5 Best Courses to Learn Data Structure\n1. Data Structures Deep Dive Using Java - https://t.co/B6UKy9YjBi\n2. DSA by <PERSON> - https://t.co/eg4jYMlxGw\n3. Algorithms  - https://t.co/fmd5G9Tdhl\n3. Data Structures - https://t.co/NXkh4xROaJ\n4. DSA in Python - https://t.co/XE5YSs4oL9 https://t.co/AI5jquBSKf", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 4, "reply_count": 22, "retweet_count": 275, "retweeted": false, "user_id_str": "206645598", "id_str": "1558694634255106054"}, "twe_private_fields": {"created_at": 1660456734000, "updated_at": 1748554255574, "media_count": 1}}}, {"id": "1558769559934586881", "created_at": "2022-08-14 13:56:38 +03:00", "full_text": "Sharing is caring\n\nHere are a few tricks to work faster with Excel.\nExcel remains a useful tool for quick manipulation of datasets, checks of values, stats, and transformations\n\nCredit: 7-Second Riddles\n#automation #excel #maths #science https://t.co/nPHEL9XAfY", "media": [{"type": "video", "url": "https://t.co/nPHEL9XAfY", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1558769485301022720/pu/img/XYFj2aAhjDGAou7L.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/vid/400x400/vCm_DhFpA-LmwiYw.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3279, "retweet_count": 1067, "bookmark_count": 1345, "quote_count": 14, "reply_count": 27, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1558769559934586881", "metadata": {"__typename": "Tweet", "rest_id": "1558769559934586881", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo1MTE0Mjc4NQ==", "rest_id": "51142785", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1582765436101283845/67AuGZr2_normal.jpg"}, "core": {"created_at": "Fri Jun 26 17:24:02 +0000 2009", "name": "<PERSON>", "screen_name": "pascal_bornet"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Top Voice in Tech | Best-selling Author | Keynote speaker | AI & Automation expert | Forbes Tech Council | 1 Million+ followers", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pascalbornet.com", "expanded_url": "https://www.pascalbornet.com", "url": "https://t.co/85JoXPwuvA", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 61803, "followers_count": 138653, "friends_count": 932, "has_custom_timelines": true, "is_translator": false, "listed_count": 804, "media_count": 3442, "normal_followers_count": 138653, "pinned_tweet_ids_str": ["1901604346024145224"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/51142785/1745362713", "profile_interstitial_type": "", "statuses_count": 6053, "translator_type": "none", "url": "https://t.co/85JoXPwuvA", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Miami, FL"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1558769559934586881"], "editable_until_msecs": "1660476398000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.hootsuite.com\" rel=\"nofollow\">Hootsuite Inc.</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1345, "bookmarked": true, "created_at": "Sun Aug 14 10:56:38 +0000 2022", "conversation_id_str": "1558769559934586881", "display_text_range": [0, 237], "entities": {"hashtags": [{"indices": [203, 214], "text": "automation"}, {"indices": [215, 221], "text": "excel"}, {"indices": [222, 228], "text": "maths"}, {"indices": [229, 237], "text": "science"}], "media": [{"display_url": "pic.x.com/nPHEL9XAfY", "expanded_url": "https://x.com/pascal_bornet/status/1558769559934586881/video/1", "id_str": "1558769485301022720", "indices": [238, 261], "media_key": "7_1558769485301022720", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1558769485301022720/pu/img/XYFj2aAhjDGAou7L.jpg", "type": "video", "url": "https://t.co/nPHEL9XAfY", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 400, "w": 400, "resize": "fit"}, "medium": {"h": 400, "w": 400, "resize": "fit"}, "small": {"h": 400, "w": 400, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 400, "width": 400, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "duration_millis": 85952, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/pl/dynF8w4Bueh4SGbP.m3u8?tag=12&v=030"}, {"bitrate": 432000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/vid/320x320/dgPoSP6YOgYRNRal.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/vid/400x400/vCm_DhFpA-LmwiYw.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1558769485301022720"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/nPHEL9XAfY", "expanded_url": "https://x.com/pascal_bornet/status/1558769559934586881/video/1", "id_str": "1558769485301022720", "indices": [238, 261], "media_key": "7_1558769485301022720", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1558769485301022720/pu/img/XYFj2aAhjDGAou7L.jpg", "type": "video", "url": "https://t.co/nPHEL9XAfY", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 400, "w": 400, "resize": "fit"}, "medium": {"h": 400, "w": 400, "resize": "fit"}, "small": {"h": 400, "w": 400, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 400, "width": 400, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "duration_millis": 85952, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/pl/dynF8w4Bueh4SGbP.m3u8?tag=12&v=030"}, {"bitrate": 432000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/vid/320x320/dgPoSP6YOgYRNRal.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1558769485301022720/pu/vid/400x400/vCm_DhFpA-LmwiYw.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1558769485301022720"}}}]}, "favorite_count": 3279, "favorited": false, "full_text": "Sharing is caring\n\nHere are a few tricks to work faster with Excel.\nExcel remains a useful tool for quick manipulation of datasets, checks of values, stats, and transformations\n\nCredit: 7-Second Riddles\n#automation #excel #maths #science https://t.co/nPHEL9XAfY", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 14, "reply_count": 27, "retweet_count": 1067, "retweeted": false, "user_id_str": "51142785", "id_str": "1558769559934586881"}, "twe_private_fields": {"created_at": 1660474598000, "updated_at": 1748554247988, "media_count": 1}}}]