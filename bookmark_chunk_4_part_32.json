[{"id": "1698312511572058473", "created_at": "2023-09-03 15:30:30 +03:00", "full_text": "Best YouTube Channels for Programming:\n\n❯ C ➟ Jacob Sorber\n❯ C++ ➟ TheCherno\n❯ Python ➟ Corey Schafer\n❯ JavaScript ➟ developedbyed\n❯ Java ➟ amigoscode\n❯ C# ➟ kudvenkat\n❯ Golang ➟ Jon Calhoun\n❯ Swift ➟ CodeWithChris\n❯ Kotlin ➟ PhilippLackner\n❯ PHP ➟ ProgramWithGio\n❯ Dart ➟ Flutterly\n❯ Ruby ➟ DriftingRuby\n❯ Rust ➟ NoBoilerplate\n❯ TypeScript ➟ Matt Pocock\n❯ Lua ➟ Steve's teacher\n❯ R ➟ marinstatlectures\n❯ SQL ➟ Joey Blue\n\n❯ C++ ➟ javidx9\n❯ JavaScript ➟ Akshay Saini\n❯ TypeScript ➟ basarat\n❯ TypeScript ➟ TypeScriptTV\n❯ C# ➟ Microsoft Developer [Bob Tabor]\n❯ C# ➟ dotnet [Scott/Kendra]\n\n              -- Frameworks --\n\n❯ Node.js ➟ Traversy Media\n❯ React ➟ Dave Gray\n❯ React ➟ Jack Herrington\n❯ Next.js ➟ Lama Dev\n❯ Vue ➟ Vue Mastery\n❯ Svelte ➟ Joy of Code\n❯ Angular ➟ Angular University\n❯ Django ➟ CodingEntrepreneurs\n❯ Laravel ➟ LaravelDaily\n❯ Blazor ➟ James Montemagno\n❯ Spring ➟ SpringSourceDev\n❯ SpringBoot ➟ amigoscode\n❯ Ruby on Rails ➟ GorailsTV\n❯ Flutter ➟ The Flutter Way\n❯ Flutter ➟ Tadas Petra\n\n                 -- Special Mentions --\n\n❯ Programming in 100 Sec ➟ Fireship\n❯ DSA ➟ take U forward\n❯ Interviews ➟ NeetCode\n❯ Projects ➟ JavaScript King\n\n                    -- Code Editors --\n\n❯ Vim ➟ ThePrimeagen\n❯ VS Code ➟ Visual Studio Code\n❯ Jupyter Notebook ➟ Corey Schafer\n\n                   -- Free Education --\n\n  ➟ freecodecamp\n  ➟ Simplilearn\n  ➟ edureka!\n\n                       -- Allrounders --\n\n   ➟ TechWithTim\n   ➟ WebDevSimplified\n   ➟ programmingwithmosh\n   ➟ Traversy Media\n   ➟ BroCodez\n   ➟ thenewboston\n   ➟ Telusko\n   ➟ Derek Banas\n   ➟ CodeWithHarry\n   ➟ MySirG .com", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3096, "retweet_count": 979, "bookmark_count": 4757, "quote_count": 18, "reply_count": 84, "views_count": 484564, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1698312511572058473", "metadata": {"__typename": "Tweet", "rest_id": "1698312511572058473", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204228, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204228, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1698312511572058473"], "editable_until_msecs": "1693747830000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "484564", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE2OTgzMTI1MTEzNjY1NDEzMTI=", "text": "Best YouTube Channels for Programming:\n\n❯ C ➟ Jacob Sorber\n❯ C++ ➟ TheCherno\n❯ Python ➟ Corey Schafer\n❯ JavaScript ➟ developedbyed\n❯ Java ➟ amigoscode\n❯ C# ➟ kudvenkat\n❯ Golang ➟ Jon Calhoun\n❯ Swift ➟ CodeWithChris\n❯ Kotlin ➟ PhilippLackner\n❯ PHP ➟ ProgramWithGio\n❯ Dart ➟ Flutterly\n❯ Ruby ➟ DriftingRuby\n❯ Rust ➟ NoBoilerplate\n❯ TypeScript ➟ Matt Pocock\n❯ Lua ➟ Steve's teacher\n❯ R ➟ marinstatlectures\n❯ SQL ➟ Joey Blue\n\n❯ C++ ➟ javidx9\n❯ JavaScript ➟ Akshay Saini\n❯ TypeScript ➟ basarat\n❯ TypeScript ➟ TypeScriptTV\n❯ C# ➟ Microsoft Developer [Bob Tabor]\n❯ C# ➟ dotnet [Scott/Kendra]\n\n              -- Frameworks --\n\n❯ Node.js ➟ Traversy Media\n❯ React ➟ Dave Gray\n❯ React ➟ Jack Herrington\n❯ Next.js ➟ Lama Dev\n❯ Vue ➟ Vue Mastery\n❯ Svelte ➟ Joy of Code\n❯ Angular ➟ Angular University\n❯ Django ➟ CodingEntrepreneurs\n❯ Laravel ➟ LaravelDaily\n❯ Blazor ➟ James Montemagno\n❯ Spring ➟ SpringSourceDev\n❯ SpringBoot ➟ amigoscode\n❯ Ruby on Rails ➟ GorailsTV\n❯ Flutter ➟ The Flutter Way\n❯ Flutter ➟ Tadas Petra\n\n                 -- Special Mentions --\n\n❯ Programming in 100 Sec ➟ Fireship\n❯ DSA ➟ take U forward\n❯ Interviews ➟ NeetCode\n❯ Projects ➟ JavaScript King\n\n                    -- Code Editors --\n\n❯ Vim ➟ ThePrimeagen\n❯ VS Code ➟ Visual Studio Code\n❯ Jupyter Notebook ➟ Corey Schafer\n\n                   -- Free Education --\n\n  ➟ freecodecamp\n  ➟ Simplilearn\n  ➟ edureka!\n\n                       -- Allrounders --\n\n   ➟ TechWithTim\n   ➟ WebDevSimplified\n   ➟ programmingwithmosh\n   ➟ Traversy Media\n   ➟ BroCodez\n   ➟ thenewboston\n   ➟ Telusko\n   ➟ Derek Banas\n   ➟ CodeWithHarry\n   ➟ MySirG .com", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 603, "to_index": 613, "richtext_types": ["Bold"]}, {"from_index": 1024, "to_index": 1040, "richtext_types": ["Bold"]}, {"from_index": 1181, "to_index": 1193, "richtext_types": ["Bold"]}, {"from_index": 1308, "to_index": 1322, "richtext_types": ["Bold"]}, {"from_index": 1400, "to_index": 1411, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 4757, "bookmarked": true, "created_at": "Sun Sep 03 12:30:30 +0000 2023", "conversation_id_str": "1698312511572058473", "display_text_range": [0, 248], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 3096, "favorited": false, "full_text": "Best YouTube Channels for Programming:\n\n❯ C ➟ Jacob Sorber\n❯ C++ ➟ TheCherno\n❯ Python ➟ <PERSON>\n❯ JavaScript ➟ developedbyed\n❯ Java ➟ amigoscode\n❯ C# ➟ kudvenkat\n❯ Golang ➟ Jon <PERSON>\n❯ Swift ➟ CodeWithChris\n❯ Kotlin ➟ PhilippLackner\n❯ PHP ➟", "is_quote_status": false, "lang": "en", "quote_count": 18, "reply_count": 84, "retweet_count": 979, "retweeted": false, "user_id_str": "69941978", "id_str": "1698312511572058473"}, "twe_private_fields": {"created_at": 1693744230000, "updated_at": 1748554188923, "media_count": 0}}}, {"id": "1699755709964128305", "created_at": "2023-09-07 15:05:15 +03:00", "full_text": "Bookmark these 50+ sites for lifetime:\n\n❯ C ➟ learn-c\n❯ C++ ➟ codecademy\n❯ Java ➟ baeldung\n❯ C# ➟ learncs\n❯ JavaScript ➟ javascript .info\n❯ Python ➟ realpython\n❯ PHP ➟ learn-php\n❯ Kotlin ➟ studytonight\n❯ Go ➟ learn-golang\n❯ Swift ➟ codewithchris\n❯ SQL ➟ sqlbolt\n❯ DSA ➟ techdevguide .withgoogle\n\n❯ Python ➟ kaggle\n❯ Java ➟ dzone\n❯ Java ➟ codecademy\n❯ JavaScript ➟ MDN\n❯ TypeScript ➟ codecademy\n❯ Go ➟ gobyexample\n❯ Go ➟ golangbot\n❯ Kotlin ➟ codecademy\n❯ Ruby ➟ rubyguides\n❯ Swift ➟ hackingwithswift\n❯ Swift ➟ codecademy\n\n❯ HTML and CSS ➟ learn-html\n❯ CSS ➟ css-tricks\n\n❯ React ➟ scrimba\n❯ React ➟ react-tutorial\n❯ Vue ➟ vuemastery\n❯ Vue ➟ learnvue\n❯ Vue ➟ scrimba\n❯ Ruby on Rails ➟ railstutorial\n❯ Spring ➟ baeldung\n❯ Spring ➟ journaldev\n❯ Spring ➟ dzone\n❯ Laravel ➟ laracasts\n\n❯ SQL ➟ codecademy\n❯ SQL ➟ sqltutorial\n❯ MySQL ➟ mysqltutorial\n❯ PostgreSQL ➟ postgresqltutorial\n❯ SQL Server ➟ sqlservertutorial\n❯ Oracle ➟ oracletutorial\n❯ MongoDB ➟ mongodbtutorial\n\n❯ REST ➟ restfulapi\n❯ GraphQL ➟ apollographql\n❯ Full Stack ➟ theodinproject\n❯ Backend ➟ theserverside\n❯ Microservices ➟ dzone\n❯ DevOps ➟ theserverside\n❯ Cloud ➟ dzone\n❯ Azure ➟ learn. microsoft\n\nThese popular sites provide learning content for multiple skills:\n\n❯ w3schools\n❯ tutorialspoint\n❯ programiz\n❯ geeksforgeeks\n❯ freecodecamp\n❯ studytonight\n❯ dzone\n❯ theserverside\n\n✧ Also follow the official docs. Those are sometimes the best places to learn.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1771, "retweet_count": 565, "bookmark_count": 3902, "quote_count": 12, "reply_count": 49, "views_count": 309513, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1699755709964128305", "metadata": {"__typename": "Tweet", "rest_id": "1699755709964128305", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204228, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204228, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1699755709964128305"], "editable_until_msecs": "1694091915000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "309513", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE2OTk3NTU3MDk3ODc5NDI5MTI=", "text": "Bookmark these 50+ sites for lifetime:\n\n❯ C ➟ learn-c\n❯ C++ ➟ codecademy\n❯ Java ➟ baeldung\n❯ C# ➟ learncs\n❯ JavaScript ➟ javascript .info\n❯ Python ➟ realpython\n❯ PHP ➟ learn-php\n❯ Kotlin ➟ studytonight\n❯ Go ➟ learn-golang\n❯ Swift ➟ codewithchris\n❯ SQL ➟ sqlbolt\n❯ DSA ➟ techdevguide .withgoogle\n\n❯ Python ➟ kaggle\n❯ Java ➟ dzone\n❯ Java ➟ codecademy\n❯ JavaScript ➟ MDN\n❯ TypeScript ➟ codecademy\n❯ Go ➟ gobyexample\n❯ Go ➟ golangbot\n❯ Kotlin ➟ codecademy\n❯ Ruby ➟ rubyguides\n❯ Swift ➟ hackingwithswift\n❯ Swift ➟ codecademy\n\n❯ HTML and CSS ➟ learn-html\n❯ CSS ➟ css-tricks\n\n❯ React ➟ scrimba\n❯ React ➟ react-tutorial\n❯ Vue ➟ vuemastery\n❯ Vue ➟ learnvue\n❯ Vue ➟ scrimba\n❯ Ruby on Rails ➟ railstutorial\n❯ Spring ➟ baeldung\n❯ Spring ➟ journaldev\n❯ Spring ➟ dzone\n❯ Laravel ➟ laracasts\n\n❯ SQL ➟ codecademy\n❯ SQL ➟ sqltutorial\n❯ MySQL ➟ mysqltutorial\n❯ PostgreSQL ➟ postgresqltutorial\n❯ SQL Server ➟ sqlservertutorial\n❯ Oracle ➟ oracletutorial\n❯ MongoDB ➟ mongodbtutorial\n\n❯ REST ➟ restfulapi\n❯ GraphQL ➟ apollographql\n❯ Full Stack ➟ theodinproject\n❯ Backend ➟ theserverside\n❯ Microservices ➟ dzone\n❯ DevOps ➟ theserverside\n❯ Cloud ➟ dzone\n❯ Azure ➟ learn. microsoft\n\nThese popular sites provide learning content for multiple skills:\n\n❯ w3schools\n❯ tutorialspoint\n❯ programiz\n❯ geeksforgeeks\n❯ freecodecamp\n❯ studytonight\n❯ dzone\n❯ theserverside\n\n✧ Also follow the official docs. Those are sometimes the best places to learn.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 0, "to_index": 37, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 3902, "bookmarked": true, "created_at": "Thu Sep 07 12:05:15 +0000 2023", "conversation_id_str": "1699755709964128305", "display_text_range": [0, 253], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1771, "favorited": false, "full_text": "Bookmark these 50+ sites for lifetime:\n\n❯ C ➟ learn-c\n❯ C++ ➟ codecademy\n❯ Java ➟ baeldung\n❯ C# ➟ learncs\n❯ JavaScript ➟ javascript .info\n❯ Python ➟ realpython\n❯ PHP ➟ learn-php\n❯ Kotlin ➟ studytonight\n❯ Go ➟ learn-golang\n❯ Swift ➟ codewithchris\n❯ SQL ➟", "is_quote_status": false, "lang": "en", "quote_count": 12, "reply_count": 49, "retweet_count": 565, "retweeted": false, "user_id_str": "69941978", "id_str": "1699755709964128305"}, "twe_private_fields": {"created_at": 1694088315000, "updated_at": 1748554188923, "media_count": 0}}}]