[{"id": "1801605083731210477", "created_at": "2024-06-14 16:18:18 +03:00", "full_text": "This sample Python code will show you step-by-step \nhow to clean the following 'messy' Healthcare dataset \nas a beginner in Data  Science.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 330, "retweet_count": 62, "bookmark_count": 607, "quote_count": 0, "reply_count": 2, "views_count": 52920, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1801605083731210477", "metadata": {"__typename": "Tweet", "rest_id": "1801605083731210477", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3OTU5OTIxOTM2MTM5MTAwMTY=", "rest_id": "795992193613910016", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1627050367841103872/TKyC_zFX_normal.jpg"}, "core": {"created_at": "Tue Nov 08 14:11:42 +0000 2016", "name": "<PERSON><PERSON>, PhD", "screen_name": "Eyowhite3"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Simplifying tech education for learners || Data and Business Analyst || Engineer || Advocate for sustainability.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "eyowhite.com", "expanded_url": "http://eyowhite.com", "url": "https://t.co/3hV1Uf7p8l", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10512, "followers_count": 48783, "friends_count": 585, "has_custom_timelines": false, "is_translator": false, "listed_count": 268, "media_count": 4522, "normal_followers_count": 48783, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/795992193613910016/1676754134", "profile_interstitial_type": "", "statuses_count": 14600, "translator_type": "none", "url": "https://t.co/3hV1Uf7p8l", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Birmingham, England"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1801605083731210477"], "editable_until_msecs": "1718374698000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "52920", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 607, "bookmarked": true, "created_at": "Fri Jun 14 13:18:18 +0000 2024", "conversation_id_str": "1801605083731210477", "display_text_range": [0, 138], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 330, "favorited": false, "full_text": "This sample Python code will show you step-by-step \nhow to clean the following 'messy' Healthcare dataset \nas a beginner in Data  Science.", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 2, "retweet_count": 62, "retweeted": false, "user_id_str": "795992193613910016", "id_str": "1801605083731210477"}, "twe_private_fields": {"created_at": 1718371098000, "updated_at": 1748554161183, "media_count": 0}}}, {"id": "1802320978795430302", "created_at": "2024-06-16 15:43:01 +03:00", "full_text": "If you want to become a world-class programmer, read these 8 books:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2231, "retweet_count": 317, "bookmark_count": 6617, "quote_count": 6, "reply_count": 28, "views_count": 416617, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1802320978795430302", "metadata": {"__typename": "Tweet", "rest_id": "1802320978795430302", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjQ2NDQ1ODY0Nzk2MTg0NTc4", "rest_id": "1646445864796184578", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1715471366835556352/zuMLBgAQ_normal.jpg"}, "core": {"created_at": "Thu Apr 13 09:31:24 +0000 2023", "name": "<PERSON>", "screen_name": "systemdesignone"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I Teach You System Design • 400K+ Audience", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "newsletter.systemdesign.one", "expanded_url": "https://newsletter.systemdesign.one/", "url": "https://t.co/XSmQ27nA5d", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7302, "followers_count": 26463, "friends_count": 134, "has_custom_timelines": false, "is_translator": false, "listed_count": 213, "media_count": 257, "normal_followers_count": 26463, "pinned_tweet_ids_str": ["1928052311886037466"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1646445864796184578/1747593365", "profile_interstitial_type": "", "statuses_count": 7848, "translator_type": "none", "url": "https://t.co/XSmQ27nA5d", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Join 150K+ Subscribers →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1737178222691831823", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1802320978795430302"], "editable_until_msecs": "1718545381000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "416617", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 6617, "bookmarked": true, "created_at": "Sun Jun 16 12:43:01 +0000 2024", "conversation_id_str": "1802320978795430302", "display_text_range": [0, 67], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2231, "favorited": false, "full_text": "If you want to become a world-class programmer, read these 8 books:", "is_quote_status": false, "lang": "en", "quote_count": 6, "reply_count": 28, "retweet_count": 317, "retweeted": false, "user_id_str": "1646445864796184578", "id_str": "1802320978795430302"}, "twe_private_fields": {"created_at": 1718541781000, "updated_at": 1748554161183, "media_count": 0}}}]