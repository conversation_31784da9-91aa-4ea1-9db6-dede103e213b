[{"id": "1565716442250706948", "created_at": "2022-09-02 18:01:04 +03:00", "full_text": "The freeCodeCamp community published all of these free YouTube courses over the summer. 🏗️\n\nWe're only just getting started. 🚀\n\nWe endeavor to cover every popular programming topic. And from a variety of perspectives, teaching approaches, and in many world languages. 🌏🌍🌎 https://t.co/oHkLlloy90", "media": [{"type": "photo", "url": "https://t.co/oHkLlloy90", "thumbnail": "https://pbs.twimg.com/media/FbqKc5wWQAgFUJ3?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FbqKc5wWQAgFUJ3?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3639, "retweet_count": 545, "bookmark_count": 700, "quote_count": 31, "reply_count": 75, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1565716442250706948", "metadata": {"__typename": "Tweet", "rest_id": "1565716442250706948", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozNDE2NDM5NTA=", "rest_id": "341643950", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1377353524661604356/DIMdJND1_normal.jpg"}, "core": {"created_at": "Sun Jul 24 18:24:50 +0000 2011", "name": "<PERSON>", "screen_name": "ossia"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Teacher and founder of https://t.co/SQRVR5jAWf 🏕️\nHost of The freeCodeCamp Podcast 🎧\nLearn from developers, founders, and ambitious people getting into tech.", "entities": {"description": {"urls": [{"display_url": "freeCodeCamp.org", "expanded_url": "http://freeCodeCamp.org", "url": "https://t.co/SQRVR5jAWf", "indices": [23, 46]}]}, "url": {"urls": [{"display_url": "freeCodeCamp.org", "expanded_url": "https://www.freeCodeCamp.org", "url": "https://t.co/1Bjrckjl2u", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 109571, "followers_count": 291287, "friends_count": 812, "has_custom_timelines": true, "is_translator": false, "listed_count": 2499, "media_count": 1386, "normal_followers_count": 291287, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/341643950/1667853564", "profile_interstitial_type": "", "statuses_count": 18305, "translator_type": "none", "url": "https://t.co/1Bjrckjl2u", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "On YT or your fav podcast app"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1565716442250706948"], "editable_until_msecs": "1662132664000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 700, "bookmarked": true, "created_at": "Fri Sep 02 15:01:04 +0000 2022", "conversation_id_str": "1565716442250706948", "display_text_range": [0, 271], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/oHkLlloy90", "expanded_url": "https://x.com/ossia/status/1565716442250706948/photo/1", "id_str": "1565716049609244680", "indices": [272, 295], "media_key": "3_1565716049609244680", "media_url_https": "https://pbs.twimg.com/media/FbqKc5wWQAgFUJ3.jpg", "type": "photo", "url": "https://t.co/oHkLlloy90", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 312, "y": 21, "h": 42, "w": 42}, {"x": 818, "y": 185, "h": 46, "w": 46}, {"x": 563, "y": 188, "h": 45, "w": 45}, {"x": 472, "y": 355, "h": 42, "w": 42}, {"x": 304, "y": 528, "h": 45, "w": 45}, {"x": 126, "y": 721, "h": 43, "w": 43}, {"x": 479, "y": 728, "h": 39, "w": 39}, {"x": 879, "y": 2, "h": 39, "w": 39}, {"x": 819, "y": 13, "h": 45, "w": 45}, {"x": 986, "y": 16, "h": 43, "w": 43}, {"x": 720, "y": 364, "h": 49, "w": 49}, {"x": 637, "y": 529, "h": 44, "w": 44}, {"x": 986, "y": 204, "h": 44, "w": 44}, {"x": 33, "y": 540, "h": 52, "w": 52}]}, "medium": {"faces": [{"x": 312, "y": 21, "h": 42, "w": 42}, {"x": 818, "y": 185, "h": 46, "w": 46}, {"x": 563, "y": 188, "h": 45, "w": 45}, {"x": 472, "y": 355, "h": 42, "w": 42}, {"x": 304, "y": 528, "h": 45, "w": 45}, {"x": 126, "y": 721, "h": 43, "w": 43}, {"x": 479, "y": 728, "h": 39, "w": 39}, {"x": 879, "y": 2, "h": 39, "w": 39}, {"x": 819, "y": 13, "h": 45, "w": 45}, {"x": 986, "y": 16, "h": 43, "w": 43}, {"x": 720, "y": 364, "h": 49, "w": 49}, {"x": 637, "y": 529, "h": 44, "w": 44}, {"x": 986, "y": 204, "h": 44, "w": 44}, {"x": 33, "y": 540, "h": 52, "w": 52}]}, "small": {"faces": [{"x": 199, "y": 13, "h": 26, "w": 26}, {"x": 522, "y": 118, "h": 29, "w": 29}, {"x": 359, "y": 120, "h": 28, "w": 28}, {"x": 301, "y": 226, "h": 26, "w": 26}, {"x": 194, "y": 337, "h": 28, "w": 28}, {"x": 80, "y": 460, "h": 27, "w": 27}, {"x": 305, "y": 464, "h": 24, "w": 24}, {"x": 561, "y": 1, "h": 24, "w": 24}, {"x": 522, "y": 8, "h": 28, "w": 28}, {"x": 629, "y": 10, "h": 27, "w": 27}, {"x": 459, "y": 232, "h": 31, "w": 31}, {"x": 406, "y": 337, "h": 28, "w": 28}, {"x": 629, "y": 130, "h": 28, "w": 28}, {"x": 21, "y": 344, "h": 33, "w": 33}]}, "orig": {"faces": [{"x": 312, "y": 21, "h": 42, "w": 42}, {"x": 818, "y": 185, "h": 46, "w": 46}, {"x": 563, "y": 188, "h": 45, "w": 45}, {"x": 472, "y": 355, "h": 42, "w": 42}, {"x": 304, "y": 528, "h": 45, "w": 45}, {"x": 126, "y": 721, "h": 43, "w": 43}, {"x": 479, "y": 728, "h": 39, "w": 39}, {"x": 879, "y": 2, "h": 39, "w": 39}, {"x": 819, "y": 13, "h": 45, "w": 45}, {"x": 986, "y": 16, "h": 43, "w": 43}, {"x": 720, "y": 364, "h": 49, "w": 49}, {"x": 637, "y": 529, "h": 44, "w": 44}, {"x": 986, "y": 204, "h": 44, "w": 44}, {"x": 33, "y": 540, "h": 52, "w": 52}]}}, "sizes": {"large": {"h": 885, "w": 1065, "resize": "fit"}, "medium": {"h": 885, "w": 1065, "resize": "fit"}, "small": {"h": 565, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 885, "width": 1065, "focus_rects": [{"x": 0, "y": 101, "w": 1065, "h": 596}, {"x": 180, "y": 0, "w": 885, "h": 885}, {"x": 289, "y": 0, "w": 776, "h": 885}, {"x": 622, "y": 0, "w": 443, "h": 885}, {"x": 0, "y": 0, "w": 1065, "h": 885}]}, "media_results": {"result": {"media_key": "3_1565716049609244680"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/oHkLlloy90", "expanded_url": "https://x.com/ossia/status/1565716442250706948/photo/1", "id_str": "1565716049609244680", "indices": [272, 295], "media_key": "3_1565716049609244680", "media_url_https": "https://pbs.twimg.com/media/FbqKc5wWQAgFUJ3.jpg", "type": "photo", "url": "https://t.co/oHkLlloy90", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 312, "y": 21, "h": 42, "w": 42}, {"x": 818, "y": 185, "h": 46, "w": 46}, {"x": 563, "y": 188, "h": 45, "w": 45}, {"x": 472, "y": 355, "h": 42, "w": 42}, {"x": 304, "y": 528, "h": 45, "w": 45}, {"x": 126, "y": 721, "h": 43, "w": 43}, {"x": 479, "y": 728, "h": 39, "w": 39}, {"x": 879, "y": 2, "h": 39, "w": 39}, {"x": 819, "y": 13, "h": 45, "w": 45}, {"x": 986, "y": 16, "h": 43, "w": 43}, {"x": 720, "y": 364, "h": 49, "w": 49}, {"x": 637, "y": 529, "h": 44, "w": 44}, {"x": 986, "y": 204, "h": 44, "w": 44}, {"x": 33, "y": 540, "h": 52, "w": 52}]}, "medium": {"faces": [{"x": 312, "y": 21, "h": 42, "w": 42}, {"x": 818, "y": 185, "h": 46, "w": 46}, {"x": 563, "y": 188, "h": 45, "w": 45}, {"x": 472, "y": 355, "h": 42, "w": 42}, {"x": 304, "y": 528, "h": 45, "w": 45}, {"x": 126, "y": 721, "h": 43, "w": 43}, {"x": 479, "y": 728, "h": 39, "w": 39}, {"x": 879, "y": 2, "h": 39, "w": 39}, {"x": 819, "y": 13, "h": 45, "w": 45}, {"x": 986, "y": 16, "h": 43, "w": 43}, {"x": 720, "y": 364, "h": 49, "w": 49}, {"x": 637, "y": 529, "h": 44, "w": 44}, {"x": 986, "y": 204, "h": 44, "w": 44}, {"x": 33, "y": 540, "h": 52, "w": 52}]}, "small": {"faces": [{"x": 199, "y": 13, "h": 26, "w": 26}, {"x": 522, "y": 118, "h": 29, "w": 29}, {"x": 359, "y": 120, "h": 28, "w": 28}, {"x": 301, "y": 226, "h": 26, "w": 26}, {"x": 194, "y": 337, "h": 28, "w": 28}, {"x": 80, "y": 460, "h": 27, "w": 27}, {"x": 305, "y": 464, "h": 24, "w": 24}, {"x": 561, "y": 1, "h": 24, "w": 24}, {"x": 522, "y": 8, "h": 28, "w": 28}, {"x": 629, "y": 10, "h": 27, "w": 27}, {"x": 459, "y": 232, "h": 31, "w": 31}, {"x": 406, "y": 337, "h": 28, "w": 28}, {"x": 629, "y": 130, "h": 28, "w": 28}, {"x": 21, "y": 344, "h": 33, "w": 33}]}, "orig": {"faces": [{"x": 312, "y": 21, "h": 42, "w": 42}, {"x": 818, "y": 185, "h": 46, "w": 46}, {"x": 563, "y": 188, "h": 45, "w": 45}, {"x": 472, "y": 355, "h": 42, "w": 42}, {"x": 304, "y": 528, "h": 45, "w": 45}, {"x": 126, "y": 721, "h": 43, "w": 43}, {"x": 479, "y": 728, "h": 39, "w": 39}, {"x": 879, "y": 2, "h": 39, "w": 39}, {"x": 819, "y": 13, "h": 45, "w": 45}, {"x": 986, "y": 16, "h": 43, "w": 43}, {"x": 720, "y": 364, "h": 49, "w": 49}, {"x": 637, "y": 529, "h": 44, "w": 44}, {"x": 986, "y": 204, "h": 44, "w": 44}, {"x": 33, "y": 540, "h": 52, "w": 52}]}}, "sizes": {"large": {"h": 885, "w": 1065, "resize": "fit"}, "medium": {"h": 885, "w": 1065, "resize": "fit"}, "small": {"h": 565, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 885, "width": 1065, "focus_rects": [{"x": 0, "y": 101, "w": 1065, "h": 596}, {"x": 180, "y": 0, "w": 885, "h": 885}, {"x": 289, "y": 0, "w": 776, "h": 885}, {"x": 622, "y": 0, "w": 443, "h": 885}, {"x": 0, "y": 0, "w": 1065, "h": 885}]}, "media_results": {"result": {"media_key": "3_1565716049609244680"}}}]}, "favorite_count": 3639, "favorited": false, "full_text": "The freeCodeCamp community published all of these free YouTube courses over the summer. 🏗️\n\nWe're only just getting started. 🚀\n\nWe endeavor to cover every popular programming topic. And from a variety of perspectives, teaching approaches, and in many world languages. 🌏🌍🌎 https://t.co/oHkLlloy90", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 31, "reply_count": 75, "retweet_count": 545, "retweeted": false, "user_id_str": "341643950", "id_str": "1565716442250706948"}, "twe_private_fields": {"created_at": 1662130864000, "updated_at": 1748554240581, "media_count": 1}}}, {"id": "1567694226296799233", "created_at": "2022-09-08 05:00:04 +03:00", "full_text": "Building an app? Use @bubble.\n\nCreating a website? Use @carrd. \n\nSelling a product? Use @gumroad. \n\nWriting marketing copy? Use @copy_ai.\n\nStarting a newsletter? Use @SubstackInc. \n\nStart simple. Complicate later.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4215, "retweet_count": 1004, "bookmark_count": 3436, "quote_count": 23, "reply_count": 73, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1567694226296799233", "metadata": {"__typename": "Tweet", "rest_id": "1567694226296799233", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzM5MDUyOTI4MjU0OTM5MTM3", "rest_id": "1339052928254939137", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1484538965109907461/VaQu5_PI_normal.jpg"}, "core": {"created_at": "Wed Dec 16 03:41:36 +0000 2020", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Productivity meets minimalism. Building, designing, and sharing my solutions.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "easlo.co", "expanded_url": "http://easlo.co", "url": "https://t.co/w0ts6vzqxq", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2305, "followers_count": 365731, "friends_count": 429, "has_custom_timelines": true, "is_translator": false, "listed_count": 6577, "media_count": 1890, "normal_followers_count": 365731, "pinned_tweet_ids_str": ["1652144909493878784"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1339052928254939137/1746771878", "profile_interstitial_type": "", "statuses_count": 9930, "translator_type": "none", "url": "https://t.co/w0ts6vzqxq", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Singapore"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460976615190528004", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "patreon_handle": "e<PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1567694226296799233"], "editable_until_msecs": "1662604204000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3436, "bookmarked": true, "created_at": "Thu Sep 08 02:00:04 +0000 2022", "conversation_id_str": "1567694226296799233", "display_text_range": [0, 213], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "720544068", "name": "Bubble", "screen_name": "bubble", "indices": [21, 28]}, {"id_str": "1230546225491062784", "name": "Carrd", "screen_name": "carrd", "indices": [55, 61]}, {"id_str": "276271004", "name": "Gumroad", "screen_name": "gumroad", "indices": [88, 96]}, {"id_str": "1300861660744372230", "name": "copy.ai", "screen_name": "copy_ai", "indices": [128, 136]}, {"id_str": "877295296828522496", "name": "Substack", "screen_name": "SubstackInc", "indices": [166, 178]}]}, "favorite_count": 4215, "favorited": false, "full_text": "Building an app? Use @bubble.\n\nCreating a website? Use @carrd. \n\nSelling a product? Use @gumroad. \n\nWriting marketing copy? Use @copy_ai.\n\nStarting a newsletter? Use @SubstackInc. \n\nStart simple. Complicate later.", "is_quote_status": false, "lang": "en", "quote_count": 23, "reply_count": 73, "retweet_count": 1004, "retweeted": false, "user_id_str": "1339052928254939137", "id_str": "1567694226296799233"}, "twe_private_fields": {"created_at": 1662602404000, "updated_at": 1748554240581, "media_count": 0}}}]