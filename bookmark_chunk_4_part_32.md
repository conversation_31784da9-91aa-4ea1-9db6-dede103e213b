# Bookmarks from bookmark_chunk_4_part_32.json

## Category: Education / Programming / YouTube Channels / Resources / Learning / Software Development / Web Development / Data Structures / Algorithms

*   **Author:** @swapnakpanda
    **Date:** 2023-09-03 15:30:30 +03:00
    **Content:** Best YouTube Channels for Programming:

❯ C ➨ Jacob Sorber
❯ C++ ➨ TheCherno
❯ Python ➨ Corey Schafer
❯ JavaScript ➨ developedbyed
❯ Java ➨ amigoscode
❯ C# ➨ kudvenkat
❯ Golang ➨ Jon Calhoun
❯ Swift ➨ CodeWithChris
❯ Kotlin ➨ PhilippLackner
❯ PHP ➨ ProgramWithGio
❯ Dart ➨ Flutterly
❯ Ruby ➨ DriftingRuby
❯ Rust ➨ NoBoilerplate
❯ TypeScript ➨ Matt Pocock
❯ Lua ➨ <PERSON>'s teacher
❯ R ➨ marinstatlectures
❯ SQL ➨ Joey Blue

❯ C++ ➨ javidx9
❯ JavaScript ➨ Akshay Saini
❯ TypeScript ➨ basarat
❯ TypeScript ➨ TypeScriptTV
❯ C# ➨ Microsoft Developer [Bob Tabor]
❯ C# ➨ dotnet [Scott/Kendra]

              -- Frameworks --

❯ Node.js ➨ Traversy Media
❯ React ➨ Dave Gray
❯ React ➨ Jack Herrington
❯ Next.js ➨ Lama Dev
❯ Vue ➨ Vue Mastery
❯ Svelte ➨ Joy of Code
❯ Angular ➨ Angular University
❯ Django ➨ CodingEntrepreneurs
❯ Laravel ➨ LaravelDaily
❯ Blazor ➨ James Montemagno
❯ Spring ➨ SpringSourceDev
❯ SpringBoot ➨ amigoscode
❯ Ruby on Rails ➨ GorailsTV
❯ Flutter ➨ The Flutter Way
❯ Flutter ➨ Tadas Petra

                 -- Special Mentions --

❯ Programming in 100 Sec ➨ Fireship
❯ DSA ➨ take U forward
❯ Interviews ➨ NeetCode
❯ Projects ➨ JavaScript King

                    -- Code Editors --

❯ Vim ➨ ThePrimeagen
❯ VS Code ➨ Visual Studio Code
❯ Jupyter Notebook ➨ Corey Schafer

                   -- Free Education --

  ➨ freecodecamp
  ➨ Simplilearn
  ➨ edureka!

                       -- Allrounders --

   ➨ TechWithTim
   ➨ WebDevSimplified
   ➨ programmingwithmosh
   ➨ Traversy Media
   ➨ BroCodez
   ➨ thenewboston
   ➨ Telusko
   ➨ Derek Banas
   ➨ CodeWithHarry
   ➨ MySirG .com
    **URL:** [https://twitter.com/swapnakpanda/status/1698312511572058473](https://twitter.com/swapnakpanda/status/1698312511572058473)

---

## Category: Education / Programming / Websites / Resources / Learning / Software Development / Web Development / Data Structures / Algorithms / Databases

*   **Author:** @swapnakpanda
    **Date:** 2023-09-07 15:05:15 +03:00
    **Content:** Bookmark these 50+ sites for lifetime:

❯ C ➨ learn-c
❯ C++ ➨ codecademy
❯ Java ➨ baeldung
❯ C# ➨ learncs
❯ JavaScript ➨ javascript .info
❯ Python ➨ realpython
❯ PHP ➨ learn-php
❯ Kotlin ➨ studytonight
❯ Go ➨ learn-golang
❯ Swift ➨ codewithchris
❯ SQL ➨ sqlbolt
❯ DSA ➨ techdevguide .withgoogle

❯ Python ➨ kaggle
❯ Java ➨ dzone
❯ Java ➨ codecademy
❯ JavaScript ➨ MDN
❯ TypeScript ➨ codecademy
❯ Go ➨ gobyexample
❯ Go ➨ golangbot
❯ Kotlin ➨ codecademy
❯ Ruby ➨ rubyguides
❯ Swift ➨ hackingwithswift
❯ Swift ➨ codecademy

❯ HTML and CSS ➨ learn-html
❯ CSS ➨ css-tricks

❯ React ➨ scrimba
❯ React ➨ react-tutorial
❯ Vue ➨ vuemastery
❯ Vue ➨ learnvue
❯ Vue ➨ scrimba
❯ Ruby on Rails ➨ railstutorial
❯ Spring ➨ baeldung
❯ Spring ➨ journaldev
❯ Spring ➨ dzone
❯ Laravel ➨ laracasts

❯ SQL ➨ codecademy
❯ SQL ➨ sqltutorial
❯ MySQL ➨ mysqltutorial
❯ PostgreSQL ➨ postgresqltutorial
❯ SQL Server ➨ sqlservertutorial
❯ Oracle ➨ oracletutorial
❯ MongoDB ➨ mongodbtutorial

❯ REST ➨ restfulapi
❯ GraphQL ➨ apollographql
❯ Full Stack ➨ theodinproject
❯ Backend ➨ theserverside
❯ Microservices ➨ dzone
❯ DevOps ➨ theserverside
❯ Cloud ➨ dzone
❯ Azure ➨ learn. microsoft

These popular sites provide learning content for multiple skills:

❯ w3schools
❯ tutorialspoint
❯ programiz
❯ geeksforgeeks
❯ freecodecamp
❯ studytonight
❯ dzone
❯ theserverside

✧ Also follow the official docs. Those are sometimes the best places to learn.
    **URL:** [https://twitter.com/swapnakpanda/status/1699755709964128305](https://twitter.com/swapnakpanda/status/1699755709964128305)

---