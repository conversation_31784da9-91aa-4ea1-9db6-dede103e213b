# Bookmarks from bookmark_chunk_2_part_10.json

## Category: Sports/Football/Player Highlights/MUFC/<PERSON>

*   **Author:** @hxMUFC
    **Date:** 2021-06-20 21:13:40 +03:00
    **Content:** <PERSON> - No Church In The Wild

    [@<PERSON><PERSON><PERSON><PERSON>]
    https://t.co/CA6lUHg2G8
    **URL:** [https://twitter.com/undefined/status/1406676639023521792](https://twitter.com/undefined/status/1406676639023521792)
    **Note:** A video compilation of <PERSON>'s highlights, likely set to the song "No Church In The Wild". Original video from @LancsOG.

---

## Category: Technology/Programming/Python/Tutorials/Functions

*   **Author:** @razacodes
    **Date:** 2021-06-22 01:56:36 +03:00
    **Content:** Python Functions for beginners 🧵

    Functions are a block of code that can be executed in a standardised way
    All Python frameworks, like Django or Flask, rely on them.
    Knowing the basics is essential

    What we covered so far 👇
    **URL:** [https://twitter.com/undefined/status/1407110230648012801](https://twitter.com/undefined/status/1407110230648012801)
    **Note:** The beginning of an educational Twitter thread explaining Python functions for beginners.

---