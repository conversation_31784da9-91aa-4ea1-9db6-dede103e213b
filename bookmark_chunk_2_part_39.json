[{"id": "1471562021829287940", "created_at": "2021-12-16 22:25:00 +03:00", "full_text": "How to Build Muscle Doing Push Ups... https://t.co/8RTveCFGS6", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 49, "retweet_count": 2, "bookmark_count": 4, "quote_count": 1, "reply_count": 0, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1471562021829287940", "metadata": {"__typename": "Tweet", "rest_id": "1471562021829287940", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNDI1MjMx", "rest_id": "2425231", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1244657050275151872/BRycNabV_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Mar 27 07:29:54 +0000 2007", "name": "Fact", "screen_name": "Fact"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Interesting facts about life. If you spot a mistake, just throw a DM over.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "geckoninja.com", "expanded_url": "https://geckoninja.com", "url": "https://t.co/EEC1XLOREp", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 62, "followers_count": 3430696, "friends_count": 0, "has_custom_timelines": false, "is_translator": false, "listed_count": 6309, "media_count": 1285, "normal_followers_count": 3430696, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2425231/1585584342", "profile_interstitial_type": "", "statuses_count": 927204, "translator_type": "regular", "url": "https://t.co/EEC1XLOREp", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "United Kingdom"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************"}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/8RTveCFGS6", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 147, "width": 280, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=280x150"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "Remember how you hated doing pushups during your gym class in school and even moreso during football practice outside in the freezing cold? Contrary to popular belief for those who don’t [...]", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "viralventura.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=800x320_1"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "2359992720", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 76, "width": 144, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "creator", "value": {"type": "USER", "user_value": {"id_str": "2359992720", "path": []}}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "viralventura.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 188, "green": 203, "red": 225}, "percentage": 34.19}, {"rgb": {"blue": 24, "green": 30, "red": 29}, "percentage": 33.03}, {"rgb": {"blue": 103, "green": 113, "red": 128}, "percentage": 14.34}, {"rgb": {"blue": 93, "green": 122, "red": 173}, "percentage": 8.65}, {"rgb": {"blue": 38, "green": 66, "red": 105}, "percentage": 2.39}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "6 Ways Pushups Can Help You Build and <PERSON>ne <PERSON>", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 188, "green": 203, "red": 225}, "percentage": 34.19}, {"rgb": {"blue": 24, "green": 30, "red": 29}, "percentage": 33.03}, {"rgb": {"blue": 103, "green": 113, "red": 128}, "percentage": 14.34}, {"rgb": {"blue": 93, "green": 122, "red": 173}, "percentage": 8.65}, {"rgb": {"blue": 38, "green": 66, "red": 105}, "percentage": 2.39}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 188, "green": 203, "red": 225}, "percentage": 34.19}, {"rgb": {"blue": 24, "green": 30, "red": 29}, "percentage": 33.03}, {"rgb": {"blue": 103, "green": 113, "red": 128}, "percentage": 14.34}, {"rgb": {"blue": 93, "green": 122, "red": 173}, "percentage": 8.65}, {"rgb": {"blue": 38, "green": 66, "red": 105}, "percentage": 2.39}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/8RTveCFGS6", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 312, "width": 595, "url": "https://pbs.twimg.com/card_img/1927025009437917184/XDBKifBx?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/8RTveCFGS6", "user_refs_results": [{"result": {"__typename": "UserUnavailable", "message": "User is suspended", "reason": "Suspended"}}, {"result": {"__typename": "UserUnavailable", "message": "User is suspended", "reason": "Suspended"}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1471562021829287940"], "editable_until_msecs": "1639684500961", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://buffer.com\" rel=\"nofollow\"><PERSON><PERSON>er App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 4, "bookmarked": true, "created_at": "Thu Dec 16 19:25:00 +0000 2021", "conversation_id_str": "1471562021829287940", "display_text_range": [0, 61], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "viralventura.com/pushups-build-…", "expanded_url": "https://viralventura.com/pushups-build-muscle/", "url": "https://t.co/8RTveCFGS6", "indices": [38, 61]}], "user_mentions": []}, "favorite_count": 49, "favorited": false, "full_text": "How to Build Muscle Doing Push Ups... https://t.co/8RTveCFGS6", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 0, "retweet_count": 2, "retweeted": false, "user_id_str": "2425231", "id_str": "1471562021829287940"}, "twe_private_fields": {"created_at": 1639682700000, "updated_at": 1748554384387, "media_count": 0}}}, {"id": "1471913434413424645", "created_at": "2021-12-17 21:41:24 +03:00", "full_text": "🧵 Thread \n\n🎥@TifoFootball_ \n&amp;\n✍️Eric Laurie", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 340, "retweet_count": 73, "bookmark_count": 145, "quote_count": 7, "reply_count": 3, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1471913434413424645", "metadata": {"__typename": "Tweet", "rest_id": "1471913434413424645", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjA1MDQ0MDM=", "rest_id": "120504403", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1611860909839851520/RhBnCS6i_normal.jpg"}, "core": {"created_at": "Sat Mar 06 16:47:50 +0000 2010", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Player Pathway Scout @USMNT • Former @Molde_FK @EASportsFC • MSc Football Management • views my own", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 14683, "followers_count": 50569, "friends_count": 308, "has_custom_timelines": false, "is_translator": false, "listed_count": 600, "media_count": 13, "normal_followers_count": 50569, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/120504403/1689255297", "profile_interstitial_type": "", "statuses_count": 80, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "🇺🇸"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1471913434413424645"], "editable_until_msecs": "1639768284250", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 145, "bookmarked": true, "created_at": "Fri Dec 17 18:41:24 +0000 2021", "conversation_id_str": "1471913434413424645", "display_text_range": [0, 47], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "521238652", "name": "Tifo Football by The Athletic", "screen_name": "TifoFootball_", "indices": [12, 26]}]}, "favorite_count": 340, "favorited": false, "full_text": "🧵 Thread \n\n🎥@TifoFootball_ \n&amp;\n✍️Eric Laurie", "is_quote_status": false, "lang": "en", "quote_count": 7, "reply_count": 3, "retweet_count": 73, "retweeted": false, "user_id_str": "120504403", "id_str": "1471913434413424645"}, "twe_private_fields": {"created_at": 1639766484000, "updated_at": 1748554384387, "media_count": 0}}}]