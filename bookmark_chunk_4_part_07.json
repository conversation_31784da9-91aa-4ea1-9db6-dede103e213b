[{"id": "1608849364788842498", "created_at": "2022-12-30 18:35:54 +03:00", "full_text": "Make Audio Book from any PDF using Python\n#python #developer \nhttps://t.co/fc3epCR27N", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 691, "retweet_count": 201, "bookmark_count": 228, "quote_count": 1, "reply_count": 14, "views_count": 61284, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1608849364788842498", "metadata": {"__typename": "Tweet", "rest_id": "1608849364788842498", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTUzODQ2Mjc5NzU4MzE1NTM=", "rest_id": "855384627975831553", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/855386134439895041/BKOMdcnN_normal.jpg"}, "core": {"created_at": "Fri Apr 21 11:36:02 +0000 2017", "name": "Python Programming", "screen_name": "PythonPr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "#python #programming", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "morioh.com", "expanded_url": "https://morioh.com", "url": "https://t.co/6eeX8MWjRB", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6945, "followers_count": 135341, "friends_count": 1177, "has_custom_timelines": false, "is_translator": false, "listed_count": 1052, "media_count": 1624, "normal_followers_count": 135341, "pinned_tweet_ids_str": ["1859613267775385673"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/855384627975831553/1492774829", "profile_interstitial_type": "", "statuses_count": 7123, "translator_type": "none", "url": "https://t.co/6eeX8MWjRB", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1608849364788842498"], "editable_until_msecs": "1672416354000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "61284", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 228, "bookmarked": true, "created_at": "Fri Dec 30 15:35:54 +0000 2022", "conversation_id_str": "1608849364788842498", "display_text_range": [0, 85], "entities": {"hashtags": [{"indices": [42, 49], "text": "python"}, {"indices": [50, 60], "text": "developer"}], "symbols": [], "timestamps": [], "urls": [{"display_url": "morioh.com/p/42a6957afa8a…", "expanded_url": "https://morioh.com/p/42a6957afa8a?f=5c21fb01c16e2556b555ab32", "url": "https://t.co/fc3epCR27N", "indices": [62, 85]}], "user_mentions": []}, "favorite_count": 691, "favorited": false, "full_text": "Make Audio Book from any PDF using Python\n#python #developer \nhttps://t.co/fc3epCR27N", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 14, "retweet_count": 201, "retweeted": false, "user_id_str": "855384627975831553", "id_str": "1608849364788842498"}, "twe_private_fields": {"created_at": 1672414554000, "updated_at": 1748554204513, "media_count": 0}}}, {"id": "1608932673569067009", "created_at": "2022-12-31 00:06:56 +03:00", "full_text": "10 Free Python Courses to upskill in 2023!\n\nCS50's Introduction to Programming with Python -- https://t.co/S2hoke3h73\n\nPython Tutorials for Absolute Beginners by CS Dojo -- https://t.co/Zk2Qqi6zmE\n\nMore in Tweet thread below! https://t.co/8hBmv4NtAl", "media": [{"type": "photo", "url": "https://t.co/8hBmv4NtAl", "thumbnail": "https://pbs.twimg.com/media/FlQTvFzaYAAnMCQ?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FlQTvFzaYAAnMCQ?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1031, "retweet_count": 336, "bookmark_count": 547, "quote_count": 5, "reply_count": 22, "views_count": 127925, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1608932673569067009", "metadata": {"__typename": "Tweet", "rest_id": "1608932673569067009", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2MzU1MTY0ODA=", "rest_id": "635516480", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/839926952840544261/9NRaGhKr_normal.jpg"}, "core": {"created_at": "Sat Jul 14 17:09:44 +0000 2012", "name": "<PERSON><PERSON> (The Ravit Show)", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Founder & Host of \"The Ravit Show\" | Head Community Evangelist | Official LinkedIn Creator | Data Community Builder | Data Professional | Media", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "theravitshow.com", "expanded_url": "http://www.theravitshow.com", "url": "https://t.co/O7BISZe5fc", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 3668, "followers_count": 2534, "friends_count": 2306, "has_custom_timelines": false, "is_translator": false, "listed_count": 44, "media_count": 727, "normal_followers_count": 2534, "pinned_tweet_ids_str": ["1927773292661780594"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/635516480/1640513742", "profile_interstitial_type": "", "statuses_count": 1804, "translator_type": "none", "url": "https://t.co/O7BISZe5fc", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1609657028573810689", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1608932673569067009"], "editable_until_msecs": "1672436216000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "127925", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 547, "bookmarked": true, "created_at": "Fri Dec 30 21:06:56 +0000 2022", "conversation_id_str": "1608932673569067009", "display_text_range": [0, 225], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/8hBmv4NtAl", "expanded_url": "https://x.com/RavitJain/status/1608932673569067009/photo/1", "id_str": "1608932666610704384", "indices": [226, 249], "media_key": "3_1608932666610704384", "media_url_https": "https://pbs.twimg.com/media/FlQTvFzaYAAnMCQ.jpg", "type": "photo", "url": "https://t.co/8hBmv4NtAl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 610, "y": 860, "h": 76, "w": 76}, {"x": 333, "y": 858, "h": 82, "w": 82}, {"x": 768, "y": 858, "h": 80, "w": 80}, {"x": 121, "y": 700, "h": 92, "w": 92}, {"x": 621, "y": 423, "h": 91, "w": 91}, {"x": 440, "y": 690, "h": 124, "w": 124}]}, "medium": {"faces": [{"x": 610, "y": 860, "h": 76, "w": 76}, {"x": 333, "y": 858, "h": 82, "w": 82}, {"x": 768, "y": 858, "h": 80, "w": 80}, {"x": 121, "y": 700, "h": 92, "w": 92}, {"x": 621, "y": 423, "h": 91, "w": 91}, {"x": 440, "y": 690, "h": 124, "w": 124}]}, "small": {"faces": [{"x": 384, "y": 541, "h": 47, "w": 47}, {"x": 209, "y": 540, "h": 51, "w": 51}, {"x": 483, "y": 540, "h": 50, "w": 50}, {"x": 76, "y": 440, "h": 57, "w": 57}, {"x": 391, "y": 266, "h": 57, "w": 57}, {"x": 277, "y": 434, "h": 78, "w": 78}]}, "orig": {"faces": [{"x": 610, "y": 860, "h": 76, "w": 76}, {"x": 333, "y": 858, "h": 82, "w": 82}, {"x": 768, "y": 858, "h": 80, "w": 80}, {"x": 121, "y": 700, "h": 92, "w": 92}, {"x": 621, "y": 423, "h": 91, "w": 91}, {"x": 440, "y": 690, "h": 124, "w": 124}]}}, "sizes": {"large": {"h": 1080, "w": 1080, "resize": "fit"}, "medium": {"h": 1080, "w": 1080, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 1080, "focus_rects": [{"x": 0, "y": 211, "w": 1080, "h": 605}, {"x": 0, "y": 0, "w": 1080, "h": 1080}, {"x": 67, "y": 0, "w": 947, "h": 1080}, {"x": 270, "y": 0, "w": 540, "h": 1080}, {"x": 0, "y": 0, "w": 1080, "h": 1080}]}, "media_results": {"result": {"media_key": "3_1608932666610704384"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "lnkd.in/dzTekpHp", "expanded_url": "https://lnkd.in/dzTekpHp", "url": "https://t.co/S2hoke3h73", "indices": [94, 117]}, {"display_url": "lnkd.in/d39TnzKr", "expanded_url": "https://lnkd.in/d39TnzKr", "url": "https://t.co/Zk2Qqi6zmE", "indices": [173, 196]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/8hBmv4NtAl", "expanded_url": "https://x.com/RavitJain/status/1608932673569067009/photo/1", "id_str": "1608932666610704384", "indices": [226, 249], "media_key": "3_1608932666610704384", "media_url_https": "https://pbs.twimg.com/media/FlQTvFzaYAAnMCQ.jpg", "type": "photo", "url": "https://t.co/8hBmv4NtAl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 610, "y": 860, "h": 76, "w": 76}, {"x": 333, "y": 858, "h": 82, "w": 82}, {"x": 768, "y": 858, "h": 80, "w": 80}, {"x": 121, "y": 700, "h": 92, "w": 92}, {"x": 621, "y": 423, "h": 91, "w": 91}, {"x": 440, "y": 690, "h": 124, "w": 124}]}, "medium": {"faces": [{"x": 610, "y": 860, "h": 76, "w": 76}, {"x": 333, "y": 858, "h": 82, "w": 82}, {"x": 768, "y": 858, "h": 80, "w": 80}, {"x": 121, "y": 700, "h": 92, "w": 92}, {"x": 621, "y": 423, "h": 91, "w": 91}, {"x": 440, "y": 690, "h": 124, "w": 124}]}, "small": {"faces": [{"x": 384, "y": 541, "h": 47, "w": 47}, {"x": 209, "y": 540, "h": 51, "w": 51}, {"x": 483, "y": 540, "h": 50, "w": 50}, {"x": 76, "y": 440, "h": 57, "w": 57}, {"x": 391, "y": 266, "h": 57, "w": 57}, {"x": 277, "y": 434, "h": 78, "w": 78}]}, "orig": {"faces": [{"x": 610, "y": 860, "h": 76, "w": 76}, {"x": 333, "y": 858, "h": 82, "w": 82}, {"x": 768, "y": 858, "h": 80, "w": 80}, {"x": 121, "y": 700, "h": 92, "w": 92}, {"x": 621, "y": 423, "h": 91, "w": 91}, {"x": 440, "y": 690, "h": 124, "w": 124}]}}, "sizes": {"large": {"h": 1080, "w": 1080, "resize": "fit"}, "medium": {"h": 1080, "w": 1080, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1080, "width": 1080, "focus_rects": [{"x": 0, "y": 211, "w": 1080, "h": 605}, {"x": 0, "y": 0, "w": 1080, "h": 1080}, {"x": 67, "y": 0, "w": 947, "h": 1080}, {"x": 270, "y": 0, "w": 540, "h": 1080}, {"x": 0, "y": 0, "w": 1080, "h": 1080}]}, "media_results": {"result": {"media_key": "3_1608932666610704384"}}}]}, "favorite_count": 1031, "favorited": false, "full_text": "10 Free Python Courses to upskill in 2023!\n\nCS50's Introduction to Programming with Python -- https://t.co/S2hoke3h73\n\nPython Tutorials for Absolute Beginners by CS Dojo -- https://t.co/Zk2Qqi6zmE\n\nMore in Tweet thread below! https://t.co/8hBmv4NtAl", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 5, "reply_count": 22, "retweet_count": 336, "retweeted": false, "user_id_str": "635516480", "id_str": "1608932673569067009"}, "twe_private_fields": {"created_at": 1672434416000, "updated_at": 1748554204513, "media_count": 1}}}]