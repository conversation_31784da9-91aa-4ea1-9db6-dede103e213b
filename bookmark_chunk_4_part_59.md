# Bookmark 1788176526862590026

**Author:** @Pirat_Nation
**URL:** [https://twitter.com/Pirat_Nation/status/1788176526862590026](https://twitter.com/Pirat_Nation/status/1788176526862590026)
**Category:** Technology / Operating Systems / Windows / Security / Tutorial

## Content

Windows 11 24H2 will enable BitLocker encryption for everyone, happens on both clean installs and reinstalls.

BitLocker has been proven to impact system performance, particularly SSD performance.
 SSD performance can drop by up to 45% depending on the workload

Even worse, if you are using the software form of BitLocker, all the encryption and decryption tasks get loaded onto the CPU, which can potentially reduce system performance as well.

Anything storage-related goes wrong with a machine that has BitLocker turned on, users can lose all access to their drive contents due to encryption.

——

How to disable Bitlocket for Windows Home:

1 Type and search [Device encryption settings] in the Windows search bar①, then click [Open]

2 On the Device encryption field, set the option to [Off]③.

3 Confirm whether you need to turn off device encryption, select [Turn off] to disable the device encryption function④.

Article: [https://t.co/CGyJUbW1ze](https://t.co/CGyJUbW1ze)

Tutorial to disable: [https://t.co/hhEtfy6zqw](https://t.co/hhEtfy6zqw)

## Media

- **Type:** photo
  **URL:** [https://t.co/soKqn6ycRD](https://t.co/soKqn6ycRD)
- **Type:** photo
  **URL:** [https://t.co/soKqn6ycRD](https://t.co/soKqn6ycRD)
- **Type:** photo
  **URL:** [https://t.co/soKqn6ycRD](https://t.co/soKqn6ycRD)

## External Links

- [tomshardware.com/software/windo…](https://www.tomshardware.com/software/windows/windows-11-24h2-will-enable-bitlocker-encryption-for-everyone-happens-on-both-clean-installs-and-reinstalls?utm_content=tomsguide&utm_medium=social&utm_campaign=socialflow&utm_source=facebook.com)
- [asus.com/support/faq/10…](https://www.asus.com/support/faq/1047461/)