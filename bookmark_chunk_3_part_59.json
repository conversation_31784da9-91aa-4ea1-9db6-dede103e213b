[{"id": "1564102489669902336", "created_at": "2022-08-29 07:07:47 +03:00", "full_text": "I attempted 20+ interviews in different companies before I started working as a software developer at Microsoft. \n\nHere's what I learnt about cracking programming interviews:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5365, "retweet_count": 1245, "bookmark_count": 3949, "quote_count": 30, "reply_count": 100, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1564102489669902336", "metadata": {"__typename": "Tweet", "rest_id": "1564102489669902336", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMzk2NjEzNTA=", "rest_id": "339661350", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1479627028378705921/4YHubV3Y_normal.jpg"}, "core": {"created_at": "Thu Jul 21 13:15:18 +0000 2011", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "ujjwalscript"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Senior SDE @Fluxon. Ex @microsoft US. I will help you break into tech and crack top jobs worldwide 🚀. DMs Open 🙂", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 11275, "followers_count": 85695, "friends_count": 353, "has_custom_timelines": true, "is_translator": false, "listed_count": 1203, "media_count": 297, "normal_followers_count": 85695, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/339661350/1660533756", "profile_interstitial_type": "", "statuses_count": 8257, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1483622229736755208", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1564102489669902336"], "editable_until_msecs": "1661747867000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3949, "bookmarked": true, "created_at": "Mon Aug 29 04:07:47 +0000 2022", "conversation_id_str": "1564102489669902336", "display_text_range": [0, 174], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 5365, "favorited": false, "full_text": "I attempted 20+ interviews in different companies before I started working as a software developer at Microsoft. \n\nHere's what I learnt about cracking programming interviews:", "is_quote_status": false, "lang": "en", "quote_count": 30, "reply_count": 100, "retweet_count": 1245, "retweeted": false, "user_id_str": "339661350", "id_str": "1564102489669902336"}, "twe_private_fields": {"created_at": 1661746067000, "updated_at": 1748554240582, "media_count": 0}}}, {"id": "1565494000936697858", "created_at": "2022-09-02 03:17:09 +03:00", "full_text": "Python and Excel:\n\nA potent combination for working with data.\n\nHere are the 17 Python libraries to help you unlock the power.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4072, "retweet_count": 902, "bookmark_count": 3516, "quote_count": 15, "reply_count": 76, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1565494000936697858", "metadata": {"__typename": "Tweet", "rest_id": "1565494000936697858", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTg3MTMyOTYw", "rest_id": "3187132960", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1924181461978017792/YNLsa7W5_normal.jpg"}, "core": {"created_at": "Mon Apr 20 12:55:10 +0000 2015", "name": "PyQuant News 🐍", "screen_name": "pyquantnews"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Where finance practitioners get started with Python for quant finance, algorithmic trading, and data analysis | Tweets & threads with free Python code & tools.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "links.pyquantnews.com/videos", "expanded_url": "https://links.pyquantnews.com/videos", "url": "https://t.co/xleEXqYDRj", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16470, "followers_count": 146598, "friends_count": 520, "has_custom_timelines": true, "is_translator": false, "listed_count": 2998, "media_count": 4091, "normal_followers_count": 146598, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/3187132960/1688638175", "profile_interstitial_type": "", "statuses_count": 17268, "translator_type": "none", "url": "https://t.co/xleEXqYDRj", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Free algo trading videos →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1606127950176419840", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1565494000936697858"], "editable_until_msecs": "1662079629000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3516, "bookmarked": true, "created_at": "Fri Sep 02 00:17:09 +0000 2022", "conversation_id_str": "1565494000936697858", "display_text_range": [0, 126], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 4072, "favorited": false, "full_text": "Python and Excel:\n\nA potent combination for working with data.\n\nHere are the 17 Python libraries to help you unlock the power.", "is_quote_status": false, "lang": "en", "quote_count": 15, "reply_count": 76, "retweet_count": 902, "retweeted": false, "user_id_str": "3187132960", "id_str": "1565494000936697858"}, "twe_private_fields": {"created_at": 1662077829000, "updated_at": 1748554240582, "media_count": 0}}}]