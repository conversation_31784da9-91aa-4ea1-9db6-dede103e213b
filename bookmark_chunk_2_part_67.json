[{"id": "1497950095228080137", "created_at": "2022-02-27 18:01:47 +03:00", "full_text": "One company increased sales 262.5% with this pricing strategy: \n\nThe Decoy Effect. \n\nWhat it is &amp; how to use it: 🧵", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 8246, "retweet_count": 1847, "bookmark_count": 5257, "quote_count": 203, "reply_count": 213, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1497950095228080137", "metadata": {"__typename": "Tweet", "rest_id": "1497950095228080137", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozNDEwMTc1NTM=", "rest_id": "*********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1562966608506675200/BzGRdRPc_normal.jpg"}, "core": {"created_at": "Sat Jul 23 17:27:53 +0000 2011", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Built 2 companies, sold 1. Now investing in industrial real estate across New England. Follow along 👇", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "brightline.kit.com/real-estate-in…", "expanded_url": "https://brightline.kit.com/real-estate-investor-list", "url": "https://t.co/zYfEJmsZVd", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 56962, "followers_count": 96771, "friends_count": 223, "has_custom_timelines": true, "is_translator": false, "listed_count": 1587, "media_count": 300, "normal_followers_count": 96771, "pinned_tweet_ids_str": ["1892580273323131183"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/*********/1672674230", "profile_interstitial_type": "", "statuses_count": 20351, "translator_type": "none", "url": "https://t.co/zYfEJmsZVd", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Boston, Massachusetts "}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "barrett-oneill-3"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1497950095228080137"], "editable_until_msecs": "1645975907926", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 5257, "bookmarked": true, "created_at": "Sun Feb 27 15:01:47 +0000 2022", "conversation_id_str": "1497950095228080137", "display_text_range": [0, 118], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 8246, "favorited": false, "full_text": "One company increased sales 262.5% with this pricing strategy: \n\nThe Decoy Effect. \n\nWhat it is &amp; how to use it: 🧵", "is_quote_status": false, "lang": "en", "quote_count": 203, "reply_count": 213, "retweet_count": 1847, "retweeted": false, "user_id_str": "*********", "id_str": "1497950095228080137"}, "twe_private_fields": {"created_at": 1645974107000, "updated_at": 1748554363911, "media_count": 0}}}, {"id": "1498670658120826881", "created_at": "2022-03-01 17:45:03 +03:00", "full_text": "Here are 7 proven methods to get honest feedback from your team:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 680, "retweet_count": 120, "bookmark_count": 531, "quote_count": 12, "reply_count": 22, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1498670658120826881", "metadata": {"__typename": "Tweet", "rest_id": "1498670658120826881", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMjE2NDUwNjU=", "rest_id": "*********", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1722676274018291712/-_NYTdvA_normal.jpg"}, "core": {"created_at": "Wed Dec 01 06:06:23 +0000 2010", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Salem, Oregon’s hype man. Sharing thoughts about building a local media company. Big high five guy.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "JacobEspinoza.com", "expanded_url": "http://JacobEspinoza.com", "url": "https://t.co/5ytrGwgBiK", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 105747, "followers_count": 38500, "friends_count": 1956, "has_custom_timelines": true, "is_translator": false, "listed_count": 1084, "media_count": 2214, "normal_followers_count": 38500, "pinned_tweet_ids_str": ["1513509689236996097"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/*********/1640930911", "profile_interstitial_type": "", "statuses_count": 38163, "translator_type": "none", "url": "https://t.co/5ytrGwgBiK", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1464683212534386697", "professional_type": "Creator", "category": [{"id": 1009, "name": "Community", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "cash_app_handle": "$mrjacobespi"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1498670658120826881"], "editable_until_msecs": "1646147703507", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://writehouse.app\" rel=\"nofollow\">Writehouse.app</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 531, "bookmarked": true, "created_at": "Tue Mar 01 14:45:03 +0000 2022", "conversation_id_str": "1498670658120826881", "display_text_range": [0, 64], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 680, "favorited": false, "full_text": "Here are 7 proven methods to get honest feedback from your team:", "is_quote_status": false, "lang": "en", "quote_count": 12, "reply_count": 22, "retweet_count": 120, "retweeted": false, "user_id_str": "*********", "id_str": "1498670658120826881"}, "twe_private_fields": {"created_at": 1646145903000, "updated_at": 1748554363911, "media_count": 0}}}]