[{"id": "1649492404943323136", "created_at": "2023-04-21 22:16:50 +03:00", "full_text": "Come code your 1st AI app!\n\nBuild an OpenAI Q&amp;A bot in 21 lines of Python.\n\nLearn how to:\n- write Python code\n- interact with OpenAI’s API\n- build a CLI\n\n100% free. No experience required.\n\nCourse on @Replit by @TakeoffAI.\n\nLearn: https://t.co/jdJQaE6o3Z https://t.co/5Ip5xuUQm5", "media": [{"type": "photo", "url": "https://t.co/5Ip5xuUQm5", "thumbnail": "https://pbs.twimg.com/media/FuQsmXwaEAA0rwi?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FuQsmXwaEAA0rwi?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2259, "retweet_count": 411, "bookmark_count": 3203, "quote_count": 29, "reply_count": 105, "views_count": 650083, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1649492404943323136", "metadata": {"__typename": "Tweet", "rest_id": "1649492404943323136", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNzg2NDMxNDM3", "rest_id": "2786431437", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1552979440547704832/WX5crG9I_normal.jpg"}, "core": {"created_at": "Fri Sep 26 23:44:05 +0000 2014", "name": "<PERSON><PERSON><PERSON>", "screen_name": "m<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I build & teach AI stuff. Building @TakeoffAI. Learn to build with AI at https://t.co/oJ8PNoAutE.", "entities": {"description": {"urls": [{"display_url": "JoinTakeoff.com", "expanded_url": "http://JoinTakeoff.com", "url": "https://t.co/oJ8PNoAutE", "indices": [73, 96]}]}, "url": {"urls": [{"display_url": "mckaywrigley.com", "expanded_url": "https://www.mckaywrigley.com", "url": "https://t.co/6wGfNx82Gl", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 95041, "followers_count": 196577, "friends_count": 348, "has_custom_timelines": true, "is_translator": false, "listed_count": 4041, "media_count": 899, "normal_followers_count": 196577, "pinned_tweet_ids_str": ["1827395971032215632"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2786431437/1679644853", "profile_interstitial_type": "", "statuses_count": 18322, "translator_type": "none", "url": "https://t.co/6wGfNx82Gl", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "The Simulation"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "cash_app_handle": "", "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1649492404943323136"], "editable_until_msecs": "1682106410000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "650083", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3203, "bookmarked": true, "created_at": "Fri Apr 21 19:16:50 +0000 2023", "conversation_id_str": "1649492404943323136", "display_text_range": [0, 258], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/5Ip5xuUQm5", "expanded_url": "https://x.com/mckaywrigley/status/1649492404943323136/photo/1", "id_str": "1649492401248145408", "indices": [259, 282], "media_key": "3_1649492401248145408", "media_url_https": "https://pbs.twimg.com/media/FuQsmXwaEAA0rwi.jpg", "type": "photo", "url": "https://t.co/5Ip5xuUQm5", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1228, "w": 1360, "resize": "fit"}, "medium": {"h": 1084, "w": 1200, "resize": "fit"}, "small": {"h": 614, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1228, "width": 1360, "focus_rects": [{"x": 0, "y": 0, "w": 1360, "h": 762}, {"x": 66, "y": 0, "w": 1228, "h": 1228}, {"x": 142, "y": 0, "w": 1077, "h": 1228}, {"x": 373, "y": 0, "w": 614, "h": 1228}, {"x": 0, "y": 0, "w": 1360, "h": 1228}]}, "media_results": {"result": {"media_key": "3_1649492401248145408"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "replit.com/@MckayWrigley/…", "expanded_url": "https://replit.com/@MckayWrigley/Takeoff-School-Your-1st-AI-App", "url": "https://t.co/jdJQaE6o3Z", "indices": [235, 258]}], "user_mentions": [{"id_str": "366289783", "name": "Replit ⠕", "screen_name": "Replit", "indices": [204, 211]}, {"id_str": "1552969862514745344", "name": "Takeoff AI", "screen_name": "TakeoffAI", "indices": [215, 225]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/5Ip5xuUQm5", "expanded_url": "https://x.com/mckaywrigley/status/1649492404943323136/photo/1", "id_str": "1649492401248145408", "indices": [259, 282], "media_key": "3_1649492401248145408", "media_url_https": "https://pbs.twimg.com/media/FuQsmXwaEAA0rwi.jpg", "type": "photo", "url": "https://t.co/5Ip5xuUQm5", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1228, "w": 1360, "resize": "fit"}, "medium": {"h": 1084, "w": 1200, "resize": "fit"}, "small": {"h": 614, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1228, "width": 1360, "focus_rects": [{"x": 0, "y": 0, "w": 1360, "h": 762}, {"x": 66, "y": 0, "w": 1228, "h": 1228}, {"x": 142, "y": 0, "w": 1077, "h": 1228}, {"x": 373, "y": 0, "w": 614, "h": 1228}, {"x": 0, "y": 0, "w": 1360, "h": 1228}]}, "media_results": {"result": {"media_key": "3_1649492401248145408"}}}]}, "favorite_count": 2259, "favorited": false, "full_text": "Come code your 1st AI app!\n\nBuild an OpenAI Q&amp;A bot in 21 lines of Python.\n\nLearn how to:\n- write Python code\n- interact with OpenAI’s API\n- build a CLI\n\n100% free. No experience required.\n\nCourse on @Replit by @TakeoffAI.\n\nLearn: https://t.co/jdJQaE6o3Z https://t.co/5Ip5xuUQm5", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 29, "reply_count": 105, "retweet_count": 411, "retweeted": false, "user_id_str": "2786431437", "id_str": "1649492404943323136"}, "twe_private_fields": {"created_at": 1682104610000, "updated_at": 1748554197458, "media_count": 1}}}, {"id": "1649753774712320002", "created_at": "2023-04-22 15:35:25 +03:00", "full_text": "Sites to earn FREE certificates:\n\n1. https://t.co/6Zuwz9RVZH\nSQL, ML, DL, Data Science\n\n2. https://t.co/JATGbiGDPI\nFront-end, Back-end, Python, ML\n\n3. https://t.co/CnQE473v75\nBlockchain, Data Science, AI, Cloud, Serverless,\nDocker, Kubernetes\n\n4. https://t.co/12QdKExa1T\nAI/ML, DL\n\n5. https://t.co/WuOsWEr1bg\nMongoDB\n\n6. https://t.co/2WCDyJSbUo *\n.NET, Azure, GitHub, SQL Server\n\n7. simplilearn. com/skillup-free-online-courses\nAI/ML, ChatGPT, Data Science, DevOps, Cloud, PM\n\n8. https://t.co/N92cYHWEjX\nData Science, Cloud Computing, DevOps, PM\n\n9. https://t.co/fzJzD5X3Fl\nSalesforce, Blockchain\n\n10. https://t.co/YBO2OyiIDO\nC, C++, Java, Python, JavaScript\n\n11. https://t.co/x5CYGFqGtU\n4000+ Free Courses", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4097, "retweet_count": 1518, "bookmark_count": 3919, "quote_count": 22, "reply_count": 112, "views_count": 472450, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1649753774712320002", "metadata": {"__typename": "Tweet", "rest_id": "1649753774712320002", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204222, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204222, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1649753774712320002"], "editable_until_msecs": "1682168725000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "472450", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE2NDk3NTM3NzQ1MjM1NTU4NDI=", "text": "Sites to earn FREE certificates:\n\n1. https://t.co/6Zuwz9RVZH\nSQL, ML, DL, Data Science\n\n2. https://t.co/JATGbiGDPI\nFront-end, Back-end, Python, ML\n\n3. https://t.co/CnQE473v75\nBlockchain, Data Science, AI, Cloud, Serverless,\nDocker, Kubernetes\n\n4. https://t.co/12QdKExa1T\nAI/ML, DL\n\n5. https://t.co/WuOsWEr1bg\nMongoDB\n\n6. https://t.co/2WCDyJSbUo *\n.NET, Azure, GitHub, SQL Server\n\n7. simplilearn. com/skillup-free-online-courses\nAI/ML, ChatGPT, Data Science, DevOps, Cloud, PM\n\n8. https://t.co/N92cYHWEjX\nData Science, Cloud Computing, DevOps, PM\n\n9. https://t.co/fzJzD5X3Fl\nSalesforce, Blockchain\n\n10. https://t.co/YBO2OyiIDO\nC, C++, Java, Python, JavaScript\n\n11. https://t.co/x5CYGFqGtU\n4000+ Free Courses", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "kaggle.com", "expanded_url": "http://kaggle.com", "url": "https://t.co/6Zuwz9RVZH", "indices": [37, 60]}, {"display_url": "freecodecamp.org", "expanded_url": "http://freecodecamp.org", "url": "https://t.co/JATGbiGDPI", "indices": [91, 114]}, {"display_url": "cognitiveclass.ai", "expanded_url": "http://cognitiveclass.ai", "url": "https://t.co/CnQE473v75", "indices": [151, 174]}, {"display_url": "matlabacademy.mathworks.com", "expanded_url": "http://matlabacademy.mathworks.com", "url": "https://t.co/12QdKExa1T", "indices": [247, 270]}, {"display_url": "learn.mongodb.com", "expanded_url": "http://learn.mongodb.com", "url": "https://t.co/WuOsWEr1bg", "indices": [285, 308]}, {"display_url": "learn.microsoft.com", "expanded_url": "http://learn.microsoft.com", "url": "https://t.co/2WCDyJSbUo", "indices": [321, 344]}, {"display_url": "mygreatlearning.com/academy/learn-…", "expanded_url": "http://mygreatlearning.com/academy/learn-for-free/courses", "url": "https://t.co/N92cYHWEjX", "indices": [480, 503]}, {"display_url": "trailhead.salesforce.com", "expanded_url": "http://trailhead.salesforce.com", "url": "https://t.co/fzJzD5X3Fl", "indices": [550, 573]}, {"display_url": "spoken-tutorial.org", "expanded_url": "http://spoken-tutorial.org", "url": "https://t.co/YBO2OyiIDO", "indices": [602, 625]}, {"display_url": "alison.com", "expanded_url": "http://alison.com", "url": "https://t.co/x5CYGFqGtU", "indices": [664, 687]}], "user_mentions": []}, "richtext": {"richtext_tags": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 3919, "bookmarked": true, "created_at": "Sat Apr 22 12:35:25 +0000 2023", "conversation_id_str": "1649753774712320002", "display_text_range": [0, 280], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "kaggle.com", "expanded_url": "http://kaggle.com", "url": "https://t.co/8OYE4viFCU", "indices": [37, 60]}, {"display_url": "freecodecamp.org", "expanded_url": "http://freecodecamp.org", "url": "https://t.co/ZBLEk0qteh", "indices": [91, 114]}, {"display_url": "cognitiveclass.ai", "expanded_url": "http://cognitiveclass.ai", "url": "https://t.co/WIW0QyreUg", "indices": [151, 174]}, {"display_url": "matlabacademy.mathworks.com", "expanded_url": "http://matlabacademy.mathworks.com", "url": "https://t.co/rlbNt5vKaP", "indices": [247, 270]}], "user_mentions": []}, "favorite_count": 4097, "favorited": false, "full_text": "Sites to earn FREE certificates:\n\n1. https://t.co/8OYE4viFCU\nSQL, ML, DL, Data Science\n\n2. https://t.co/ZBLEk0qteh\nFront-end, Back-end, Python, ML\n\n3. https://t.co/WIW0QyreUg\nBlockchain, Data Science, AI, Cloud, Serverless,\nDocker, Kubernetes\n\n4. https://t.co/rlbNt5vKaP\nAI/ML, DL", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 22, "reply_count": 112, "retweet_count": 1518, "retweeted": false, "user_id_str": "69941978", "id_str": "1649753774712320002"}, "twe_private_fields": {"created_at": 1682166925000, "updated_at": 1748554197458, "media_count": 0}}}]