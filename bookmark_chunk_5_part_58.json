[{"id": "1888516813065007323", "created_at": "2025-02-09 12:14:29 +03:00", "full_text": "Want a Cloud Engineer job in 6 months?\nStudy this roadmap:\n\n3-Month Foundation:\n• Linux fundamentals (Basic bash scripting)\n• Basic networking\n• Brush up your Coding skills (Python preferably)\n• One cloud platform (AWS/Azure/GCP)\n• Infrastructure as Code (Terraform)\n• Version control (Git)\n↳ Target: Junior Cloud Engineer roles (ave comp $95k)", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1435, "retweet_count": 208, "bookmark_count": 2201, "quote_count": 6, "reply_count": 21, "views_count": 112224, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1888516813065007323", "metadata": {"__typename": "Tweet", "rest_id": "1888516813065007323", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5NzM2NTc3MjU1MDE2ODk4NTY=", "rest_id": "973657725501689856", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1906363091396300800/ZLawyjSF_normal.jpg"}, "core": {"created_at": "Tue Mar 13 20:31:06 +0000 2018", "name": "<PERSON> 👨‍💻", "screen_name": "<PERSON><PERSON><PERSON><PERSON>op<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Cloud DevOps Engineer | 1x AWS Certified | Ex @Remoteli | Ex @TaryaFinTech | Akkadian Labs | AWS Community Builder", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/lloyd.theophil…", "expanded_url": "https://linktr.ee/lloyd.theophilus", "url": "https://t.co/G0jNMxlxpa", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 7296, "followers_count": 2547, "friends_count": 233, "has_custom_timelines": false, "is_translator": false, "listed_count": 9, "media_count": 309, "normal_followers_count": 2547, "pinned_tweet_ids_str": ["1830570804649558262"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/973657725501689856/1724883788", "profile_interstitial_type": "", "statuses_count": 4291, "translator_type": "none", "url": "https://t.co/G0jNMxlxpa", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Somewhere in Tech"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1500256455726936068", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1888516813065007323"], "editable_until_msecs": "1739096069000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "112224", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4ODg1MTY4MTMwMDYyMDA4MzI=", "text": "Want a Cloud Engineer job in 6 months?\nStudy this roadmap:\n\n3-Month Foundation:\n• Linux fundamentals (Basic bash scripting)\n• Basic networking\n• Brush up your Coding skills (Python preferably)\n• One cloud platform (AWS/Azure/GCP)\n• Infrastructure as Code (Terraform)\n• Version control (Git)\n↳ Target: Junior Cloud Engineer roles (ave comp $95k)", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 2201, "bookmarked": true, "created_at": "Sun Feb 09 09:14:29 +0000 2025", "conversation_id_str": "1888516813065007323", "display_text_range": [0, 268], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1435, "favorited": false, "full_text": "Want a Cloud Engineer job in 6 months?\nStudy this roadmap:\n\n3-Month Foundation:\n• Linux fundamentals (Basic bash scripting)\n• Basic networking\n• Brush up your Coding skills (Python preferably)\n• One cloud platform (AWS/Azure/GCP)\n• Infrastructure as Code (Terraform)\n•", "is_quote_status": false, "lang": "en", "quote_count": 6, "reply_count": 21, "retweet_count": 208, "retweeted": false, "user_id_str": "973657725501689856", "id_str": "1888516813065007323"}, "twe_private_fields": {"created_at": 1739092469000, "updated_at": 1748554096073, "media_count": 0}}}, {"id": "1888938965975335000", "created_at": "2025-02-10 16:11:58 +03:00", "full_text": "Take a look at this book for some tough and fun MySQL challenges! https://t.co/QlbBTECv4b", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": "1888937481094980048", "favorite_count": 419, "retweet_count": 43, "bookmark_count": 696, "quote_count": 0, "reply_count": 4, "views_count": 42885, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1888938965975335000", "metadata": {"__typename": "Tweet", "rest_id": "1888938965975335000", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4ODEzNjQzNDg=", "rest_id": "881364348", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1883333331900846080/Vg2JkPSK_normal.jpg"}, "core": {"created_at": "Mon Oct 15 02:16:39 +0000 2012", "name": "wangbin579", "screen_name": "wangbin579"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Specializing in solving complex software challenges, with deep expertise in TCP and MySQL kernels, and a strong passion for AI, history, math, and physics.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com/enhancedformys…", "expanded_url": "https://github.com/enhancedformysql", "url": "https://t.co/UE469F3MkL", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 1220, "followers_count": 16706, "friends_count": 124, "has_custom_timelines": false, "is_translator": false, "listed_count": 73, "media_count": 300, "normal_followers_count": 16706, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_interstitial_type": "", "statuses_count": 2543, "translator_type": "none", "url": "https://t.co/UE469F3MkL", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Beijing, China"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/QlbBTECv4b", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 200, "width": 400, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "The Art of Problem-Solving in Software Engineering: How to Make MySQL Better - enhancedformysql/The-Art-of-Problem-Solving-in-Software-Engineering_How-to-Make-MySQL-Better", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 600, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=600x600"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 72, "width": 144, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "The Art of Problem-Solving in Software Engineering: How to Make MySQL Better - enhancedformysql/The-Art-of-Problem-Solving-in-Software-Engineering_How-to-Make-MySQL-Better", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "The Art of Problem-Solving in Software Engineering: How to Make MySQL Better - enhancedformysql/The-Art-of-Problem-Solving-in-Software-Engineering_How-to-Make-MySQL-Better", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 95.06}, {"rgb": {"blue": 101, "green": 202, "red": 170}, "percentage": 2.66}, {"rgb": {"blue": 130, "green": 126, "red": 121}, "percentage": 2.04}, {"rgb": {"blue": 216, "green": 200, "red": 193}, "percentage": 0.15}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "GitHub - enhancedformysql/The-Art-of-Problem-Solving-in-Software-Engineering_How-to-Make-MySQL-Be...", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 95.06}, {"rgb": {"blue": 101, "green": 202, "red": 170}, "percentage": 2.66}, {"rgb": {"blue": 130, "green": 126, "red": 121}, "percentage": 2.04}, {"rgb": {"blue": 216, "green": 200, "red": 193}, "percentage": 0.15}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 95.06}, {"rgb": {"blue": 101, "green": 202, "red": 170}, "percentage": 2.66}, {"rgb": {"blue": 130, "green": 126, "red": 121}, "percentage": 2.04}, {"rgb": {"blue": 216, "green": 200, "red": 193}, "percentage": 0.15}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/QlbBTECv4b", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1925764048672460800/cLdkiuQU?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/QlbBTECv4b", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620091, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17801, "media_count": 2646, "normal_followers_count": 2620091, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1888938965975335000"], "editable_until_msecs": "1739196718000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "42885", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "quoted_status_result": {"result": {"__typename": "Tweet", "rest_id": "1888937481094980048", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMDY5MDc0MTkz", "rest_id": "3069074193", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/577677829875216384/aw7qZHjN_normal.jpeg"}, "core": {"created_at": "Wed Mar 04 02:37:33 +0000 2015", "name": "Night's King", "screen_name": "intotheFLUX"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Soy lo desconocido. Constantemente descubriéndose a sí mismo.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 7123, "followers_count": 49, "friends_count": 336, "has_custom_timelines": true, "is_translator": false, "listed_count": 3, "media_count": 42, "normal_followers_count": 49, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/3069074193/1426564049", "profile_interstitial_type": "", "statuses_count": 1730, "translator_type": "none", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Rosario, Argentina"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1888937481094980048"], "editable_until_msecs": "1739196364000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "45487", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 4, "bookmarked": false, "created_at": "Mon Feb 10 13:06:04 +0000 2025", "conversation_id_str": "1888932425218285672", "display_text_range": [12, 87], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "881364348", "name": "wangbin579", "screen_name": "wangbin579", "indices": [0, 11]}]}, "favorite_count": 11, "favorited": false, "full_text": "@wangbin579 Can you name a couple of intermediate/difficult complex issues for each DB?", "in_reply_to_screen_name": "wangbin579", "in_reply_to_status_id_str": "1888932425218285672", "in_reply_to_user_id_str": "881364348", "is_quote_status": false, "lang": "en", "quote_count": 1, "reply_count": 1, "retweet_count": 1, "retweeted": false, "user_id_str": "3069074193", "id_str": "1888937481094980048"}}}, "legacy": {"bookmark_count": 696, "bookmarked": true, "created_at": "Mon Feb 10 13:11:58 +0000 2025", "conversation_id_str": "1888938965975335000", "display_text_range": [0, 89], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/enhancedformys…", "expanded_url": "https://github.com/enhancedformysql/The-Art-of-Problem-Solving-in-Software-Engineering_How-to-Make-MySQL-Better", "url": "https://t.co/QlbBTECv4b", "indices": [66, 89]}], "user_mentions": []}, "favorite_count": 419, "favorited": false, "full_text": "Take a look at this book for some tough and fun MySQL challenges! https://t.co/QlbBTECv4b", "is_quote_status": true, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "quoted_status_id_str": "1888937481094980048", "quoted_status_permalink": {"url": "https://t.co/xTvfBHw1aK", "expanded": "https://twitter.com/intotheFLUX/status/1888937481094980048", "display": "x.com/intotheFLUX/st…"}, "reply_count": 4, "retweet_count": 43, "retweeted": false, "user_id_str": "881364348", "id_str": "1888938965975335000"}, "twe_private_fields": {"created_at": 1739193118000, "updated_at": 1748554096073, "media_count": 0}}}]