[{"id": "1563165371254812677", "created_at": "2022-08-26 17:04:01 +03:00", "full_text": "When you're learning Python, there are many key algorithms and data structures you'll need to know.\n\nThey'll come up in job interviews, and you'll use them on a daily basis, too. \n\nYou'll learn how to use them in this comprehensive Python DSA course.\n\nhttps://t.co/XiLfBX1qe7", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 515, "retweet_count": 105, "bookmark_count": 213, "quote_count": 1, "reply_count": 9, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1563165371254812677", "metadata": {"__typename": "Tweet", "rest_id": "1563165371254812677", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjY4MTAwMTQy", "rest_id": "1668100142", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1276770212927410176/qTgTIejk_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> Aug 13 15:27:51 +0000 2013", "name": "freeCodeCamp.org", "screen_name": "freeCodeCamp"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "We're a community of millions of people who are building new skills and getting new jobs together. A 501(c)(3) public charity. Tweets by @abbeyrenn.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "freecodecamp.org", "expanded_url": "https://www.freecodecamp.org", "url": "https://t.co/LHs4dxj9fJ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 73582, "followers_count": 902203, "friends_count": 158, "has_custom_timelines": false, "is_translator": false, "listed_count": 6764, "media_count": 2468, "normal_followers_count": 902203, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1668100142/1711032731", "profile_interstitial_type": "", "statuses_count": 34046, "translator_type": "none", "url": "https://t.co/LHs4dxj9fJ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Just here on Earth... for now"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1563165371254812677"], "editable_until_msecs": "1661524441000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://buffer.com\" rel=\"nofollow\"><PERSON><PERSON>er App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 213, "bookmarked": true, "created_at": "Fri Aug 26 14:04:01 +0000 2022", "conversation_id_str": "1563165371254812677", "display_text_range": [0, 275], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "freecodecamp.org/news/learn-alg…", "expanded_url": "https://www.freecodecamp.org/news/learn-algorithms-and-data-structures-in-python/", "url": "https://t.co/XiLfBX1qe7", "indices": [252, 275]}], "user_mentions": []}, "favorite_count": 515, "favorited": false, "full_text": "When you're learning Python, there are many key algorithms and data structures you'll need to know.\n\nThey'll come up in job interviews, and you'll use them on a daily basis, too. \n\nYou'll learn how to use them in this comprehensive Python DSA course.\n\nhttps://t.co/XiLfBX1qe7", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 9, "retweet_count": 105, "retweeted": false, "user_id_str": "1668100142", "id_str": "1563165371254812677"}, "twe_private_fields": {"created_at": 1661522641000, "updated_at": 1748554240582, "media_count": 0}}}, {"id": "1563187313181859841", "created_at": "2022-08-26 18:31:12 +03:00", "full_text": "Learn Data Analysis in Python for free 🥳\nBookmark this thread\n\nA thread🧵👇", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 6773, "retweet_count": 2047, "bookmark_count": 8239, "quote_count": 32, "reply_count": 173, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1563187313181859841", "metadata": {"__typename": "Tweet", "rest_id": "1563187313181859841", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyOTM0NDI4Nzkx", "rest_id": "2934428791", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1870161741793071104/vPa9lswu_normal.jpg"}, "core": {"created_at": "Thu Dec 18 06:50:24 +0000 2014", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "nevrekaraishwa2"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "I post about data science and memes.\n\nYour only limit is your mind.\n\nDM for Collaborations 📩", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com/aishwaryanevre…", "expanded_url": "https://github.com/a<PERSON>war<PERSON><PERSON><PERSON><PERSON>/The-Complete-DS-Guide", "url": "https://t.co/5td5zXtKv9", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 24542, "followers_count": 45723, "friends_count": 1140, "has_custom_timelines": true, "is_translator": false, "listed_count": 905, "media_count": 1150, "normal_followers_count": 45723, "pinned_tweet_ids_str": ["1536020000376778752"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2934428791/1735746796", "profile_interstitial_type": "", "statuses_count": 7895, "translator_type": "none", "url": "https://t.co/5td5zXtKv9", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Remote"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1465034567140605954", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1563187313181859841"], "editable_until_msecs": "1661529672000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 8239, "bookmarked": true, "created_at": "Fri Aug 26 15:31:12 +0000 2022", "conversation_id_str": "1563187313181859841", "display_text_range": [0, 73], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 6773, "favorited": false, "full_text": "Learn Data Analysis in Python for free 🥳\nBookmark this thread\n\nA thread🧵👇", "is_quote_status": false, "lang": "en", "quote_count": 32, "reply_count": 173, "retweet_count": 2047, "retweeted": false, "user_id_str": "2934428791", "id_str": "1563187313181859841"}, "twe_private_fields": {"created_at": 1661527872000, "updated_at": 1748554240582, "media_count": 0}}}]