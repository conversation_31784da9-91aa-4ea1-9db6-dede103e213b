[{"id": "1576705586384838656", "created_at": "2022-10-03 01:48:00 +03:00", "full_text": "8 best acupressure points to treat body pains &amp; aches https://t.co/CSeMOHaACX", "media": [{"type": "video", "url": "https://t.co/CSeMOHaACX", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1576645228765016064/pu/img/WEolGhmXU7t8BgCd.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/640x640/ie-BBTC7qfg9yeS9.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 41767, "retweet_count": 12331, "bookmark_count": 15225, "quote_count": 267, "reply_count": 268, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1576705586384838656", "metadata": {"__typename": "Tweet", "rest_id": "1576705586384838656", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDQ0NzYwMjI3NTc2ODgxMTU2", "rest_id": "1444760227576881156", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1592626494546796545/YxyPborr_normal.jpg"}, "core": {"created_at": "Sun Oct 03 20:24:36 +0000 2021", "name": "HOW THINGS WORK", "screen_name": "HowThingsWork_"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "The Official How Things Work page including Tech, AI & loads more. Also all the best News & Viral content from around the globe.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "onlysearch.com", "expanded_url": "http://onlysearch.com", "url": "https://t.co/gGc0nkirbj", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 22845, "followers_count": 2392431, "friends_count": 7509, "has_custom_timelines": true, "is_translator": false, "listed_count": 7033, "media_count": 7114, "normal_followers_count": 2392431, "pinned_tweet_ids_str": ["1927835147849597192"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1444760227576881156/1727288400", "profile_interstitial_type": "", "statuses_count": 12551, "translator_type": "none", "url": "https://t.co/gGc0nkirbj", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "<EMAIL>"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1590388789792342016", "professional_type": "Business", "category": [{"id": 580, "name": "Media & News Company", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1576705586384838656"], "editable_until_msecs": "1664752680000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 15225, "bookmarked": true, "created_at": "Sun Oct 02 22:48:00 +0000 2022", "conversation_id_str": "1576705586384838656", "display_text_range": [0, 57], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/CSeMOHaACX", "expanded_url": "https://x.com/wowinteresting8/status/1576705586384838656/video/1", "id_str": "1576645228765016064", "indices": [58, 81], "media_key": "7_1576645228765016064", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1576645228765016064/pu/img/WEolGhmXU7t8BgCd.jpg", "type": "video", "url": "https://t.co/CSeMOHaACX", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 640, "w": 640, "resize": "fit"}, "medium": {"h": 640, "w": 640, "resize": "fit"}, "small": {"h": 640, "w": 640, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 640, "width": 640, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "duration_millis": 59580, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/pl/uC5gwxC94Jycg486.m3u8?tag=12"}, {"bitrate": 432000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/320x320/cOec8YAicqpm6yIJ.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/540x540/D0yGDYOXEc4vFVGu.mp4?tag=12"}, {"bitrate": 1280000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/640x640/ie-BBTC7qfg9yeS9.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1576645228765016064"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/CSeMOHaACX", "expanded_url": "https://x.com/wowinteresting8/status/1576705586384838656/video/1", "id_str": "1576645228765016064", "indices": [58, 81], "media_key": "7_1576645228765016064", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1576645228765016064/pu/img/WEolGhmXU7t8BgCd.jpg", "type": "video", "url": "https://t.co/CSeMOHaACX", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 640, "w": 640, "resize": "fit"}, "medium": {"h": 640, "w": 640, "resize": "fit"}, "small": {"h": 640, "w": 640, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 640, "width": 640, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "duration_millis": 59580, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/pl/uC5gwxC94Jycg486.m3u8?tag=12"}, {"bitrate": 432000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/320x320/cOec8YAicqpm6yIJ.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/540x540/D0yGDYOXEc4vFVGu.mp4?tag=12"}, {"bitrate": 1280000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1576645228765016064/pu/vid/640x640/ie-BBTC7qfg9yeS9.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1576645228765016064"}}}]}, "favorite_count": 41767, "favorited": false, "full_text": "8 best acupressure points to treat body pains &amp; aches https://t.co/CSeMOHaACX", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 267, "reply_count": 268, "retweet_count": 12331, "retweeted": false, "user_id_str": "1444760227576881156", "id_str": "1576705586384838656"}, "twe_private_fields": {"created_at": 1664750880000, "updated_at": 1748554235792, "media_count": 1}}}, {"id": "1576837977439109122", "created_at": "2022-10-03 10:34:04 +03:00", "full_text": "You don't need to code in 2022.\n\nThere's a no-code solution for everything. https://t.co/2Cn2g7C3yZ", "media": [{"type": "photo", "url": "https://t.co/2Cn2g7C3yZ", "thumbnail": "https://pbs.twimg.com/media/FeIMY1FWQAcwpUL?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FeIMY1FWQAcwpUL?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 24601, "retweet_count": 4092, "bookmark_count": 17878, "quote_count": 292, "reply_count": 467, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1576837977439109122", "metadata": {"__typename": "Tweet", "rest_id": "1576837977439109122", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTI0ODE0NzQ0NzI4Nzg0ODk3", "rest_id": "1524814744728784897", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1630482873634656261/h92S7p-Q_normal.jpg"}, "core": {"created_at": "Thu May 12 18:12:28 +0000 2022", "name": "Sveta Bay", "screen_name": "sveta_bay"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "🛠 https://t.co/MVP6vr0Pcw & https://t.co/ybWSmdJwUs \n\n👩‍💻 Indie Entrepreneur & Nomad", "entities": {"description": {"urls": [{"display_url": "MakerBox.club", "expanded_url": "http://MakerBox.club", "url": "https://t.co/MVP6vr0Pcw", "indices": [2, 25]}, {"display_url": "FounderPal.ai", "expanded_url": "http://FounderPal.ai", "url": "https://t.co/ybWSmdJwUs", "indices": [28, 51]}]}, "url": {"urls": [{"display_url": "makerbox.club/ai-wrapper-cou…", "expanded_url": "https://www.makerbox.club/ai-wrapper-course", "url": "https://t.co/mJEsjKbTqm", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 26150, "followers_count": 29679, "friends_count": 301, "has_custom_timelines": true, "is_translator": false, "listed_count": 380, "media_count": 1664, "normal_followers_count": 29679, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1524814744728784897/1677572388", "profile_interstitial_type": "", "statuses_count": 11432, "translator_type": "none", "url": "https://t.co/mJEsjKbTqm", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Course \"AI wrapper in 5 days\""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1542884553785745408", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1576837977439109122"], "editable_until_msecs": "1664784244000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 17878, "bookmarked": true, "created_at": "Mon Oct 03 07:34:04 +0000 2022", "conversation_id_str": "1576837977439109122", "display_text_range": [0, 75], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/2Cn2g7C3yZ", "expanded_url": "https://x.com/sveta_bay/status/1576837977439109122/photo/1", "id_str": "1576836440238997511", "indices": [76, 99], "media_key": "3_1576836440238997511", "media_url_https": "https://pbs.twimg.com/media/FeIMY1FWQAcwpUL.jpg", "type": "photo", "url": "https://t.co/2Cn2g7C3yZ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 217, "y": 489, "h": 202, "w": 202}, {"x": 172, "y": 99, "h": 257, "w": 257}, {"x": 1170, "y": 101, "h": 250, "w": 250}, {"x": 1193, "y": 637, "h": 276, "w": 276}]}, "medium": {"faces": [{"x": 161, "y": 363, "h": 150, "w": 150}, {"x": 127, "y": 73, "h": 190, "w": 190}, {"x": 868, "y": 75, "h": 185, "w": 185}, {"x": 885, "y": 473, "h": 204, "w": 204}]}, "small": {"faces": [{"x": 91, "y": 205, "h": 85, "w": 85}, {"x": 72, "y": 41, "h": 108, "w": 108}, {"x": 492, "y": 42, "h": 105, "w": 105}, {"x": 502, "y": 268, "h": 116, "w": 116}]}, "orig": {"faces": [{"x": 217, "y": 489, "h": 202, "w": 202}, {"x": 172, "y": 99, "h": 257, "w": 257}, {"x": 1170, "y": 101, "h": 250, "w": 250}, {"x": 1193, "y": 637, "h": 276, "w": 276}]}}, "sizes": {"large": {"h": 1616, "w": 1616, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1616, "width": 1616, "focus_rects": [{"x": 0, "y": 0, "w": 1616, "h": 905}, {"x": 0, "y": 0, "w": 1616, "h": 1616}, {"x": 198, "y": 0, "w": 1418, "h": 1616}, {"x": 808, "y": 0, "w": 808, "h": 1616}, {"x": 0, "y": 0, "w": 1616, "h": 1616}]}, "media_results": {"result": {"media_key": "3_1576836440238997511"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/2Cn2g7C3yZ", "expanded_url": "https://x.com/sveta_bay/status/1576837977439109122/photo/1", "id_str": "1576836440238997511", "indices": [76, 99], "media_key": "3_1576836440238997511", "media_url_https": "https://pbs.twimg.com/media/FeIMY1FWQAcwpUL.jpg", "type": "photo", "url": "https://t.co/2Cn2g7C3yZ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 217, "y": 489, "h": 202, "w": 202}, {"x": 172, "y": 99, "h": 257, "w": 257}, {"x": 1170, "y": 101, "h": 250, "w": 250}, {"x": 1193, "y": 637, "h": 276, "w": 276}]}, "medium": {"faces": [{"x": 161, "y": 363, "h": 150, "w": 150}, {"x": 127, "y": 73, "h": 190, "w": 190}, {"x": 868, "y": 75, "h": 185, "w": 185}, {"x": 885, "y": 473, "h": 204, "w": 204}]}, "small": {"faces": [{"x": 91, "y": 205, "h": 85, "w": 85}, {"x": 72, "y": 41, "h": 108, "w": 108}, {"x": 492, "y": 42, "h": 105, "w": 105}, {"x": 502, "y": 268, "h": 116, "w": 116}]}, "orig": {"faces": [{"x": 217, "y": 489, "h": 202, "w": 202}, {"x": 172, "y": 99, "h": 257, "w": 257}, {"x": 1170, "y": 101, "h": 250, "w": 250}, {"x": 1193, "y": 637, "h": 276, "w": 276}]}}, "sizes": {"large": {"h": 1616, "w": 1616, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1616, "width": 1616, "focus_rects": [{"x": 0, "y": 0, "w": 1616, "h": 905}, {"x": 0, "y": 0, "w": 1616, "h": 1616}, {"x": 198, "y": 0, "w": 1418, "h": 1616}, {"x": 808, "y": 0, "w": 808, "h": 1616}, {"x": 0, "y": 0, "w": 1616, "h": 1616}]}, "media_results": {"result": {"media_key": "3_1576836440238997511"}}}]}, "favorite_count": 24601, "favorited": false, "full_text": "You don't need to code in 2022.\n\nThere's a no-code solution for everything. https://t.co/2Cn2g7C3yZ", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 292, "reply_count": 467, "retweet_count": 4092, "retweeted": false, "user_id_str": "1524814744728784897", "id_str": "1576837977439109122"}, "twe_private_fields": {"created_at": 1664782444000, "updated_at": 1748554235792, "media_count": 1}}}]