[{"id": "1525195005438091264", "created_at": "2022-05-13 22:23:20 +03:00", "full_text": "Top 10 Github Repositories to Learn JavaScript\n\n🧶 thread ↓", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1761, "retweet_count": 498, "bookmark_count": 1175, "quote_count": 12, "reply_count": 55, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1525195005438091264", "metadata": {"__typename": "Tweet", "rest_id": "1525195005438091264", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjI0NTM4NjQ0MjQ3MjAzODQ0", "rest_id": "1224538644247203844", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1888357542004162560/5D-YlH5e_normal.jpg"}, "core": {"created_at": "Tue Feb 04 03:42:32 +0000 2020", "name": "Khairallah AL-Awady", "screen_name": "eng_khai<PERSON>lah1"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Founder @Web3Arabs 🚀 | working on @Starknet // @pwrlabs | Blockchain Engineer 💻 Math 🦞👽 | don’t trust, verify", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com/KhairallahA", "expanded_url": "https://github.com/KhairallahA", "url": "https://t.co/oPA0O5k5Qy", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 95436, "followers_count": 14326, "friends_count": 976, "has_custom_timelines": true, "is_translator": false, "listed_count": 467, "media_count": 369, "normal_followers_count": 14326, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1224538644247203844/1691590897", "profile_interstitial_type": "", "statuses_count": 23054, "translator_type": "none", "url": "https://t.co/oPA0O5k5Qy", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Onchain"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460891436401012737", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "ethereum_handle": "******************************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1525195005438091264"], "editable_until_msecs": "1652471600000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1175, "bookmarked": true, "created_at": "Fri May 13 19:23:20 +0000 2022", "conversation_id_str": "1525195005438091264", "display_text_range": [0, 58], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1761, "favorited": false, "full_text": "Top 10 Github Repositories to Learn JavaScript\n\n🧶 thread ↓", "is_quote_status": false, "lang": "en", "quote_count": 12, "reply_count": 55, "retweet_count": 498, "retweeted": false, "user_id_str": "1224538644247203844", "id_str": "1525195005438091264"}, "twe_private_fields": {"created_at": 1652469800000, "updated_at": 1748554346019, "media_count": 0}}}, {"id": "1525466667249180672", "created_at": "2022-05-14 16:22:49 +03:00", "full_text": "I started establishing “Anti-Goals” for all new projects.\n\nEverything changed.\n\nHere’s how it works:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 8681, "retweet_count": 1654, "bookmark_count": 6619, "quote_count": 241, "reply_count": 272, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1525466667249180672", "metadata": {"__typename": "Tweet", "rest_id": "1525466667249180672", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTI2ODE5NTM=", "rest_id": "312681953", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1586859332104343552/V1HRpbP1_normal.jpg"}, "core": {"created_at": "<PERSON>e Jun 07 14:14:17 +0000 2011", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "NYT Bestselling Author of The 5 Types of Wealth. Gave up a grand slam on ESPN in 2012 and still waiting for it to land. Order my book below 👇", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "the5typesofwealth.com", "expanded_url": "https://www.the5typesofwealth.com", "url": "https://t.co/exGtZduEvq", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 151240, "followers_count": 1080264, "friends_count": 278, "has_custom_timelines": true, "is_translator": false, "listed_count": 16659, "media_count": 4870, "normal_followers_count": 1080264, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/312681953/1739539616", "profile_interstitial_type": "", "statuses_count": 69302, "translator_type": "none", "url": "https://t.co/exGtZduEvq", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "New York, USA"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1675186015294611456", "professional_type": "Business", "category": [{"id": 957, "name": "Author", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "<PERSON><PERSON><PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1525466667249180672"], "editable_until_msecs": "1652536369000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"http://itunes.apple.com/us/app/twitter/id409789998?mt=12\" rel=\"nofollow\">Twitter for Mac</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 6619, "bookmarked": true, "created_at": "Sat May 14 13:22:49 +0000 2022", "conversation_id_str": "1525466667249180672", "display_text_range": [0, 100], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 8681, "favorited": false, "full_text": "I started establishing “Anti-Goals” for all new projects.\n\nEverything changed.\n\nHere’s how it works:", "is_quote_status": false, "lang": "en", "quote_count": 241, "reply_count": 272, "retweet_count": 1654, "retweeted": false, "user_id_str": "312681953", "id_str": "1525466667249180672"}, "twe_private_fields": {"created_at": 1652534569000, "updated_at": 1748554346018, "media_count": 0}}}]