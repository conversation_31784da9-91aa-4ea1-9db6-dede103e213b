[{"id": "1607233072042479618", "created_at": "2022-12-26 07:33:19 +03:00", "full_text": "Learn Statistics with these FREE Online Courses- https://t.co/04Hg1ngmsL\n\n#Machinelearning #100DaysOfCode #AI #IoT #100DaysOfMLCode #Python #javascript #Serverless #womenwhocode #cybersecurity #RStats #CodeNewbie #DataScience #DEVCommunity  #BigData #Analytics #TensorFlow https://t.co/wTszBhBtwT", "media": [{"type": "photo", "url": "https://t.co/wTszBhBtwT", "thumbnail": "https://pbs.twimg.com/media/Fk4J25paMAMnt9q?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Fk4J25paMAMnt9q?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2268, "retweet_count": 571, "bookmark_count": 835, "quote_count": 10, "reply_count": 21, "views_count": 239295, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1607233072042479618", "metadata": {"__typename": "Tweet", "rest_id": "1607233072042479618", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjM1ODU4OTY0MTAyNzg3MDc3", "rest_id": "1235858964102787077", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1235859189026582528/uiwnrE2k_normal.png"}, "core": {"created_at": "Fri Mar 06 09:25:39 +0000 2020", "name": "tut_ml", "screen_name": "tut_ml"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "We are providing Machine Learning Blogs on every important topic @ https://t.co/RIxNezrfe0. Our aim is to clear doubts who want to learn Machine Learning. Get one blog daily.", "entities": {"description": {"urls": [{"display_url": "mltut.com", "expanded_url": "http://mltut.com", "url": "https://t.co/RIxNezrfe0", "indices": [67, 90]}]}, "url": {"urls": [{"display_url": "mltut.com", "expanded_url": "http://www.mltut.com", "url": "https://t.co/Ib5uXU3RnF", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 653, "followers_count": 20659, "friends_count": 169, "has_custom_timelines": false, "is_translator": false, "listed_count": 248, "media_count": 1652, "normal_followers_count": 20659, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1235858964102787077/1642149389", "profile_interstitial_type": "", "statuses_count": 5520, "translator_type": "none", "url": "https://t.co/Ib5uXU3RnF", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1607233072042479618"], "editable_until_msecs": "1672030999000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "239295", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 835, "bookmarked": true, "created_at": "Mon Dec 26 04:33:19 +0000 2022", "conversation_id_str": "1607233072042479618", "display_text_range": [0, 272], "entities": {"hashtags": [{"indices": [74, 90], "text": "Machinelearning"}, {"indices": [91, 105], "text": "100DaysOfCode"}, {"indices": [106, 109], "text": "AI"}, {"indices": [110, 114], "text": "IoT"}, {"indices": [115, 131], "text": "100DaysOfMLCode"}, {"indices": [132, 139], "text": "Python"}, {"indices": [140, 151], "text": "javascript"}, {"indices": [152, 163], "text": "Serverless"}, {"indices": [164, 177], "text": "womenwhocode"}, {"indices": [178, 192], "text": "cybersecurity"}, {"indices": [193, 200], "text": "RStats"}, {"indices": [201, 212], "text": "CodeNewbie"}, {"indices": [213, 225], "text": "DataScience"}, {"indices": [226, 239], "text": "DEVCommunity"}, {"indices": [241, 249], "text": "BigData"}, {"indices": [250, 260], "text": "Analytics"}, {"indices": [261, 272], "text": "TensorFlow"}], "media": [{"display_url": "pic.x.com/wTszBhBtwT", "expanded_url": "https://x.com/tut_ml/status/1607233072042479618/photo/1", "id_str": "1607232955809935363", "indices": [273, 296], "media_key": "3_1607232955809935363", "media_url_https": "https://pbs.twimg.com/media/Fk4J25paMAMnt9q.jpg", "type": "photo", "url": "https://t.co/wTszBhBtwT", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 31, "y": 147, "h": 107, "w": 107}]}, "medium": {"faces": [{"x": 31, "y": 147, "h": 107, "w": 107}]}, "small": {"faces": [{"x": 19, "y": 91, "h": 66, "w": 66}]}, "orig": {"faces": [{"x": 31, "y": 147, "h": 107, "w": 107}]}}, "sizes": {"large": {"h": 1090, "w": 800, "resize": "fit"}, "medium": {"h": 1090, "w": 800, "resize": "fit"}, "small": {"h": 680, "w": 499, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1090, "width": 800, "focus_rects": [{"x": 0, "y": 0, "w": 800, "h": 448}, {"x": 0, "y": 0, "w": 800, "h": 800}, {"x": 0, "y": 0, "w": 800, "h": 912}, {"x": 0, "y": 0, "w": 545, "h": 1090}, {"x": 0, "y": 0, "w": 800, "h": 1090}]}, "media_results": {"result": {"media_key": "3_1607232955809935363"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "mltut.com/best-free-onli…", "expanded_url": "https://www.mltut.com/best-free-online-courses-for-statistics/", "url": "https://t.co/04Hg1ngmsL", "indices": [49, 72]}], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/wTszBhBtwT", "expanded_url": "https://x.com/tut_ml/status/1607233072042479618/photo/1", "id_str": "1607232955809935363", "indices": [273, 296], "media_key": "3_1607232955809935363", "media_url_https": "https://pbs.twimg.com/media/Fk4J25paMAMnt9q.jpg", "type": "photo", "url": "https://t.co/wTszBhBtwT", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 31, "y": 147, "h": 107, "w": 107}]}, "medium": {"faces": [{"x": 31, "y": 147, "h": 107, "w": 107}]}, "small": {"faces": [{"x": 19, "y": 91, "h": 66, "w": 66}]}, "orig": {"faces": [{"x": 31, "y": 147, "h": 107, "w": 107}]}}, "sizes": {"large": {"h": 1090, "w": 800, "resize": "fit"}, "medium": {"h": 1090, "w": 800, "resize": "fit"}, "small": {"h": 680, "w": 499, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1090, "width": 800, "focus_rects": [{"x": 0, "y": 0, "w": 800, "h": 448}, {"x": 0, "y": 0, "w": 800, "h": 800}, {"x": 0, "y": 0, "w": 800, "h": 912}, {"x": 0, "y": 0, "w": 545, "h": 1090}, {"x": 0, "y": 0, "w": 800, "h": 1090}]}, "media_results": {"result": {"media_key": "3_1607232955809935363"}}}]}, "favorite_count": 2268, "favorited": false, "full_text": "Learn Statistics with these FREE Online Courses- https://t.co/04Hg1ngmsL\n\n#Machinelearning #100DaysOfCode #AI #IoT #100DaysOfMLCode #Python #javascript #Serverless #womenwhocode #cybersecurity #RStats #CodeNewbie #DataScience #DEVCommunity  #BigData #Analytics #TensorFlow https://t.co/wTszBhBtwT", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 10, "reply_count": 21, "retweet_count": 571, "retweeted": false, "user_id_str": "1235858964102787077", "id_str": "1607233072042479618"}, "twe_private_fields": {"created_at": 1672029199000, "updated_at": 1748554206264, "media_count": 1}}}, {"id": "1607866424420372481", "created_at": "2022-12-28 01:30:02 +03:00", "full_text": "90+ Linux commands that Linux sysadmins and power Linux users regularly use. (bookmark this):", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1403, "retweet_count": 391, "bookmark_count": 1174, "quote_count": 7, "reply_count": 62, "views_count": 156588, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1607866424420372481", "metadata": {"__typename": "Tweet", "rest_id": "1607866424420372481", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDEyNTA2NzQxMzE3NzAxNjM2", "rest_id": "1412506741317701636", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1868295303696121856/Rj6ByJLR_normal.jpg"}, "core": {"created_at": "<PERSON><PERSON> 06 20:21:55 +0000 2021", "name": "TRÄW🤟", "screen_name": "thatstraw"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Linux & fun, I use Arch (BTW). Digital nomad, Network Engineer. Having fun (@sysxplore).", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "thatstraw.bsky.social", "expanded_url": "http://thatstraw.bsky.social", "url": "https://t.co/OnxD7NLgvO", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 23284, "followers_count": 87755, "friends_count": 124, "has_custom_timelines": true, "is_translator": false, "listed_count": 747, "media_count": 2100, "normal_followers_count": 87755, "pinned_tweet_ids_str": ["1835951330168565918"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1412506741317701636/1716228266", "profile_interstitial_type": "", "statuses_count": 11952, "translator_type": "none", "url": "https://t.co/OnxD7NLgvO", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1456707596791042050", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1607866424420372481"], "editable_until_msecs": "1672182002000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "156588", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1174, "bookmarked": true, "created_at": "<PERSON>e Dec 27 22:30:02 +0000 2022", "conversation_id_str": "1607866424420372481", "display_text_range": [0, 93], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 1403, "favorited": false, "full_text": "90+ Linux commands that Linux sysadmins and power Linux users regularly use. (bookmark this):", "is_quote_status": false, "lang": "en", "quote_count": 7, "reply_count": 62, "retweet_count": 391, "retweeted": false, "user_id_str": "1412506741317701636", "id_str": "1607866424420372481"}, "twe_private_fields": {"created_at": 1672180202000, "updated_at": 1748554204513, "media_count": 0}}}]