[{"id": "1650182557168746496", "created_at": "2023-04-23 19:59:15 +03:00", "full_text": "20 ChatGPT prompts to finish hours of work in seconds: https://t.co/XoFs2GGboH", "media": [{"type": "photo", "url": "https://t.co/XoFs2GGboH", "thumbnail": "https://pbs.twimg.com/media/FuagSg4WAAA6b5o?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FuagSg4WAAA6b5o?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 6464, "retweet_count": 1160, "bookmark_count": 11586, "quote_count": 41, "reply_count": 169, "views_count": 1331999, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1650182557168746496", "metadata": {"__typename": "Tweet", "rest_id": "1650182557168746496", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMDkzMDg5NzA0MzczOTAzMzYw", "rest_id": "1093089704373903360", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1679831685985075202/rmC9eVnN_normal.jpg"}, "core": {"created_at": "Wed Feb 06 10:11:08 +0000 2019", "name": "<PERSON><PERSON>", "screen_name": "moritzkremb"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I build things with AI and share what I learn along the way | Building https://t.co/YtGFkH3nJ7 ($18k/m) and https://t.co/SFlWrWIvmw", "entities": {"description": {"urls": [{"display_url": "copycoder.ai", "expanded_url": "https://copycoder.ai", "url": "https://t.co/YtGFkH3nJ7", "indices": [71, 94]}, {"display_url": "vireel.com", "expanded_url": "https://vireel.com", "url": "https://t.co/SFlWrWIvmw", "indices": [108, 131]}]}, "url": {"urls": [{"display_url": "youtube.com/@promptwarrior", "expanded_url": "https://www.youtube.com/@promptwarrior", "url": "https://t.co/uGnPKgHt35", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 10048, "followers_count": 70296, "friends_count": 504, "has_custom_timelines": true, "is_translator": false, "listed_count": 2135, "media_count": 1432, "normal_followers_count": 70296, "pinned_tweet_ids_str": ["1919380108667662495"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1093089704373903360/1742654498", "profile_interstitial_type": "", "statuses_count": 8919, "translator_type": "none", "url": "https://t.co/uGnPKgHt35", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "My Youtube →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1650182557168746496"], "editable_until_msecs": "1682270955000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1331999", "state": "EnabledWithCount"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 11586, "bookmarked": true, "created_at": "Sun Apr 23 16:59:15 +0000 2023", "conversation_id_str": "1650182557168746496", "display_text_range": [0, 54], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/XoFs2GGboH", "expanded_url": "https://x.com/moritzkremb/status/1650182557168746496/photo/1", "id_str": "1650182553402212352", "indices": [55, 78], "media_key": "3_1650182553402212352", "media_url_https": "https://pbs.twimg.com/media/FuagSg4WAAA6b5o.jpg", "type": "photo", "url": "https://t.co/XoFs2GGboH", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1552, "w": 1820, "resize": "fit"}, "medium": {"h": 1023, "w": 1200, "resize": "fit"}, "small": {"h": 580, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1552, "width": 1820, "focus_rects": [{"x": 0, "y": 264, "w": 1820, "h": 1019}, {"x": 134, "y": 0, "w": 1552, "h": 1552}, {"x": 230, "y": 0, "w": 1361, "h": 1552}, {"x": 522, "y": 0, "w": 776, "h": 1552}, {"x": 0, "y": 0, "w": 1820, "h": 1552}]}, "media_results": {"result": {"media_key": "3_1650182553402212352"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/XoFs2GGboH", "expanded_url": "https://x.com/moritzkremb/status/1650182557168746496/photo/1", "id_str": "1650182553402212352", "indices": [55, 78], "media_key": "3_1650182553402212352", "media_url_https": "https://pbs.twimg.com/media/FuagSg4WAAA6b5o.jpg", "type": "photo", "url": "https://t.co/XoFs2GGboH", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1552, "w": 1820, "resize": "fit"}, "medium": {"h": 1023, "w": 1200, "resize": "fit"}, "small": {"h": 580, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1552, "width": 1820, "focus_rects": [{"x": 0, "y": 264, "w": 1820, "h": 1019}, {"x": 134, "y": 0, "w": 1552, "h": 1552}, {"x": 230, "y": 0, "w": 1361, "h": 1552}, {"x": 522, "y": 0, "w": 776, "h": 1552}, {"x": 0, "y": 0, "w": 1820, "h": 1552}]}, "media_results": {"result": {"media_key": "3_1650182553402212352"}}}]}, "favorite_count": 6464, "favorited": false, "full_text": "20 ChatGPT prompts to finish hours of work in seconds: https://t.co/XoFs2GGboH", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 41, "reply_count": 169, "retweet_count": 1160, "retweeted": false, "user_id_str": "1093089704373903360", "id_str": "1650182557168746496"}, "twe_private_fields": {"created_at": 1682269155000, "updated_at": 1748554197457, "media_count": 1}}}, {"id": "1650386805932564480", "created_at": "2023-04-24 09:30:51 +03:00", "full_text": "Upper, Middle &amp; Lower Four Day Bodybuilding Workout https://t.co/WWQV2HCTHM", "media": [{"type": "video", "url": "https://t.co/WWQV2HCTHM", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1650386780728770560/pu/img/VUj0k4rLJz9FU_qM.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/576x1024/AxxDu3f8OAsUC5ll.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1875, "retweet_count": 355, "bookmark_count": 717, "quote_count": 2, "reply_count": 14, "views_count": 377919, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1650386805932564480", "metadata": {"__typename": "Tweet", "rest_id": "1650386805932564480", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjMyODQzODU2MzYxMjcxMjk3", "rest_id": "1632843856361271297", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1632853784157143042/jDLhLiJo_normal.jpg"}, "core": {"created_at": "Mon Mar 06 20:41:40 +0000 2023", "name": "Fitness Pavilion", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "On a mission to inspire one new person everyday to exercise and eat healthy meals.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "bestlocalgym.com", "expanded_url": "https://bestlocalgym.com", "url": "https://t.co/lHZMDmgAEz", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 430, "followers_count": 10165, "friends_count": 6, "has_custom_timelines": false, "is_translator": false, "listed_count": 93, "media_count": 82, "normal_followers_count": 10165, "pinned_tweet_ids_str": ["1652722031509782528"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1632843856361271297/**********", "profile_interstitial_type": "", "statuses_count": 129, "translator_type": "none", "url": "https://t.co/lHZMDmgAEz", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Lewes Delaware"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1650386805932564480"], "editable_until_msecs": "1682319651000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "377919", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 717, "bookmarked": true, "created_at": "Mon Apr 24 06:30:51 +0000 2023", "conversation_id_str": "1650386805932564480", "display_text_range": [0, 55], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/WWQV2HCTHM", "expanded_url": "https://x.com/fitnesspavill/status/1650386805932564480/video/1", "id_str": "1650386780728770560", "indices": [56, 79], "media_key": "7_1650386780728770560", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1650386780728770560/pu/img/VUj0k4rLJz9FU_qM.jpg", "type": "video", "url": "https://t.co/WWQV2HCTHM", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1024, "w": 576, "resize": "fit"}, "medium": {"h": 1024, "w": 576, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1024, "width": 576, "focus_rects": []}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 11052, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/pl/wt4ACShmPRQs-3ex.m3u8?tag=12&v=bc8"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/320x568/wI3IhkVQgfUDUbaT.mp4?tag=12"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/480x852/8y7JIjC3bf8w5zAM.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/576x1024/AxxDu3f8OAsUC5ll.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1650386780728770560"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/WWQV2HCTHM", "expanded_url": "https://x.com/fitnesspavill/status/1650386805932564480/video/1", "id_str": "1650386780728770560", "indices": [56, 79], "media_key": "7_1650386780728770560", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1650386780728770560/pu/img/VUj0k4rLJz9FU_qM.jpg", "type": "video", "url": "https://t.co/WWQV2HCTHM", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1024, "w": 576, "resize": "fit"}, "medium": {"h": 1024, "w": 576, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1024, "width": 576, "focus_rects": []}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 11052, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/pl/wt4ACShmPRQs-3ex.m3u8?tag=12&v=bc8"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/320x568/wI3IhkVQgfUDUbaT.mp4?tag=12"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/480x852/8y7JIjC3bf8w5zAM.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1650386780728770560/pu/vid/576x1024/AxxDu3f8OAsUC5ll.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1650386780728770560"}}}]}, "favorite_count": 1875, "favorited": false, "full_text": "Upper, Middle &amp; Lower Four Day Bodybuilding Workout https://t.co/WWQV2HCTHM", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 14, "retweet_count": 355, "retweeted": false, "user_id_str": "1632843856361271297", "id_str": "1650386805932564480"}, "twe_private_fields": {"created_at": 1682317851000, "updated_at": 1748554197457, "media_count": 1}}}]