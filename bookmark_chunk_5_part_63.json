[{"id": "1896193546044022822", "created_at": "2025-03-02 16:39:05 +03:00", "full_text": "Absolute Beginner Friendly Programming Notes\n\n    ❯ Python\n    ❯ Java\n    ❯ C/C++, DSA\n    ❯ HTML, CSS, JavaScript\n    ❯ SQL\n    ❯ Linux, Git\n\n40 other subjects - 10000+ Pages (💯 FREE)\n\nNo signup required. Find links inside.", "media": [{"type": "photo", "url": "https://t.co/dXPiukvgYl", "thumbnail": "https://pbs.twimg.com/media/GlCh95RXwAAZHdu?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GlCh95RXwAAZHdu?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1943, "retweet_count": 288, "bookmark_count": 2996, "quote_count": 6, "reply_count": 27, "views_count": 165729, "favorited": true, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1896193546044022822", "metadata": {"__typename": "Tweet", "rest_id": "1896193546044022822", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204228, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204228, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1896193546044022822"], "editable_until_msecs": "1740926345000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "165729", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": false, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4OTYxOTM1NDU5NjAwNDY1OTI=", "text": "Absolute Beginner Friendly Programming Notes\n\n    ❯ Python\n    ❯ Java\n    ❯ C/C++, DSA\n    ❯ HTML, CSS, JavaScript\n    ❯ SQL\n    ❯ Linux, Git\n\n40 other subjects - 10000+ Pages (💯 FREE)\n\nNo signup required. Find links inside.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 0, "to_index": 44, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 2996, "bookmarked": true, "created_at": "Sun Mar 02 13:39:05 +0000 2025", "conversation_id_str": "1896193546044022822", "display_text_range": [0, 224], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/dXPiukvgYl", "expanded_url": "https://x.com/swapnakpanda/status/1896193546044022822/photo/1", "id_str": "1896193527840751616", "indices": [225, 248], "media_key": "3_1896193527840751616", "media_url_https": "https://pbs.twimg.com/media/GlCh95RXwAAZHdu.jpg", "type": "photo", "url": "https://t.co/dXPiukvgYl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1200, "w": 1200, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1200, "width": 1200, "focus_rects": [{"x": 0, "y": 174, "w": 1200, "h": 672}, {"x": 0, "y": 0, "w": 1200, "h": 1200}, {"x": 74, "y": 0, "w": 1053, "h": 1200}, {"x": 300, "y": 0, "w": 600, "h": 1200}, {"x": 0, "y": 0, "w": 1200, "h": 1200}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1896193527840751616"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/dXPiukvgYl", "expanded_url": "https://x.com/swapnakpanda/status/1896193546044022822/photo/1", "id_str": "1896193527840751616", "indices": [225, 248], "media_key": "3_1896193527840751616", "media_url_https": "https://pbs.twimg.com/media/GlCh95RXwAAZHdu.jpg", "type": "photo", "url": "https://t.co/dXPiukvgYl", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1200, "w": 1200, "resize": "fit"}, "medium": {"h": 1200, "w": 1200, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1200, "width": 1200, "focus_rects": [{"x": 0, "y": 174, "w": 1200, "h": 672}, {"x": 0, "y": 0, "w": 1200, "h": 1200}, {"x": 74, "y": 0, "w": 1053, "h": 1200}, {"x": 300, "y": 0, "w": 600, "h": 1200}, {"x": 0, "y": 0, "w": 1200, "h": 1200}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1896193527840751616"}}}]}, "favorite_count": 1943, "favorited": true, "full_text": "Absolute Beginner Friendly Programming Notes\n\n    ❯ Python\n    ❯ Java\n    ❯ C/C++, DSA\n    ❯ HTML, CSS, JavaScript\n    ❯ SQL\n    ❯ Linux, Git\n\n40 other subjects - 10000+ Pages (💯 FREE)\n\nNo signup required. Find links inside. https://t.co/dXPiukvgYl", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 27, "retweet_count": 288, "retweeted": false, "user_id_str": "69941978", "id_str": "1896193546044022822"}, "twe_private_fields": {"created_at": 1740922745000, "updated_at": 1748554096073, "media_count": 1}}}, {"id": "1896209444675154147", "created_at": "2025-03-02 17:42:16 +03:00", "full_text": "𝑴𝒂𝒔𝒕𝒆𝒓 𝑹𝑬𝑺𝑻 𝑨𝑷𝑰 𝑫𝒆𝒔𝒊𝒈𝒏 𝒊𝒏 𝑴𝒊𝒏𝒖𝒕𝒆𝒔! 🚀\n\nStruggling with REST API design? This cheat sheet breaks down everything - best practices, URL rules, status codes, security, and more - in one place. Save time, build better APIs! 🚀👇 https://t.co/ExVui9BOIt", "media": [{"type": "photo", "url": "https://t.co/ExVui9BOIt", "thumbnail": "https://pbs.twimg.com/media/GlCve7ZW4AAjlR0?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GlCve7ZW4AAjlR0?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 492, "retweet_count": 83, "bookmark_count": 660, "quote_count": 3, "reply_count": 7, "views_count": 45300, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1896209444675154147", "metadata": {"__typename": "Tweet", "rest_id": "1896209444675154147", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjc3MjY2MzU5NjM2NDM5MDQw", "rest_id": "1677266359636439040", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1730570141111398400/cFQQow9q_normal.jpg"}, "core": {"created_at": "Fri Jul 07 10:41:01 +0000 2023", "name": "Tech Fusionist", "screen_name": "techyoutbe"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Bridging Tech Gaps → AI | Cloud | No-Code | DevOps | Security\n🚀 Sharing insights, tools & trends\n🤝 DM for collabs or partnerships", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/techfusionist", "expanded_url": "https://linktr.ee/techfusionist", "url": "https://t.co/tlfAR0BngK", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 23593, "followers_count": 42121, "friends_count": 1582, "has_custom_timelines": false, "is_translator": false, "listed_count": 232, "media_count": 5220, "normal_followers_count": 42121, "pinned_tweet_ids_str": ["1927406718020931833"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1677266359636439040/1733548643", "profile_interstitial_type": "", "statuses_count": 29076, "translator_type": "none", "url": "https://t.co/tlfAR0BngK", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Tech Universe"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1896209444675154147"], "editable_until_msecs": "1740930136000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "45300", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 660, "bookmarked": true, "created_at": "Sun Mar 02 14:42:16 +0000 2025", "conversation_id_str": "1896209444675154147", "display_text_range": [0, 221], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/ExVui9BOIt", "expanded_url": "https://x.com/techyoutbe/status/1896209444675154147/photo/1", "id_str": "1896208388997963776", "indices": [222, 245], "media_key": "3_1896208388997963776", "media_url_https": "https://pbs.twimg.com/media/GlCve7ZW4AAjlR0.jpg", "type": "photo", "url": "https://t.co/ExVui9BOIt", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1152, "w": 2048, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1350, "width": 2400, "focus_rects": [{"x": 0, "y": 0, "w": 2400, "h": 1344}, {"x": 0, "y": 0, "w": 1350, "h": 1350}, {"x": 68, "y": 0, "w": 1184, "h": 1350}, {"x": 323, "y": 0, "w": 675, "h": 1350}, {"x": 0, "y": 0, "w": 2400, "h": 1350}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1896208388997963776"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/ExVui9BOIt", "expanded_url": "https://x.com/techyoutbe/status/1896209444675154147/photo/1", "id_str": "1896208388997963776", "indices": [222, 245], "media_key": "3_1896208388997963776", "media_url_https": "https://pbs.twimg.com/media/GlCve7ZW4AAjlR0.jpg", "type": "photo", "url": "https://t.co/ExVui9BOIt", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1152, "w": 2048, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1350, "width": 2400, "focus_rects": [{"x": 0, "y": 0, "w": 2400, "h": 1344}, {"x": 0, "y": 0, "w": 1350, "h": 1350}, {"x": 68, "y": 0, "w": 1184, "h": 1350}, {"x": 323, "y": 0, "w": 675, "h": 1350}, {"x": 0, "y": 0, "w": 2400, "h": 1350}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1896208388997963776"}}}]}, "favorite_count": 492, "favorited": false, "full_text": "𝑴𝒂𝒔𝒕𝒆𝒓 𝑹𝑬𝑺𝑻 𝑨𝑷𝑰 𝑫𝒆𝒔𝒊𝒈𝒏 𝒊𝒏 𝑴𝒊𝒏𝒖𝒕𝒆𝒔! 🚀\n\nStruggling with REST API design? This cheat sheet breaks down everything - best practices, URL rules, status codes, security, and more - in one place. Save time, build better APIs! 🚀👇 https://t.co/ExVui9BOIt", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 3, "reply_count": 7, "retweet_count": 83, "retweeted": false, "user_id_str": "1677266359636439040", "id_str": "1896209444675154147"}, "twe_private_fields": {"created_at": 1740926536000, "updated_at": 1748554096073, "media_count": 1}}}]