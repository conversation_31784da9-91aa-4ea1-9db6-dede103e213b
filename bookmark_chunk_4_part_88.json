[{"id": "1840364328978895114", "created_at": "2024-09-29 15:13:43 +03:00", "full_text": "The math behind the Quant Scientist Stack:\n\n• SciPy: $0\n• Zipline: $0\n• Python: $0\n• NumPy: $0\n• PyFolio: $0\n• pandas : $0\n• OpenBB: $0\n• Empyrical: $0\n• AlphaLens: $0\n• Statsmodels: $0\n• RiskFolio-Lib: $0\n\nYou can start algorithmic trading for free. https://t.co/CjJ6fv0ZcG", "media": [{"type": "photo", "url": "https://t.co/CjJ6fv0ZcG", "thumbnail": "https://pbs.twimg.com/media/GYpJmk7WMAAl9_s?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GYpJmk7WMAAl9_s?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 831, "retweet_count": 108, "bookmark_count": 908, "quote_count": 5, "reply_count": 6, "views_count": 12, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1840364328978895114", "metadata": {"__typename": "Tweet", "rest_id": "1840364328978895114", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjgzNTI2OTkzMDU5NDMwNDEx", "rest_id": "1683526993059430411", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1683527188782432269/rne8h7-S_normal.jpg"}, "core": {"created_at": "Mon Jul 24 17:18:21 +0000 2023", "name": "Quant Science", "screen_name": "quantscience_"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Develop profitable trading strategies, build a systematic trading process, and trade your ideas with Python—even if you’ve never done it before.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "quantscience.io", "expanded_url": "https://quantscience.io/", "url": "https://t.co/isixlTdGob", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 843, "followers_count": 53125, "friends_count": 13, "has_custom_timelines": false, "is_translator": false, "listed_count": 532, "media_count": 2748, "normal_followers_count": 53125, "pinned_tweet_ids_str": ["1691515116477419520"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1683526993059430411/1690219580", "profile_interstitial_type": "", "statuses_count": 5312, "translator_type": "none", "url": "https://t.co/isixlTdGob", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Free Python newsletter 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1781690782002360617", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1840364328978895114"], "editable_until_msecs": "1727615623000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "12", "state": "EnabledWithCount"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 908, "bookmarked": true, "created_at": "Sun Sep 29 12:13:43 +0000 2024", "conversation_id_str": "1840364328978895114", "display_text_range": [0, 250], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/CjJ6fv0ZcG", "expanded_url": "https://x.com/quantscience_/status/1840364328978895114/photo/1", "id_str": "1840364324830720000", "indices": [251, 274], "media_key": "3_1840364324830720000", "media_url_https": "https://pbs.twimg.com/media/GYpJmk7WMAAl9_s.png", "type": "photo", "url": "https://t.co/CjJ6fv0ZcG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 383, "w": 680, "resize": "fit"}, "medium": {"h": 383, "w": 680, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 383, "width": 680, "focus_rects": [{"x": 0, "y": 0, "w": 680, "h": 381}, {"x": 234, "y": 0, "w": 383, "h": 383}, {"x": 257, "y": 0, "w": 336, "h": 383}, {"x": 329, "y": 0, "w": 192, "h": 383}, {"x": 0, "y": 0, "w": 680, "h": 383}]}, "media_results": {"result": {"media_key": "3_1840364324830720000"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/CjJ6fv0ZcG", "expanded_url": "https://x.com/quantscience_/status/1840364328978895114/photo/1", "id_str": "1840364324830720000", "indices": [251, 274], "media_key": "3_1840364324830720000", "media_url_https": "https://pbs.twimg.com/media/GYpJmk7WMAAl9_s.png", "type": "photo", "url": "https://t.co/CjJ6fv0ZcG", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 383, "w": 680, "resize": "fit"}, "medium": {"h": 383, "w": 680, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 383, "width": 680, "focus_rects": [{"x": 0, "y": 0, "w": 680, "h": 381}, {"x": 234, "y": 0, "w": 383, "h": 383}, {"x": 257, "y": 0, "w": 336, "h": 383}, {"x": 329, "y": 0, "w": 192, "h": 383}, {"x": 0, "y": 0, "w": 680, "h": 383}]}, "media_results": {"result": {"media_key": "3_1840364324830720000"}}}]}, "favorite_count": 831, "favorited": false, "full_text": "The math behind the Quant Scientist Stack:\n\n• SciPy: $0\n• Zipline: $0\n• Python: $0\n• NumPy: $0\n• PyFolio: $0\n• pandas : $0\n• OpenBB: $0\n• Empyrical: $0\n• AlphaLens: $0\n• Statsmodels: $0\n• RiskFolio-Lib: $0\n\nYou can start algorithmic trading for free. https://t.co/CjJ6fv0ZcG", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 5, "reply_count": 6, "retweet_count": 108, "retweeted": false, "user_id_str": "1683526993059430411", "id_str": "1840364328978895114"}, "twe_private_fields": {"created_at": 1727612023000, "updated_at": 1748554144882, "media_count": 1}}}, {"id": "1840420451358507089", "created_at": "2024-09-29 18:56:43 +03:00", "full_text": "OpenBB: A free alternative to the $20,000 Bloomberg Terminal\n\nAvailable 100% free on GitHub: https://t.co/rVf9riF9Tv", "media": [{"type": "photo", "url": "https://t.co/rVf9riF9Tv", "thumbnail": "https://pbs.twimg.com/media/GYp8pYMWgAAHUIa?format=png&name=thumb", "original": "https://pbs.twimg.com/media/GYp8pYMWgAAHUIa?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2772, "retweet_count": 319, "bookmark_count": 3559, "quote_count": 12, "reply_count": 26, "views_count": 246756, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1840420451358507089", "metadata": {"__typename": "Tweet", "rest_id": "1840420451358507089", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjgzNTI2OTkzMDU5NDMwNDEx", "rest_id": "1683526993059430411", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1683527188782432269/rne8h7-S_normal.jpg"}, "core": {"created_at": "Mon Jul 24 17:18:21 +0000 2023", "name": "Quant Science", "screen_name": "quantscience_"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Develop profitable trading strategies, build a systematic trading process, and trade your ideas with Python—even if you’ve never done it before.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "quantscience.io", "expanded_url": "https://quantscience.io/", "url": "https://t.co/isixlTdGob", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 843, "followers_count": 53125, "friends_count": 13, "has_custom_timelines": false, "is_translator": false, "listed_count": 532, "media_count": 2748, "normal_followers_count": 53125, "pinned_tweet_ids_str": ["1691515116477419520"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1683526993059430411/1690219580", "profile_interstitial_type": "", "statuses_count": 5312, "translator_type": "none", "url": "https://t.co/isixlTdGob", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Free Python newsletter 👉"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1781690782002360617", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1840420451358507089"], "editable_until_msecs": "1727629003000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "246756", "state": "EnabledWithCount"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3559, "bookmarked": true, "created_at": "Sun Sep 29 15:56:43 +0000 2024", "conversation_id_str": "1840420451358507089", "display_text_range": [0, 92], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/rVf9riF9Tv", "expanded_url": "https://x.com/quantscience_/status/1840420451358507089/photo/1", "id_str": "1840420448045006848", "indices": [93, 116], "media_key": "3_1840420448045006848", "media_url_https": "https://pbs.twimg.com/media/GYp8pYMWgAAHUIa.png", "type": "photo", "url": "https://t.co/rVf9riF9Tv", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 440, "w": 680, "resize": "fit"}, "medium": {"h": 440, "w": 680, "resize": "fit"}, "small": {"h": 440, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 440, "width": 680, "focus_rects": [{"x": 0, "y": 31, "w": 680, "h": 381}, {"x": 171, "y": 0, "w": 440, "h": 440}, {"x": 198, "y": 0, "w": 386, "h": 440}, {"x": 281, "y": 0, "w": 220, "h": 440}, {"x": 0, "y": 0, "w": 680, "h": 440}]}, "media_results": {"result": {"media_key": "3_1840420448045006848"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/rVf9riF9Tv", "expanded_url": "https://x.com/quantscience_/status/1840420451358507089/photo/1", "id_str": "1840420448045006848", "indices": [93, 116], "media_key": "3_1840420448045006848", "media_url_https": "https://pbs.twimg.com/media/GYp8pYMWgAAHUIa.png", "type": "photo", "url": "https://t.co/rVf9riF9Tv", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 440, "w": 680, "resize": "fit"}, "medium": {"h": 440, "w": 680, "resize": "fit"}, "small": {"h": 440, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 440, "width": 680, "focus_rects": [{"x": 0, "y": 31, "w": 680, "h": 381}, {"x": 171, "y": 0, "w": 440, "h": 440}, {"x": 198, "y": 0, "w": 386, "h": 440}, {"x": 281, "y": 0, "w": 220, "h": 440}, {"x": 0, "y": 0, "w": 680, "h": 440}]}, "media_results": {"result": {"media_key": "3_1840420448045006848"}}}]}, "favorite_count": 2772, "favorited": false, "full_text": "OpenBB: A free alternative to the $20,000 Bloomberg Terminal\n\nAvailable 100% free on GitHub: https://t.co/rVf9riF9Tv", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 12, "reply_count": 26, "retweet_count": 319, "retweeted": false, "user_id_str": "1683526993059430411", "id_str": "1840420451358507089"}, "twe_private_fields": {"created_at": 1727625403000, "updated_at": 1748554144882, "media_count": 1}}}]