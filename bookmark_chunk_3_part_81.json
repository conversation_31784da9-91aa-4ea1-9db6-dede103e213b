[{"id": "1591561842086023168", "created_at": "2022-11-13 01:41:27 +03:00", "full_text": "I've created a google sheet which has all the free streams of *entire leagues* that I know about. Free &amp; legal\n\nIt's open to edit. If you know of any leagues which have free official streams please add them! Add leagues with every match available on replay\nhttps://t.co/g2XmW2G7EO", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 403, "retweet_count": 48, "bookmark_count": 428, "quote_count": 1, "reply_count": 10, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1591561842086023168", "metadata": {"__typename": "Tweet", "rest_id": "1591561842086023168", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNjMzNDYxMDI=", "rest_id": "263346102", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1913158830914940930/Pn0MQ0TE_normal.jpg"}, "core": {"created_at": "Wed Mar 09 21:37:16 +0000 2011", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Recruitment & Match Analyst for Kolding IF 🇩🇰 ex Loudoun United 🇺🇸 | Views my own | https://t.co/XssD42slXC | https://t.co/xAWVQwKGBB", "entities": {"description": {"urls": [{"display_url": "best11scouting.streamlit.app", "expanded_url": "http://best11scouting.streamlit.app", "url": "https://t.co/XssD42slXC", "indices": [84, 107]}, {"display_url": "football-match-reports.streamlit.app", "expanded_url": "http://football-match-reports.streamlit.app", "url": "https://t.co/xAWVQwKGBB", "indices": [110, 133]}]}, "url": {"urls": [{"display_url": "BenGriffis.com", "expanded_url": "https://BenGriffis.com", "url": "https://t.co/R0HGblE25g", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 46259, "followers_count": 29975, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 292, "media_count": 11752, "normal_followers_count": 29975, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/263346102/1707620789", "profile_interstitial_type": "", "statuses_count": 45025, "translator_type": "none", "url": "https://t.co/R0HGblE25g", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Kolding, Danmark"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1589597178531659781", "professional_type": "Creator", "category": [{"id": 1050, "name": "Statistician", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/g2XmW2G7EO", "legacy": {"binding_values": [{"key": "thumbnail_image", "value": {"image_value": {"height": 144, "width": 144, "url": "https://pbs.twimg.com/card_img/1927784015576051712/dVdYWd7R?format=jpg&name=144x144_2"}, "type": "IMAGE"}}, {"key": "domain", "value": {"string_value": "docs.google.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 420, "width": 420, "url": "https://pbs.twimg.com/card_img/1927784015576051712/dVdYWd7R?format=jpg&name=420x420_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 630, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927784015576051712/dVdYWd7R?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 100, "width": 100, "url": "https://pbs.twimg.com/card_img/1927784015576051712/dVdYWd7R?format=jpg&name=100x100_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 630, "width": 1200, "url": "https://pbs.twimg.com/card_img/1927784015576051712/dVdYWd7R?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "docs.google.com", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 251, "green": 251, "red": 251}, "percentage": 99.94}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "Free streaming for ALL GAMES in football leagues", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/g2XmW2G7EO", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary", "url": "https://t.co/g2XmW2G7EO", "user_refs_results": []}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1591561842086023168"], "editable_until_msecs": "1668294687000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 428, "bookmarked": true, "created_at": "Sat Nov 12 22:41:27 +0000 2022", "conversation_id_str": "1591561842086023168", "display_text_range": [0, 284], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "docs.google.com/spreadsheets/d…", "expanded_url": "https://docs.google.com/spreadsheets/d/18QxXLSGo_gqU0lKApfkQOkW9Z-O0sj0AfFNECAbCUOk/edit?usp=sharing", "url": "https://t.co/g2XmW2G7EO", "indices": [261, 284]}], "user_mentions": []}, "favorite_count": 403, "favorited": false, "full_text": "I've created a google sheet which has all the free streams of *entire leagues* that I know about. Free &amp; legal\n\nIt's open to edit. If you know of any leagues which have free official streams please add them! Add leagues with every match available on replay\nhttps://t.co/g2XmW2G7EO", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 10, "retweet_count": 48, "retweeted": false, "user_id_str": "263346102", "id_str": "1591561842086023168"}, "twe_private_fields": {"created_at": 1668292887000, "updated_at": 1748554229950, "media_count": 0}}}, {"id": "1592128371378556928", "created_at": "2022-11-14 15:12:38 +03:00", "full_text": "Study Python Programming and Computer Science at @MIT for FREE on Youtube 🥸\n\nThey offer an applied &amp; beginner-friendly introduction to common computer science concepts &amp; techniques in Python\n\nPlaylist: https://t.co/wJdV6KHSot\n\nSyllabus, slides, codes: https://t.co/ubcnpVVHlc https://t.co/Vektg4KzAf", "media": [{"type": "photo", "url": "https://t.co/Vektg4KzAf", "thumbnail": "https://pbs.twimg.com/media/FhhePekWQAAunsP?format=png&name=thumb", "original": "https://pbs.twimg.com/media/FhhePekWQAAunsP?format=png&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2033, "retweet_count": 633, "bookmark_count": 1760, "quote_count": 12, "reply_count": 19, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1592128371378556928", "metadata": {"__typename": "Tweet", "rest_id": "1592128371378556928", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDA2NjM5Mzc2Njc1NzcwMzc0", "rest_id": "1406639376675770374", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1569690950447910914/5dUfOdEi_normal.jpg"}, "core": {"created_at": "Sun Jun 20 15:46:18 +0000 2021", "name": "<PERSON> | Machine Learning Engineer", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "End-to-End ML Engineer. Building the best AI learning resource at https://t.co/lC2UKMtRjj. Youtube: https://t.co/pjpX8NvUn5", "entities": {"description": {"urls": [{"display_url": "ailearninghub.io", "expanded_url": "http://ailearninghub.io", "url": "https://t.co/lC2UKMtRjj", "indices": [66, 89]}, {"display_url": "youtube.com/@dankornas", "expanded_url": "http://youtube.com/@dankornas", "url": "https://t.co/pjpX8NvUn5", "indices": [100, 123]}]}, "url": {"urls": [{"display_url": "forms.gle/KqvbKr54iVyNXY…", "expanded_url": "https://forms.gle/KqvbKr54iVyNXYHu6", "url": "https://t.co/qZMic1IkaJ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 9506, "followers_count": 76688, "friends_count": 491, "has_custom_timelines": true, "is_translator": false, "listed_count": 653, "media_count": 2142, "normal_followers_count": 76688, "pinned_tweet_ids_str": ["1907382443910238228"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1406639376675770374/1693375992", "profile_interstitial_type": "", "statuses_count": 8377, "translator_type": "none", "url": "https://t.co/qZMic1IkaJ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Collaborations →"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1471440934009290755", "professional_type": "Creator", "category": [{"id": 144, "name": "Education", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1592128371378556928"], "editable_until_msecs": "1668429758000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 1760, "bookmarked": true, "created_at": "Mon Nov 14 12:12:38 +0000 2022", "conversation_id_str": "1592128371378556928", "display_text_range": [0, 283], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/Vektg4KzAf", "expanded_url": "https://x.com/DanKornas/status/1592128371378556928/photo/1", "id_str": "1592126088272035840", "indices": [284, 307], "media_key": "3_1592126088272035840", "media_url_https": "https://pbs.twimg.com/media/FhhePekWQAAunsP.png", "type": "photo", "url": "https://t.co/Vektg4KzAf", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 183, "y": 74, "h": 39, "w": 39}]}, "medium": {"faces": [{"x": 183, "y": 74, "h": 39, "w": 39}]}, "small": {"faces": [{"x": 157, "y": 63, "h": 33, "w": 33}]}, "orig": {"faces": [{"x": 183, "y": 74, "h": 39, "w": 39}]}}, "sizes": {"large": {"h": 791, "w": 788, "resize": "fit"}, "medium": {"h": 791, "w": 788, "resize": "fit"}, "small": {"h": 680, "w": 677, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 791, "width": 788, "focus_rects": [{"x": 0, "y": 76, "w": 788, "h": 441}, {"x": 0, "y": 0, "w": 788, "h": 788}, {"x": 0, "y": 0, "w": 694, "h": 791}, {"x": 0, "y": 0, "w": 396, "h": 791}, {"x": 0, "y": 0, "w": 788, "h": 791}]}, "media_results": {"result": {"media_key": "3_1592126088272035840"}}}], "symbols": [], "timestamps": [], "urls": [{"display_url": "youtube.com/playlist?list=…", "expanded_url": "https://www.youtube.com/playlist?list=PLUl4u3cNGP63WbdFxL8giv4yhgdMGaZNA", "url": "https://t.co/wJdV6KHSot", "indices": [210, 233]}, {"display_url": "ocw.mit.edu/courses/6-0001…", "expanded_url": "https://ocw.mit.edu/courses/6-0001-introduction-to-computer-science-and-programming-in-python-fall-2016/", "url": "https://t.co/ubcnpVVHlc", "indices": [260, 283]}], "user_mentions": [{"id_str": "15460048", "name": "Massachusetts Institute of Technology (MIT)", "screen_name": "MIT", "indices": [49, 53]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/Vektg4KzAf", "expanded_url": "https://x.com/DanKornas/status/1592128371378556928/photo/1", "id_str": "1592126088272035840", "indices": [284, 307], "media_key": "3_1592126088272035840", "media_url_https": "https://pbs.twimg.com/media/FhhePekWQAAunsP.png", "type": "photo", "url": "https://t.co/Vektg4KzAf", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 183, "y": 74, "h": 39, "w": 39}]}, "medium": {"faces": [{"x": 183, "y": 74, "h": 39, "w": 39}]}, "small": {"faces": [{"x": 157, "y": 63, "h": 33, "w": 33}]}, "orig": {"faces": [{"x": 183, "y": 74, "h": 39, "w": 39}]}}, "sizes": {"large": {"h": 791, "w": 788, "resize": "fit"}, "medium": {"h": 791, "w": 788, "resize": "fit"}, "small": {"h": 680, "w": 677, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 791, "width": 788, "focus_rects": [{"x": 0, "y": 76, "w": 788, "h": 441}, {"x": 0, "y": 0, "w": 788, "h": 788}, {"x": 0, "y": 0, "w": 694, "h": 791}, {"x": 0, "y": 0, "w": 396, "h": 791}, {"x": 0, "y": 0, "w": 788, "h": 791}]}, "media_results": {"result": {"media_key": "3_1592126088272035840"}}}]}, "favorite_count": 2033, "favorited": false, "full_text": "Study Python Programming and Computer Science at @MIT for FREE on Youtube 🥸\n\nThey offer an applied &amp; beginner-friendly introduction to common computer science concepts &amp; techniques in Python\n\nPlaylist: https://t.co/wJdV6KHSot\n\nSyllabus, slides, codes: https://t.co/ubcnpVVHlc https://t.co/Vektg4KzAf", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 12, "reply_count": 19, "retweet_count": 633, "retweeted": false, "user_id_str": "1406639376675770374", "id_str": "1592128371378556928"}, "twe_private_fields": {"created_at": 1668427958000, "updated_at": 1748554229950, "media_count": 1}}}]