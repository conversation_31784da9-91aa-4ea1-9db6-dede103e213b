[{"id": "1609174775578124290", "created_at": "2022-12-31 16:08:58 +03:00", "full_text": "This week I built an easy-to-use \"One Page Budget\" planner in Google Sheets.\n\nHere's a breakdown of how it works and a template you can use: 📊", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2327, "retweet_count": 419, "bookmark_count": 2805, "quote_count": 17, "reply_count": 66, "views_count": 1210323, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1609174775578124290", "metadata": {"__typename": "Tweet", "rest_id": "1609174775578124290", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MTUzNzgyMzQ5OTc2MTY2NDA=", "rest_id": "715378234997616640", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1613615173507420199/UuYkLgPK_normal.jpg"}, "core": {"created_at": "Thu Mar 31 03:20:37 +0000 2016", "name": "<PERSON>", "screen_name": "blake<PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Fan of Bourbon, Books, and Good Advice 🥃 📚 💬", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 30503, "followers_count": 405534, "friends_count": 178, "has_custom_timelines": true, "is_translator": false, "listed_count": 9166, "media_count": 1103, "normal_followers_count": 405534, "pinned_tweet_ids_str": ["1795103776459907270"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/715378234997616640/1693065189", "profile_interstitial_type": "", "statuses_count": 15328, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Oklahoma City, OK"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1557501059995766786", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "<PERSON><PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1609174775578124290"], "editable_until_msecs": "1672493938000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "1210323", "state": "EnabledWithCount"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2805, "bookmarked": true, "created_at": "Sat Dec 31 13:08:58 +0000 2022", "conversation_id_str": "1609174775578124290", "display_text_range": [0, 142], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2327, "favorited": false, "full_text": "This week I built an easy-to-use \"One Page Budget\" planner in Google Sheets.\n\nHere's a breakdown of how it works and a template you can use: 📊", "is_quote_status": false, "lang": "en", "quote_count": 17, "reply_count": 66, "retweet_count": 419, "retweeted": false, "user_id_str": "715378234997616640", "id_str": "1609174775578124290"}, "twe_private_fields": {"created_at": 1672492138000, "updated_at": 1748554204513, "media_count": 0}}}, {"id": "1610524793580978176", "created_at": "2023-01-04 09:33:27 +03:00", "full_text": "How to Automate Excel with ChatGPT and Python\n#python #chatgpt \nhttps://t.co/hGpcPY2WRl", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1013, "retweet_count": 311, "bookmark_count": 359, "quote_count": 4, "reply_count": 16, "views_count": 108514, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1610524793580978176", "metadata": {"__typename": "Tweet", "rest_id": "1610524793580978176", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTUzODQ2Mjc5NzU4MzE1NTM=", "rest_id": "855384627975831553", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/855386134439895041/BKOMdcnN_normal.jpg"}, "core": {"created_at": "Fri Apr 21 11:36:02 +0000 2017", "name": "Python Programming", "screen_name": "PythonPr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "#python #programming", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "morioh.com", "expanded_url": "https://morioh.com", "url": "https://t.co/6eeX8MWjRB", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6945, "followers_count": 135341, "friends_count": 1177, "has_custom_timelines": false, "is_translator": false, "listed_count": 1052, "media_count": 1624, "normal_followers_count": 135341, "pinned_tweet_ids_str": ["1859613267775385673"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/855384627975831553/1492774829", "profile_interstitial_type": "", "statuses_count": 7123, "translator_type": "none", "url": "https://t.co/6eeX8MWjRB", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "United States"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1610524793580978176"], "editable_until_msecs": "1672815807000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "108514", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 359, "bookmarked": true, "created_at": "Wed Jan 04 06:33:27 +0000 2023", "conversation_id_str": "1610524793580978176", "display_text_range": [0, 87], "entities": {"hashtags": [{"indices": [46, 53], "text": "python"}, {"indices": [54, 62], "text": "chatgpt"}], "symbols": [], "timestamps": [], "urls": [{"display_url": "morioh.com/p/6563f2e8e7ae…", "expanded_url": "https://morioh.com/p/6563f2e8e7ae?f=5e44c59998b8037d03aa8178", "url": "https://t.co/hGpcPY2WRl", "indices": [64, 87]}], "user_mentions": []}, "favorite_count": 1013, "favorited": false, "full_text": "How to Automate Excel with ChatGPT and Python\n#python #chatgpt \nhttps://t.co/hGpcPY2WRl", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 4, "reply_count": 16, "retweet_count": 311, "retweeted": false, "user_id_str": "855384627975831553", "id_str": "1610524793580978176"}, "twe_private_fields": {"created_at": 1672814007000, "updated_at": 1748554204513, "media_count": 0}}}]