# Bookmarks from bookmark_chunk_2_part_16.json

## Category: Sports/Football/Player Stories/MUFC/<PERSON><PERSON><PERSON><PERSON>

*   **Author:** @awbissaka
    **Date:** 2021-07-16 17:25:41 +03:00
    **Content:** My journey so far.. Thank you @TPTFootball @PlayersTribune 

    https://t.co/V2ervRrhJ2

    https://t.co/EWy7gjnlfm https://t.co/VNizbFzBuf
    **URL:** [https://twitter.com/undefined/status/1416041352396255233](https://twitter.com/undefined/status/1416041352396255233)
    **Note:** <PERSON> shares links to a YouTube video and a Players' Tribune article about his football journey. Includes a promotional image.

---

## Category: Technology/Data Visualization/Tools/Football Analytics/Streamlit

*   **Author:** @slothfulwave612
    **Date:** 2021-07-17 21:08:21 +03:00
    **Content:** Do you want to make nightingale/pizza charts but don't know how to code. I have created a web app that plots a basic version of the nightingale chart. The web app is made using streamlit and uses mplsoccer for making the chart.

    https://t.co/0nAWNQoB5e https://t.co/4N8BwOEUTI
    **URL:** [https://twitter.com/undefined/status/1416459775563292674](https://twitter.com/undefined/status/1416459775563292674)
    **Note:** Announcement of a Streamlit web app for creating nightingale/pizza charts for football analytics using mplsoccer, targeted at non-coders. Includes a link to the app and a sample chart image.

---