[{"id": "1875218471396794557", "created_at": "2025-01-03 19:31:38 +03:00", "full_text": "When it comes to JavaScript, Node, React, and Next.js,\n\nThis is the best channel. https://t.co/2kAMJNyqbz", "media": [{"type": "photo", "url": "https://t.co/2kAMJNyqbz", "thumbnail": "https://pbs.twimg.com/media/GgYdQQiX0AIiUPk?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/GgYdQQiX0AIiUPk?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3099, "retweet_count": 302, "bookmark_count": 3276, "quote_count": 7, "reply_count": 42, "views_count": 155367, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1875218471396794557", "metadata": {"__typename": "Tweet", "rest_id": "1875218471396794557", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204228, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204228, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1875218471396794557"], "editable_until_msecs": "1735925498000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "155367", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3276, "bookmarked": true, "created_at": "Fri Jan 03 16:31:38 +0000 2025", "conversation_id_str": "1875218471396794557", "display_text_range": [0, 81], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/2kAMJNyqbz", "expanded_url": "https://x.com/swapnakpanda/status/1875218471396794557/photo/1", "id_str": "1875218460000636930", "indices": [82, 105], "media_key": "3_1875218460000636930", "media_url_https": "https://pbs.twimg.com/media/GgYdQQiX0AIiUPk.jpg", "type": "photo", "url": "https://t.co/2kAMJNyqbz", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 384, "y": 161, "h": 100, "w": 100}]}, "medium": {"faces": [{"x": 245, "y": 103, "h": 64, "w": 64}]}, "small": {"faces": [{"x": 139, "y": 58, "h": 36, "w": 36}]}, "orig": {"faces": [{"x": 384, "y": 161, "h": 100, "w": 100}]}}, "sizes": {"large": {"h": 1304, "w": 1874, "resize": "fit"}, "medium": {"h": 835, "w": 1200, "resize": "fit"}, "small": {"h": 473, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1304, "width": 1874, "focus_rects": [{"x": 0, "y": 0, "w": 1874, "h": 1049}, {"x": 330, "y": 0, "w": 1304, "h": 1304}, {"x": 410, "y": 0, "w": 1144, "h": 1304}, {"x": 656, "y": 0, "w": 652, "h": 1304}, {"x": 0, "y": 0, "w": 1874, "h": 1304}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1875218460000636930"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/2kAMJNyqbz", "expanded_url": "https://x.com/swapnakpanda/status/1875218471396794557/photo/1", "id_str": "1875218460000636930", "indices": [82, 105], "media_key": "3_1875218460000636930", "media_url_https": "https://pbs.twimg.com/media/GgYdQQiX0AIiUPk.jpg", "type": "photo", "url": "https://t.co/2kAMJNyqbz", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 384, "y": 161, "h": 100, "w": 100}]}, "medium": {"faces": [{"x": 245, "y": 103, "h": 64, "w": 64}]}, "small": {"faces": [{"x": 139, "y": 58, "h": 36, "w": 36}]}, "orig": {"faces": [{"x": 384, "y": 161, "h": 100, "w": 100}]}}, "sizes": {"large": {"h": 1304, "w": 1874, "resize": "fit"}, "medium": {"h": 835, "w": 1200, "resize": "fit"}, "small": {"h": 473, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1304, "width": 1874, "focus_rects": [{"x": 0, "y": 0, "w": 1874, "h": 1049}, {"x": 330, "y": 0, "w": 1304, "h": 1304}, {"x": 410, "y": 0, "w": 1144, "h": 1304}, {"x": 656, "y": 0, "w": 652, "h": 1304}, {"x": 0, "y": 0, "w": 1874, "h": 1304}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1875218460000636930"}}}]}, "favorite_count": 3099, "favorited": false, "full_text": "When it comes to JavaScript, Node, React, and Next.js,\n\nThis is the best channel. https://t.co/2kAMJNyqbz", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 7, "reply_count": 42, "retweet_count": 302, "retweeted": false, "user_id_str": "69941978", "id_str": "1875218471396794557"}, "twe_private_fields": {"created_at": 1735921898000, "updated_at": 1748554120680, "media_count": 1}}}, {"id": "1875639011454361860", "created_at": "2025-01-04 23:22:42 +03:00", "full_text": "Introducing Prism UI ✨\n\nA UI library built on top of @shadcn components to create beautiful pages 🪄\n\nAnimated components that are build on @shadcn, @tailwindcss and framer motion.\n\n100% Free and Open Source\n\nI have drawn inspiration from two main sources \n\n◆ @shadcn - The foundation and the 🐐\n◆ @steventey - The clean, modern UI/UX seen in dub has been a huge inspiration\n\nTech Stack:\n@vercel\n@shadcn \n@framer \n@tailwindcss \n@v0\n@mattgperry (framer motion)\n\nThis was my holiday project - Hope you like it 🖤\n\n#opensource #buildinpublic", "media": [{"type": "video", "url": "https://t.co/0ctUEzOocA", "thumbnail": "https://pbs.twimg.com/ext_tw_video_thumb/1875634239930052608/pu/img/YDXwbD0kCmmVMs6q.jpg?name=thumb", "original": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/1016x720/mQWQXY-FE7shMTPM.mp4?tag=12"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2538, "retweet_count": 203, "bookmark_count": 3373, "quote_count": 16, "reply_count": 74, "views_count": 234597, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1875639011454361860", "metadata": {"__typename": "Tweet", "rest_id": "1875639011454361860", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNjU5NDMwMDA2MDM0OTcyNjc5", "rest_id": "1659430006034972679", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1706595242009387008/_mNR89Xa_normal.jpg"}, "core": {"created_at": "Fri May 19 05:25:41 +0000 2023", "name": "CodeHagen", "screen_name": "CodeHagen"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Serial Entrepreneur ✨\n\nOpen-source:\n🪄 https://t.co/LhTeGmwdbH - 🔗 https://t.co/GNSffXUbyE\n✨ https://t.co/wGVemrpCHn - 🔗 https://t.co/T3RYiDoyTf \n\nMove fast  🔥", "entities": {"description": {"urls": [{"display_url": "Prismui.tech", "expanded_url": "https://Prismui.tech", "url": "https://t.co/LhTeGmwdbH", "indices": [38, 61]}, {"display_url": "git.new/prismui", "expanded_url": "https://git.new/prismui", "url": "https://t.co/GNSffXUbyE", "indices": [66, 89]}, {"display_url": "Badget.tech", "expanded_url": "https://Badget.tech", "url": "https://t.co/wGVemrpCHn", "indices": [92, 115]}, {"display_url": "git.new/badget", "expanded_url": "http://git.new/badget", "url": "https://t.co/T3RYiDoyTf", "indices": [120, 143]}]}, "url": {"urls": [{"display_url": "prismui.tech", "expanded_url": "https://prismui.tech/", "url": "https://t.co/Kg30ubcrTZ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2908, "followers_count": 8640, "friends_count": 289, "has_custom_timelines": false, "is_translator": false, "listed_count": 91, "media_count": 235, "normal_followers_count": 8640, "pinned_tweet_ids_str": ["1875639011454361860"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1659430006034972679/1684476776", "profile_interstitial_type": "", "statuses_count": 2226, "translator_type": "none", "url": "https://t.co/Kg30ubcrTZ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Norway"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1875639011454361860"], "editable_until_msecs": "1736025762000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "234597", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NzU2MzkwMTEzMjg1ODU3MzE=", "text": "Introducing Prism UI ✨\n\nA UI library built on top of @shadcn components to create beautiful pages 🪄\n\nAnimated components that are build on @shadcn, @tailwindcss and framer motion.\n\n100% Free and Open Source\n\nI have drawn inspiration from two main sources \n\n◆ @shadcn - The foundation and the 🐐\n◆ @steventey - The clean, modern UI/UX seen in dub has been a huge inspiration\n\nTech Stack:\n@vercel\n@shadcn \n@framer \n@tailwindcss \n@v0\n@mattgperry (framer motion)\n\nThis was my holiday project - Hope you like it 🖤\n\n#opensource #buildinpublic", "entity_set": {"hashtags": [{"indices": [509, 520], "text": "opensource"}, {"indices": [521, 535], "text": "buildinpublic"}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [53, 60]}, {"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [139, 146]}, {"id_str": "895273477711769600", "name": "Tailwind CSS", "screen_name": "tailwindcss", "indices": [148, 160]}, {"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [259, 266]}, {"id_str": "334173439", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>", "indices": [296, 306]}, {"id_str": "4686835494", "name": "Vercel", "screen_name": "vercel", "indices": [386, 393]}, {"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [394, 401]}, {"id_str": "1056838028", "name": "Framer", "screen_name": "framer", "indices": [403, 410]}, {"id_str": "895273477711769600", "name": "Tailwind CSS", "screen_name": "tailwindcss", "indices": [412, 424]}, {"id_str": "1711764162290847745", "name": "v0", "screen_name": "v0", "indices": [426, 429]}, {"id_str": "795570324532326400", "name": "<PERSON>", "screen_name": "mat<PERSON><PERSON><PERSON><PERSON>", "indices": [430, 441]}]}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 3373, "bookmarked": true, "created_at": "Sat Jan 04 20:22:42 +0000 2025", "conversation_id_str": "1875639011454361860", "display_text_range": [0, 272], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/0ctUEzOocA", "expanded_url": "https://x.com/CodeHagen/status/1875639011454361860/video/1", "id_str": "1875634239930052608", "indices": [273, 296], "media_key": "7_1875634239930052608", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1875634239930052608/pu/img/YDXwbD0kCmmVMs6q.jpg", "type": "video", "url": "https://t.co/0ctUEzOocA", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1451, "w": 2048, "resize": "fit"}, "medium": {"h": 850, "w": 1200, "resize": "fit"}, "small": {"h": 482, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2160, "width": 3048, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [127, 90], "duration_millis": 47183, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/pl/n-BMybWuhESnoySs.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/380x270/Cky-W81uZwOrJ_5y.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/508x360/g3VE8OD8Od104jb1.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/1016x720/mQWQXY-FE7shMTPM.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1875634239930052608"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [53, 60]}, {"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [139, 146]}, {"id_str": "895273477711769600", "name": "Tailwind CSS", "screen_name": "tailwindcss", "indices": [148, 160]}, {"id_str": "31206819", "name": "shadcn", "screen_name": "shadcn", "indices": [259, 266]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/0ctUEzOocA", "expanded_url": "https://x.com/CodeHagen/status/1875639011454361860/video/1", "id_str": "1875634239930052608", "indices": [273, 296], "media_key": "7_1875634239930052608", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1875634239930052608/pu/img/YDXwbD0kCmmVMs6q.jpg", "type": "video", "url": "https://t.co/0ctUEzOocA", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1451, "w": 2048, "resize": "fit"}, "medium": {"h": 850, "w": 1200, "resize": "fit"}, "small": {"h": 482, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 2160, "width": 3048, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [127, 90], "duration_millis": 47183, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/pl/n-BMybWuhESnoySs.m3u8?tag=12"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/380x270/Cky-W81uZwOrJ_5y.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/508x360/g3VE8OD8Od104jb1.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1875634239930052608/pu/vid/avc1/1016x720/mQWQXY-FE7shMTPM.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1875634239930052608"}}}]}, "favorite_count": 2538, "favorited": false, "full_text": "Introducing Prism UI ✨\n\nA UI library built on top of @shadcn components to create beautiful pages 🪄\n\nAnimated components that are build on @shadcn, @tailwindcss and framer motion.\n\n100% Free and Open Source\n\nI have drawn inspiration from two main sources \n\n◆ @shadcn - The https://t.co/0ctUEzOocA", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 16, "reply_count": 74, "retweet_count": 203, "retweeted": false, "user_id_str": "1659430006034972679", "id_str": "1875639011454361860"}, "twe_private_fields": {"created_at": 1736022162000, "updated_at": 1748554112350, "media_count": 1}}}]