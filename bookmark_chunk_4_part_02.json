[{"id": "1605527308680728579", "created_at": "2022-12-21 14:35:14 +03:00", "full_text": "💡🐍 We have 𝚏-strings in Python since 3.6\n\nLet's have a look at their powerful features \n👇 https://t.co/SbmVXmUjkd", "media": [{"type": "photo", "url": "https://t.co/SbmVXmUjkd", "thumbnail": "https://pbs.twimg.com/media/Fkf6hqhaUAAlfWL?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Fkf6hqhaUAAlfWL?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 195, "retweet_count": 36, "bookmark_count": 95, "quote_count": 1, "reply_count": 14, "views_count": 40521, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1605527308680728579", "metadata": {"__typename": "Tweet", "rest_id": "1605527308680728579", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjQ5MDY5ODM1NTYyMjMzODU4", "rest_id": "1249069835562233858", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1607738236272533505/UHVQ-yON_normal.jpg"}, "core": {"created_at": "Sat Apr 11 20:20:36 +0000 2020", "name": "<PERSON><PERSON>", "screen_name": "bascodes"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Software Developer | Consultant | Trainer\n#CoffeeChatter ☕\nTweets about\n🐍 Python and Software Development\n🗄️ Databases (SQL, NoSQL)\n☁️ CloudComputing & DevOps", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "bas.bio", "expanded_url": "https://bas.bio", "url": "https://t.co/DUnihZYwGr", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6914, "followers_count": 6777, "friends_count": 1315, "has_custom_timelines": true, "is_translator": false, "listed_count": 106, "media_count": 760, "normal_followers_count": 6777, "pinned_tweet_ids_str": ["1574821180204277762"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1249069835562233858/1675615634", "profile_interstitial_type": "", "statuses_count": 7092, "translator_type": "none", "url": "https://t.co/DUnihZYwGr", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1481186705168293894", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1605527308680728579"], "editable_until_msecs": "1671624314000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "40521", "state": "EnabledWithCount"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 95, "bookmarked": true, "created_at": "Wed Dec 21 11:35:14 +0000 2022", "conversation_id_str": "1605527308680728579", "display_text_range": [0, 89], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/SbmVXmUjkd", "expanded_url": "https://x.com/bascodes/status/1605527308680728579/photo/1", "id_str": "1605527248437923840", "indices": [90, 113], "media_key": "3_1605527248437923840", "media_url_https": "https://pbs.twimg.com/media/Fkf6hqhaUAAlfWL.jpg", "type": "photo", "url": "https://t.co/SbmVXmUjkd", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 88, "y": 1160, "h": 174, "w": 174}]}, "medium": {"faces": [{"x": 57, "y": 756, "h": 113, "w": 113}]}, "small": {"faces": [{"x": 32, "y": 428, "h": 64, "w": 64}]}, "orig": {"faces": [{"x": 88, "y": 1160, "h": 174, "w": 174}]}}, "sizes": {"large": {"h": 1416, "w": 1840, "resize": "fit"}, "medium": {"h": 923, "w": 1200, "resize": "fit"}, "small": {"h": 523, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1416, "width": 1840, "focus_rects": [{"x": 0, "y": 386, "w": 1840, "h": 1030}, {"x": 0, "y": 0, "w": 1416, "h": 1416}, {"x": 0, "y": 0, "w": 1242, "h": 1416}, {"x": 0, "y": 0, "w": 708, "h": 1416}, {"x": 0, "y": 0, "w": 1840, "h": 1416}]}, "media_results": {"result": {"media_key": "3_1605527248437923840"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/SbmVXmUjkd", "expanded_url": "https://x.com/bascodes/status/1605527308680728579/photo/1", "id_str": "1605527248437923840", "indices": [90, 113], "media_key": "3_1605527248437923840", "media_url_https": "https://pbs.twimg.com/media/Fkf6hqhaUAAlfWL.jpg", "type": "photo", "url": "https://t.co/SbmVXmUjkd", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 88, "y": 1160, "h": 174, "w": 174}]}, "medium": {"faces": [{"x": 57, "y": 756, "h": 113, "w": 113}]}, "small": {"faces": [{"x": 32, "y": 428, "h": 64, "w": 64}]}, "orig": {"faces": [{"x": 88, "y": 1160, "h": 174, "w": 174}]}}, "sizes": {"large": {"h": 1416, "w": 1840, "resize": "fit"}, "medium": {"h": 923, "w": 1200, "resize": "fit"}, "small": {"h": 523, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1416, "width": 1840, "focus_rects": [{"x": 0, "y": 386, "w": 1840, "h": 1030}, {"x": 0, "y": 0, "w": 1416, "h": 1416}, {"x": 0, "y": 0, "w": 1242, "h": 1416}, {"x": 0, "y": 0, "w": 708, "h": 1416}, {"x": 0, "y": 0, "w": 1840, "h": 1416}]}, "media_results": {"result": {"media_key": "3_1605527248437923840"}}}]}, "favorite_count": 195, "favorited": false, "full_text": "💡🐍 We have 𝚏-strings in Python since 3.6\n\nLet's have a look at their powerful features \n👇 https://t.co/SbmVXmUjkd", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 14, "retweet_count": 36, "retweeted": false, "user_id_str": "1249069835562233858", "id_str": "1605527308680728579"}, "twe_private_fields": {"created_at": 1671622514000, "updated_at": 1748554206264, "media_count": 1}}}, {"id": "1605707957030862849", "created_at": "2022-12-22 02:33:04 +03:00", "full_text": "In your opinion, what is the best Disney film from the Renaissance era? https://t.co/X5qfV8Cxch", "media": [{"type": "photo", "url": "https://t.co/X5qfV8Cxch", "thumbnail": "https://pbs.twimg.com/media/FkiettEXgAAseFp?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FkiettEXgAAseFp?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1713, "retweet_count": 207, "bookmark_count": 95, "quote_count": 883, "reply_count": 411, "views_count": 399588, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1605707957030862849", "metadata": {"__typename": "Tweet", "rest_id": "1605707957030862849", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjcyNTcyNDA4OTczNjY0MjU3", "rest_id": "1272572408973664257", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1666972851612549128/QF-LM5-h_normal.jpg"}, "core": {"created_at": "Mon Jun 15 16:51:27 +0000 2020", "name": "Cartoon Crave", "screen_name": "TheCartoonCrave"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": false, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Goodbye for now. ❤️", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 1522, "followers_count": 73216, "friends_count": 380, "has_custom_timelines": true, "is_translator": false, "listed_count": 0, "media_count": 4565, "normal_followers_count": 73216, "pinned_tweet_ids_str": ["1793041948078473672"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1272572408973664257/1686273207", "profile_interstitial_type": "", "statuses_count": 6707, "translator_type": "none", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "📧 <EMAIL>"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1466413498271821827", "professional_type": "Business", "category": [{"id": 580, "name": "Media & News Company", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1605707957030862849"], "editable_until_msecs": "1671667384000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "399588", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 95, "bookmarked": true, "created_at": "Wed Dec 21 23:33:04 +0000 2022", "conversation_id_str": "1605707957030862849", "display_text_range": [0, 71], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/X5qfV8Cxch", "expanded_url": "https://x.com/thecartooncrave/status/1605707957030862849/photo/1", "id_str": "1605707775186796544", "indices": [72, 95], "media_key": "3_1605707775186796544", "media_url_https": "https://pbs.twimg.com/media/FkiettEXgAAseFp.jpg", "type": "photo", "url": "https://t.co/X5qfV8Cxch", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 785, "y": 921, "h": 249, "w": 249}, {"x": 1185, "y": 617, "h": 455, "w": 455}]}, "medium": {"faces": [{"x": 460, "y": 539, "h": 146, "w": 146}, {"x": 694, "y": 361, "h": 267, "w": 267}]}, "small": {"faces": [{"x": 260, "y": 305, "h": 82, "w": 82}, {"x": 393, "y": 205, "h": 151, "w": 151}]}, "orig": {"faces": [{"x": 888, "y": 1041, "h": 282, "w": 282}, {"x": 1340, "y": 698, "h": 515, "w": 515}]}}, "sizes": {"large": {"h": 1198, "w": 2048, "resize": "fit"}, "medium": {"h": 702, "w": 1200, "resize": "fit"}, "small": {"h": 398, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1354, "width": 2314, "focus_rects": [{"x": 0, "y": 58, "w": 2314, "h": 1296}, {"x": 0, "y": 0, "w": 1354, "h": 1354}, {"x": 0, "y": 0, "w": 1188, "h": 1354}, {"x": 0, "y": 0, "w": 677, "h": 1354}, {"x": 0, "y": 0, "w": 2314, "h": 1354}]}, "media_results": {"result": {"media_key": "3_1605707775186796544"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/X5qfV8Cxch", "expanded_url": "https://x.com/thecartooncrave/status/1605707957030862849/photo/1", "id_str": "1605707775186796544", "indices": [72, 95], "media_key": "3_1605707775186796544", "media_url_https": "https://pbs.twimg.com/media/FkiettEXgAAseFp.jpg", "type": "photo", "url": "https://t.co/X5qfV8Cxch", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 785, "y": 921, "h": 249, "w": 249}, {"x": 1185, "y": 617, "h": 455, "w": 455}]}, "medium": {"faces": [{"x": 460, "y": 539, "h": 146, "w": 146}, {"x": 694, "y": 361, "h": 267, "w": 267}]}, "small": {"faces": [{"x": 260, "y": 305, "h": 82, "w": 82}, {"x": 393, "y": 205, "h": 151, "w": 151}]}, "orig": {"faces": [{"x": 888, "y": 1041, "h": 282, "w": 282}, {"x": 1340, "y": 698, "h": 515, "w": 515}]}}, "sizes": {"large": {"h": 1198, "w": 2048, "resize": "fit"}, "medium": {"h": 702, "w": 1200, "resize": "fit"}, "small": {"h": 398, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1354, "width": 2314, "focus_rects": [{"x": 0, "y": 58, "w": 2314, "h": 1296}, {"x": 0, "y": 0, "w": 1354, "h": 1354}, {"x": 0, "y": 0, "w": 1188, "h": 1354}, {"x": 0, "y": 0, "w": 677, "h": 1354}, {"x": 0, "y": 0, "w": 2314, "h": 1354}]}, "media_results": {"result": {"media_key": "3_1605707775186796544"}}}]}, "favorite_count": 1713, "favorited": false, "full_text": "In your opinion, what is the best Disney film from the Renaissance era? https://t.co/X5qfV8Cxch", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 883, "reply_count": 411, "retweet_count": 207, "retweeted": false, "user_id_str": "1272572408973664257", "id_str": "1605707957030862849"}, "twe_private_fields": {"created_at": 1671665584000, "updated_at": 1748554206264, "media_count": 1}}}]