[{"id": "1530244243259891713", "created_at": "2022-05-27 20:47:12 +03:00", "full_text": "🏆FPL Review Data Based Champion 21/22🏆\n\nThe scan of the top 500k #FPL teams is complete! This means top managers based on @fplreview modelling can be announced\n\nIf you finished top 500k you can find your team here (use ctrl+f): https://t.co/Zd6YI1qvSE", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 135, "retweet_count": 10, "bookmark_count": 52, "quote_count": 12, "reply_count": 16, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1530244243259891713", "metadata": {"__typename": "Tweet", "rest_id": "1530244243259891713", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMDg0MDI3MzYzNTA2MjIxMDU2", "rest_id": "1084027363506221056", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1120312434345033734/v0ZhVWVu_normal.png"}, "core": {"created_at": "Sat Jan 12 10:00:38 +0000 2019", "name": "FPL Review", "screen_name": "fplreview"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "FPL planning toolset with integrated projections & multi-period solver", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "fplreview.com", "expanded_url": "http://www.fplreview.com", "url": "https://t.co/0LPVLpqFUo", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 25566, "followers_count": 33173, "friends_count": 1054, "has_custom_timelines": true, "is_translator": false, "listed_count": 785, "media_count": 1017, "normal_followers_count": 33173, "pinned_tweet_ids_str": ["1815715199023747109"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1084027363506221056/1720889506", "profile_interstitial_type": "", "statuses_count": 10264, "translator_type": "none", "url": "https://t.co/0LPVLpqFUo", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/Zd6YI1qvSE", "legacy": {"binding_values": [{"key": "thumbnail_image", "value": {"image_value": {"height": 144, "width": 144, "url": "https://pbs.twimg.com/card_img/1926153174391377920/ZWQ_0ovV?format=jpg&name=144x144_2"}, "type": "IMAGE"}}, {"key": "domain", "value": {"string_value": "docs.google.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 420, "width": 420, "url": "https://pbs.twimg.com/card_img/1926153174391377920/ZWQ_0ovV?format=jpg&name=420x420_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 630, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926153174391377920/ZWQ_0ovV?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 100, "width": 100, "url": "https://pbs.twimg.com/card_img/1926153174391377920/ZWQ_0ovV?format=jpg&name=100x100_2"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 630, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926153174391377920/ZWQ_0ovV?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "docs.google.com", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 252, "green": 253, "red": 252}, "percentage": 64.02}, {"rgb": {"blue": 77, "green": 18, "red": 32}, "percentage": 15.73}, {"rgb": {"blue": 201, "green": 244, "red": 193}, "percentage": 14.23}, {"rgb": {"blue": 164, "green": 136, "red": 143}, "percentage": 2.38}, {"rgb": {"blue": 0, "green": 0, "red": 244}, "percentage": 1.05}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "fplreview_ranks_2122", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/Zd6YI1qvSE", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary", "url": "https://t.co/Zd6YI1qvSE", "user_refs_results": []}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1530244243259891713"], "editable_until_msecs": "1653675432000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 52, "bookmarked": true, "created_at": "Fri May 27 17:47:12 +0000 2022", "conversation_id_str": "1530244243259891713", "display_text_range": [0, 251], "entities": {"hashtags": [{"indices": [65, 69], "text": "FPL"}], "symbols": [], "timestamps": [], "urls": [{"display_url": "docs.google.com/spreadsheets/d…", "expanded_url": "https://docs.google.com/spreadsheets/d/1cpz01OyyyNXz0HjtduZRRLL3PeBbo5s-jnYaCIxJExs/edit?usp=sharing", "url": "https://t.co/Zd6YI1qvSE", "indices": [228, 251]}], "user_mentions": [{"id_str": "1084027363506221056", "name": "FPL Review", "screen_name": "fplreview", "indices": [122, 132]}]}, "favorite_count": 135, "favorited": false, "full_text": "🏆FPL Review Data Based Champion 21/22🏆\n\nThe scan of the top 500k #FPL teams is complete! This means top managers based on @fplreview modelling can be announced\n\nIf you finished top 500k you can find your team here (use ctrl+f): https://t.co/Zd6YI1qvSE", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 12, "reply_count": 16, "retweet_count": 10, "retweeted": false, "user_id_str": "1084027363506221056", "id_str": "1530244243259891713"}, "twe_private_fields": {"created_at": 1653673632000, "updated_at": 1748554262696, "media_count": 0}}}, {"id": "1530368269013557249", "created_at": "2022-05-28 05:00:02 +03:00", "full_text": "10 FREE tools for building your next startup:\n\n1.  <PERSON>va: @canva\n\n2.  Make: @make_hq\n\n3.  <PERSON>y: @TallyForms\n\n4.  Typedream: @typedreamHQ\n\n5.  CopyAI: @copy_ai\n\n6.  Slack: @SlackHQ\n\n7.  Buffer: @buffer\n\n8.  Figma: @figma\n\n9.  Mailchimp: @Mailchimp\n\n10.  Notion: @NotionHQ", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 5661, "retweet_count": 1554, "bookmark_count": 2440, "quote_count": 28, "reply_count": 90, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1530368269013557249", "metadata": {"__typename": "Tweet", "rest_id": "1530368269013557249", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzM5MDUyOTI4MjU0OTM5MTM3", "rest_id": "1339052928254939137", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1484538965109907461/VaQu5_PI_normal.jpg"}, "core": {"created_at": "Wed Dec 16 03:41:36 +0000 2020", "name": "<PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Productivity meets minimalism. Building, designing, and sharing my solutions.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "easlo.co", "expanded_url": "http://easlo.co", "url": "https://t.co/w0ts6vzqxq", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 2305, "followers_count": 365731, "friends_count": 429, "has_custom_timelines": true, "is_translator": false, "listed_count": 6577, "media_count": 1890, "normal_followers_count": 365731, "pinned_tweet_ids_str": ["1652144909493878784"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1339052928254939137/1746771878", "profile_interstitial_type": "", "statuses_count": 9930, "translator_type": "none", "url": "https://t.co/w0ts6vzqxq", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Singapore"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460976615190528004", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "patreon_handle": "e<PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1530368269013557249"], "editable_until_msecs": "1653705002000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://typefully.com/\" rel=\"nofollow\">Typefully</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2440, "bookmarked": true, "created_at": "Sat May 28 02:00:02 +0000 2022", "conversation_id_str": "1530368269013557249", "display_text_range": [0, 271], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "36542528", "name": "<PERSON><PERSON>", "screen_name": "canva", "indices": [58, 64]}, {"id_str": "2214615696", "name": "Make", "screen_name": "make_hq", "indices": [76, 84]}, {"id_str": "1262746293782208512", "name": "<PERSON><PERSON>", "screen_name": "TallyForms", "indices": [97, 108]}, {"id_str": "1352543787147382786", "name": "Typedream", "screen_name": "TypedreamHQ", "indices": [125, 137]}, {"id_str": "1300861660744372230", "name": "copy.ai", "screen_name": "copy_ai", "indices": [151, 159]}, {"id_str": "1305940272", "name": "<PERSON><PERSON>ck", "screen_name": "SlackHQ", "indices": [172, 180]}, {"id_str": "197962366", "name": "<PERSON><PERSON><PERSON>", "screen_name": "buffer", "indices": [194, 201]}, {"id_str": "3181020308", "name": "Figma", "screen_name": "figma", "indices": [214, 220]}, {"id_str": "14377870", "name": "Intuit Mailchimp", "screen_name": "Mailchimp", "indices": [237, 247]}, {"id_str": "708915428454576128", "name": "Notion", "screen_name": "NotionHQ", "indices": [262, 271]}]}, "favorite_count": 5661, "favorited": false, "full_text": "10 FREE tools for building your next startup:\n\n1.  <PERSON>va: @canva\n\n2.  Make: @make_hq\n\n3.  <PERSON>y: @TallyForms\n\n4.  Typedream: @typedreamHQ\n\n5.  CopyAI: @copy_ai\n\n6.  Slack: @SlackHQ\n\n7.  Buffer: @buffer\n\n8.  Figma: @figma\n\n9.  Mailchimp: @Mailchimp\n\n10.  Notion: @NotionHQ", "is_quote_status": false, "lang": "en", "quote_count": 28, "reply_count": 90, "retweet_count": 1554, "retweeted": false, "user_id_str": "1339052928254939137", "id_str": "1530368269013557249"}, "twe_private_fields": {"created_at": 1653703202000, "updated_at": 1748554271915, "media_count": 0}}}]