[{"id": "1628200724571070465", "created_at": "2023-02-22 04:11:17 +03:00", "full_text": "My professors taught me MATLAB during my master's degree.\n\nSo I studied 76,948 Python code repositories to teach myself Python.\n\n99.9% of them were a complete waste of time.\n\nBut these 9 are worth more than a $90,000 master's degree:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 2782, "retweet_count": 555, "bookmark_count": 3200, "quote_count": 10, "reply_count": 70, "views_count": 420656, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1628200724571070465", "metadata": {"__typename": "Tweet", "rest_id": "1628200724571070465", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTg3MTMyOTYw", "rest_id": "3187132960", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1924181461978017792/YNLsa7W5_normal.jpg"}, "core": {"created_at": "Mon Apr 20 12:55:10 +0000 2015", "name": "PyQuant News 🐍", "screen_name": "pyquantnews"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Where finance practitioners get started with Python for quant finance, algorithmic trading, and data analysis | Tweets & threads with free Python code & tools.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "links.pyquantnews.com/videos", "expanded_url": "https://links.pyquantnews.com/videos", "url": "https://t.co/xleEXqYDRj", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16470, "followers_count": 146598, "friends_count": 520, "has_custom_timelines": true, "is_translator": false, "listed_count": 2998, "media_count": 4091, "normal_followers_count": 146598, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/3187132960/1688638175", "profile_interstitial_type": "", "statuses_count": 17268, "translator_type": "none", "url": "https://t.co/xleEXqYDRj", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Free algo trading videos →"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1606127950176419840", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************"}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1628200724571070465"], "editable_until_msecs": "1677030077000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "420656", "state": "EnabledWithCount"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3200, "bookmarked": true, "created_at": "Wed Feb 22 01:11:17 +0000 2023", "conversation_id_str": "1628200724571070465", "display_text_range": [0, 233], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 2782, "favorited": false, "full_text": "My professors taught me MATLAB during my master's degree.\n\nSo I studied 76,948 Python code repositories to teach myself Python.\n\n99.9% of them were a complete waste of time.\n\nBut these 9 are worth more than a $90,000 master's degree:", "is_quote_status": false, "lang": "en", "quote_count": 10, "reply_count": 70, "retweet_count": 555, "retweeted": false, "user_id_str": "3187132960", "id_str": "1628200724571070465"}, "twe_private_fields": {"created_at": 1677028277000, "updated_at": 1748554204513, "media_count": 0}}}, {"id": "1630274891281846275", "created_at": "2023-02-27 21:33:17 +03:00", "full_text": "I will create a Fantasy F1 league so that we can all compete and see who’s the best at predicting the outcomes! 🤩\n\nI will offer a giveaway for the winner 🏆 Looking at the data will become even more important!\n\nWhich app/website do you recommend? I only used GridRival so far 📄", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 614, "retweet_count": 11, "bookmark_count": 24, "quote_count": 3, "reply_count": 24, "views_count": 131666, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1630274891281846275", "metadata": {"__typename": "Tweet", "rest_id": "1630274891281846275", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTAyNjE2NTcxMTU1NTIxNTM2", "rest_id": "1502616571155521536", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1677630690551029760/pw9d1xVr_normal.jpg"}, "core": {"created_at": "Sat Mar 12 12:08:14 +0000 2022", "name": "Formula Data Analysis", "screen_name": "FDataAnalysis"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "|📖 F1 Understanding Starts by Clicking Follow! ⬆️|📈Learn To Read F1 Telemetry Data 📊|⚙️ Motorsport Performance Engineer, PhD in Motorcycle Dynamics 🏎️|", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/fdataanalysis", "expanded_url": "https://linktr.ee/fdataanalysis", "url": "https://t.co/1BsSOomAMP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 17919, "followers_count": 209088, "friends_count": 1533, "has_custom_timelines": true, "is_translator": false, "listed_count": 2116, "media_count": 3198, "normal_followers_count": 209088, "pinned_tweet_ids_str": ["1923424832315666901"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1502616571155521536/1664013347", "profile_interstitial_type": "", "statuses_count": 15292, "translator_type": "none", "url": "https://t.co/1BsSOomAMP", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Turn Notifications On! 🔔"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************", "patreon_handle": ""}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1630274891281846275"], "editable_until_msecs": "1677524597000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "131666", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 24, "bookmarked": true, "created_at": "Mon Feb 27 18:33:17 +0000 2023", "conversation_id_str": "1630274891281846275", "display_text_range": [0, 276], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 614, "favorited": false, "full_text": "I will create a Fantasy F1 league so that we can all compete and see who’s the best at predicting the outcomes! 🤩\n\nI will offer a giveaway for the winner 🏆 Looking at the data will become even more important!\n\nWhich app/website do you recommend? I only used GridRival so far 📄", "is_quote_status": false, "lang": "en", "quote_count": 3, "reply_count": 24, "retweet_count": 11, "retweeted": false, "user_id_str": "1502616571155521536", "id_str": "1630274891281846275"}, "twe_private_fields": {"created_at": 1677522797000, "updated_at": 1748554204513, "media_count": 0}}}]