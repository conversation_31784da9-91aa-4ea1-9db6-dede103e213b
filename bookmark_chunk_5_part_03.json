[{"id": "1846808474761810144", "created_at": "2024-10-17 10:00:27 +03:00", "full_text": "https://t.co/1cZtCx05xT", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 281, "retweet_count": 27, "bookmark_count": 354, "quote_count": 3, "reply_count": 13, "views_count": 147669, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1846808474761810144", "metadata": {"__typename": "Tweet", "rest_id": "1846808474761810144", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTg3NjAxMDM0MzM5NTYxNDcy", "rest_id": "1587601034339561472", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1927967636618629120/f0H274GB_normal.jpg"}, "core": {"created_at": "Wed Nov 02 00:22:51 +0000 2022", "name": "tetsuo.sol (mog/acc)", "screen_name": "7etsuo"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": true, "default_profile_image": false, "description": "C & Assembly Gods | Mog/Acc\n\nTetsuo AI.\n\nEndorsing only the finest: #MOGCOIN #MUMU #AI16Z #APU", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "c-asm.com", "expanded_url": "https://www.c-asm.com/", "url": "https://t.co/FXis6um4QG", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 102824, "followers_count": 84312, "friends_count": 2350, "has_custom_timelines": false, "is_translator": false, "listed_count": 664, "media_count": 6781, "normal_followers_count": 84312, "pinned_tweet_ids_str": ["1926123848396681366"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1587601034339561472/1748321162", "profile_interstitial_type": "", "statuses_count": 26523, "translator_type": "none", "url": "https://t.co/FXis6um4QG", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "moon base"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "professional": {"rest_id": "1603858867515174912", "professional_type": "Business", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "bitcoin_handle": "******************************************", "ethereum_handle": "******************************************", "patreon_handle": "7etsuo"}, "super_follow_eligible": true, "verification": {"verified": false, "verified_type": "Business"}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1846808474761810144"], "editable_until_msecs": "1729152027000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "147669", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "article": {"article_results": {"result": {"rest_id": "1846804034080198658", "id": "QXJ0aWNsZUVudGl0eToxODQ2ODA0MDM0MDgwMTk4NjU4", "title": "Building a shell in C.", "preview_text": "In this article, we are going to write a shell in C! This shell reads commands from the user, parses them, and executes them by forking a child process. \nTable of Contents\nCommunity\nThe Command Struct", "cover_media": {"id": "QXBpTWVkaWE6DAAFCgABGaEtu50aUAAKAAIWCEq6sNowAAAA", "media_key": "3_1846807606016495616", "media_id": "1846807606016495616", "media_info": {"__typename": "ApiImage", "original_img_height": 1638, "original_img_width": 4096, "original_img_url": "https://pbs.twimg.com/media/GaEtu50aUAAeb7Y.jpg", "color_info": {"palette": [{"percentage": 38.83, "rgb": {"blue": 64, "green": 39, "red": 24}}, {"percentage": 26.1, "rgb": {"blue": 87, "green": 29, "red": 39}}, {"percentage": 7.54, "rgb": {"blue": 134, "green": 31, "red": 86}}, {"percentage": 4.61, "rgb": {"blue": 219, "green": 203, "red": 174}}, {"percentage": 1.99, "rgb": {"blue": 195, "green": 43, "red": 93}}]}}}, "lifecycle_state": {"modified_at_secs": 1732436951}, "metadata": {"first_published_at_secs": 1729148426}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 354, "bookmarked": true, "created_at": "Thu Oct 17 07:00:27 +0000 2024", "conversation_id_str": "1846808474761810144", "display_text_range": [0, 23], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "x.com/i/article/1846…", "expanded_url": "http://x.com/i/article/1846804034080198658", "url": "https://t.co/1cZtCx05xT", "indices": [0, 23]}], "user_mentions": []}, "favorite_count": 281, "favorited": false, "full_text": "https://t.co/1cZtCx05xT", "is_quote_status": false, "lang": "zxx", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 3, "reply_count": 13, "retweet_count": 27, "retweeted": false, "user_id_str": "1587601034339561472", "id_str": "1846808474761810144"}, "twe_private_fields": {"created_at": 1729148427000, "updated_at": 1748554137298, "media_count": 0}}}, {"id": "1846879396625617382", "created_at": "2024-10-17 14:42:16 +03:00", "full_text": "Just added AWS and Azure to my Free Data Analyst Bootcamp on YouTube! \n\nIt now covers MySQL Beginner to Advanced, Excel, Tableau, Power Bi, Python, Pandas, AWS, and Azure.\n\nIt also covers how to build a portfolio website for free, resume building with templates, and 10+ projects to add to your portfolio!", "media": [{"type": "video", "url": "https://t.co/R9T9LrPWWi", "thumbnail": "https://pbs.twimg.com/amplify_video_thumb/1846879321228455936/img/KE8_1npM4INrWEd-.jpg?name=thumb", "original": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/1080x1920/vFlN7GZlGccgo_CY.mp4?tag=16"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1042, "retweet_count": 179, "bookmark_count": 1080, "quote_count": 4, "reply_count": 29, "views_count": 48127, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1846879396625617382", "metadata": {"__typename": "Tweet", "rest_id": "1846879396625617382", "core": {"user_results": {"result": {"__typename": "User", "id": "********************************", "rest_id": "1507434998894415872", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1507436693326077962/vp1qJvn3_normal.jpg"}, "core": {"created_at": "Fri Mar 25 19:11:41 +0000 2022", "name": "<PERSON>", "screen_name": "Alex_TheAnalyst"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Alex The Analyst Official Twitter Account | YT Alex The Analyst | IG @Alex_The_Analyst | YouTube’s #1 Data Analyst Channel (don’t fact check that)", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "analystbuilder.com", "expanded_url": "https://www.analystbuilder.com/", "url": "https://t.co/jfCfhRftvl", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 6194, "followers_count": 78225, "friends_count": 44, "has_custom_timelines": true, "is_translator": false, "listed_count": 382, "media_count": 452, "normal_followers_count": 78225, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1507434998894415872/**********", "profile_interstitial_type": "", "statuses_count": 1951, "translator_type": "none", "url": "https://t.co/jfCfhRftvl", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": ""}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1761016481922359394", "professional_type": "Creator", "category": [{"id": 144, "name": "Education", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1846879396625617382"], "editable_until_msecs": "1729168936000", "is_edit_eligible": true, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "48127", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NDY4NzkzOTY1NDU4NzE4NzI=", "text": "Just added AWS and Azure to my Free Data Analyst Bootcamp on YouTube! \n\nIt now covers MySQL Beginner to Advanced, Excel, Tableau, Power Bi, Python, Pandas, AWS, and Azure.\n\nIt also covers how to build a portfolio website for free, resume building with templates, and 10+ projects to add to your portfolio!", "entity_set": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 1080, "bookmarked": true, "created_at": "Thu Oct 17 11:42:16 +0000 2024", "conversation_id_str": "1846879396625617382", "display_text_range": [0, 279], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/R9T9LrPWWi", "expanded_url": "https://x.com/Alex_TheAnalyst/status/1846879396625617382/video/1", "id_str": "1846879321228455936", "indices": [280, 303], "media_key": "13_1846879321228455936", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1846879321228455936/img/KE8_1npM4INrWEd-.jpg", "type": "video", "url": "https://t.co/R9T9LrPWWi", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1920, "w": 1080, "resize": "fit"}, "medium": {"h": 1200, "w": 675, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1920, "width": 1080, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 30066, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1846879321228455936/pl/Ne7KrMLJdPRSRWhR.m3u8?tag=16&v=51c"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/320x568/eU1j4dcjPaulvR9S.mp4?tag=16"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/480x852/9FwfusiZ2W3h2AJH.mp4?tag=16"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/720x1280/V0ZP_gw8rBfDsZCe.mp4?tag=16"}, {"bitrate": 10368000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/1080x1920/vFlN7GZlGccgo_CY.mp4?tag=16"}]}, "media_results": {"result": {"media_key": "13_1846879321228455936"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/R9T9LrPWWi", "expanded_url": "https://x.com/Alex_TheAnalyst/status/1846879396625617382/video/1", "id_str": "1846879321228455936", "indices": [280, 303], "media_key": "13_1846879321228455936", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1846879321228455936/img/KE8_1npM4INrWEd-.jpg", "type": "video", "url": "https://t.co/R9T9LrPWWi", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1920, "w": 1080, "resize": "fit"}, "medium": {"h": 1200, "w": 675, "resize": "fit"}, "small": {"h": 680, "w": 383, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1920, "width": 1080, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [9, 16], "duration_millis": 30066, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1846879321228455936/pl/Ne7KrMLJdPRSRWhR.m3u8?tag=16&v=51c"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/320x568/eU1j4dcjPaulvR9S.mp4?tag=16"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/480x852/9FwfusiZ2W3h2AJH.mp4?tag=16"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/720x1280/V0ZP_gw8rBfDsZCe.mp4?tag=16"}, {"bitrate": 10368000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1846879321228455936/vid/avc1/1080x1920/vFlN7GZlGccgo_CY.mp4?tag=16"}]}, "media_results": {"result": {"media_key": "13_1846879321228455936"}}}]}, "favorite_count": 1042, "favorited": false, "full_text": "Just added AWS and Azure to my Free Data Analyst Bootcamp on YouTube! \n\nIt now covers MySQL Beginner to Advanced, Excel, Tableau, Power Bi, Python, Pandas, AWS, and Azure.\n\nIt also covers how to build a portfolio website for free, resume building with templates, and 10+ projects https://t.co/R9T9LrPWWi", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 4, "reply_count": 29, "retweet_count": 179, "retweeted": false, "user_id_str": "1507434998894415872", "id_str": "1846879396625617382"}, "twe_private_fields": {"created_at": 1729165336000, "updated_at": 1748554137298, "media_count": 1}}}]