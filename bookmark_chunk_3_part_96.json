[{"id": "1599034706627989505", "created_at": "2022-12-03 16:35:57 +03:00", "full_text": "The most powerful paradoxes of life:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 17774, "retweet_count": 4811, "bookmark_count": 10958, "quote_count": 235, "reply_count": 348, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1599034706627989505", "metadata": {"__typename": "Tweet", "rest_id": "1599034706627989505", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjozMTI2ODE5NTM=", "rest_id": "312681953", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1586859332104343552/V1HRpbP1_normal.jpg"}, "core": {"created_at": "<PERSON>e Jun 07 14:14:17 +0000 2011", "name": "<PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "NYT Bestselling Author of The 5 Types of Wealth. Gave up a grand slam on ESPN in 2012 and still waiting for it to land. Order my book below 👇", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "the5typesofwealth.com", "expanded_url": "https://www.the5typesofwealth.com", "url": "https://t.co/exGtZduEvq", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 151241, "followers_count": 1080230, "friends_count": 278, "has_custom_timelines": true, "is_translator": false, "listed_count": 16658, "media_count": 4870, "normal_followers_count": 1080230, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/312681953/1739539616", "profile_interstitial_type": "", "statuses_count": 69302, "translator_type": "none", "url": "https://t.co/exGtZduEvq", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "New York, USA"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1675186015294611456", "professional_type": "Business", "category": [{"id": 957, "name": "Author", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": true, "venmo_handle": "<PERSON><PERSON><PERSON><PERSON>"}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1599034706627989505"], "editable_until_msecs": "1670076357000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 10958, "bookmarked": true, "created_at": "Sat Dec 03 13:35:57 +0000 2022", "conversation_id_str": "1599034706627989505", "display_text_range": [0, 36], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 17774, "favorited": false, "full_text": "The most powerful paradoxes of life:", "is_quote_status": false, "lang": "en", "quote_count": 235, "reply_count": 348, "retweet_count": 4811, "retweeted": false, "user_id_str": "312681953", "id_str": "1599034706627989505"}, "twe_private_fields": {"created_at": 1670074557000, "updated_at": 1748554206264, "media_count": 0}}}, {"id": "1601222944516182018", "created_at": "2022-12-09 17:31:13 +03:00", "full_text": "Coding games are the best way to learn coding.\n\nFrom CSS, JavaScript, Python to Blockchain.\n\nHere are 10 of the best online games to learn coding:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 4488, "retweet_count": 1388, "bookmark_count": 3844, "quote_count": 17, "reply_count": 177, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1601222944516182018", "metadata": {"__typename": "Tweet", "rest_id": "1601222944516182018", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTA2MDU1MDA1OTkyMDI2MTE1", "rest_id": "1506055005992026115", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1917658305666707456/uQd7xQ0F_normal.jpg"}, "core": {"created_at": "Mon Mar 21 23:48:18 +0000 2022", "name": "<PERSON> ✪", "screen_name": "hasantoxr"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Sharing insights on AI, Tech Trends, Online Business & Productivity • I help people master AI, Tech Tools & Digital Skills • AI Educator & Writer @theprohumanai", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "bio.link/hasantoxr", "expanded_url": "https://bio.link/hasantoxr", "url": "https://t.co/4ZOcih4nGj", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 49937, "followers_count": 398806, "friends_count": 503, "has_custom_timelines": true, "is_translator": false, "listed_count": 8451, "media_count": 15022, "normal_followers_count": 398806, "pinned_tweet_ids_str": ["1918808229402079576"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1506055005992026115/1719774703", "profile_interstitial_type": "", "statuses_count": 60324, "translator_type": "none", "url": "https://t.co/4ZOcih4nGj", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Free Products + Sponsorships →"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1506057785544691712", "professional_type": "Creator", "category": []}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1601222944516182018"], "editable_until_msecs": "1670598073000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://hypefury.com\" rel=\"nofollow\">Hypefury</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 3844, "bookmarked": true, "created_at": "Fri Dec 09 14:31:13 +0000 2022", "conversation_id_str": "1601222944516182018", "display_text_range": [0, 146], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 4488, "favorited": false, "full_text": "Coding games are the best way to learn coding.\n\nFrom CSS, JavaScript, Python to Blockchain.\n\nHere are 10 of the best online games to learn coding:", "is_quote_status": false, "lang": "en", "quote_count": 17, "reply_count": 177, "retweet_count": 1388, "retweeted": false, "user_id_str": "1506055005992026115", "id_str": "1601222944516182018"}, "twe_private_fields": {"created_at": 1670596273000, "updated_at": 1748554206264, "media_count": 0}}}]