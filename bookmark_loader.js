// Function to load all markdown files and parse bookmarks
async function loadBookmarks() {
    try {
        // Get list of all markdown files
        const markdownFiles = await getMarkdownFileList();
        const bookmarks = [];
        
        // Load and parse each markdown file
        for (const file of markdownFiles) {
            const response = await fetch(file);
            const text = await response.text();
            parseMarkdown(text, bookmarks);
        }
        
        // Render bookmarks in UI
        renderBookmarks(bookmarks);
        renderCategories(bookmarks);
        document.getElementById('bookmark-count').textContent = bookmarks.length;
        
        console.log(`Successfully loaded ${bookmarks.length} bookmarks`);
    } catch (error) {
        console.error('Error loading bookmarks:', error);
        alert('Failed to load bookmarks. Please check the console for details.');
    }
}

// Get list of all bookmark markdown files
async function getMarkdownFileList() {
    // This should be replaced with actual file listing logic
    // For now, return sample files
    return [
        'bookmark_chunk_1_part_01.md',
        'bookmark_chunk_2_part_36.md',
        // Add all other markdown files here
    ];
}

// Parse markdown content and extract bookmarks
function parseMarkdown(markdown, bookmarks) {
    const sections = markdown.split(/\n---+\n/);
    
    for (const section of sections) {
        if (!section.includes('## Category:')) continue;
        
        const categoryMatch = section.match(/## Category:\s*(.+)/);
        if (!categoryMatch) continue;
        
        const category = categoryMatch[1].trim();
        const bookmarkBlocks = section.split(/\*   \*\*Author:\*\*/).slice(1);
        
        for (const block of bookmarkBlocks) {
            try {
                const bookmark = {
                    category: category,
                    author: extractValue(block, 'Author'),
                    date: extractValue(block, 'Date'),
                    content: extractValue(block, 'Content'),
                    url: extractValue(block, 'URL')
                };
                
                // Extract media if present
                const mediaMatch = block.match(/\*\*Media:\*\*(.+?)(?:\n|$)/);
                if (mediaMatch) {
                    bookmark.media = mediaMatch[1].trim();
                }
                
                bookmarks.push(bookmark);
            } catch (e) {
                console.warn('Error parsing bookmark block:', e);
            }
        }
    }
}

// Helper to extract values from markdown
function extractValue(block, key) {
    const regex = new RegExp(`\\*\\*${key}:\\*\\*([^\\*]+)`);
    const match = block.match(regex);
    return match ? match[1].trim() : '';
}

// Render bookmarks in the UI
function renderBookmarks(bookmarks) {
    const mainContent = document.querySelector('.main-content');
    mainContent.innerHTML = '';
    
    bookmarks.forEach(bookmark => {
        const card = document.createElement('div');
        card.className = 'bookmark-card';
        
        card.innerHTML = `
            <div class="card-content">
                <span class="card-category">${bookmark.category}</span>
                <div class="card-author">${bookmark.author}</div>
                <div class="card-date">${bookmark.date}</div>
                <p class="card-text">${bookmark.content}</p>
                ${bookmark.media ? `<div class="card-media">${bookmark.media}</div>` : ''}
                <a href="${bookmark.url}" class="card-link" target="_blank">View on Twitter</a>
            </div>
        `;
        
        mainContent.appendChild(card);
    });
}

// Render categories in the sidebar
function renderCategories(bookmarks) {
    const categoryList = document.querySelector('.category-list');
    const categories = [...new Set(bookmarks.map(b => b.category))];
    
    categories.forEach(category => {
        const li = document.createElement('li');
        li.className = 'category-item';
        li.innerHTML = `<a href="#" class="category-link">${category}</a>`;
        categoryList.appendChild(li);
    });
    
    // Add event listeners to category links
    document.querySelectorAll('.category-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const selectedCategory = e.target.textContent;
            filterBookmarks(selectedCategory, bookmarks);
        });
    });
}

// Filter bookmarks by category
function filterBookmarks(category, bookmarks) {
    const filtered = bookmarks.filter(b => b.category === category);
    renderBookmarks(filtered);
    
    // Update active category
    document.querySelectorAll('.category-link').forEach(link => {
        link.classList.remove('active');
        if (link.textContent === category) {
            link.classList.add('active');
        }
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', loadBookmarks);
