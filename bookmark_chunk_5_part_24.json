[{"id": "1868924750161969594", "created_at": "2024-12-17 10:42:38 +03:00", "full_text": "-- FREE ALERT --\n\n4 Absolutely FREE Books on SQL", "media": [{"type": "photo", "url": "https://t.co/L3t2NNHDO6", "thumbnail": "https://pbs.twimg.com/media/Ge_BI3iXkAAsc98?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Ge_BI3iXkAAsc98?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/L3t2NNHDO6", "thumbnail": "https://pbs.twimg.com/media/Ge_BI3gW8AA-V8s?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Ge_BI3gW8AA-V8s?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/L3t2NNHDO6", "thumbnail": "https://pbs.twimg.com/media/Ge_BI8zXsAAAZWG?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Ge_BI8zXsAAAZWG?format=jpg&name=orig"}, {"type": "photo", "url": "https://t.co/L3t2NNHDO6", "thumbnail": "https://pbs.twimg.com/media/Ge_BI3cWQAAbLt3?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/Ge_BI3cWQAAbLt3?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1397, "retweet_count": 181, "bookmark_count": 2235, "quote_count": 6, "reply_count": 21, "views_count": 157872, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1868924750161969594", "metadata": {"__typename": "Tweet", "rest_id": "1868924750161969594", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo2OTk0MTk3OA==", "rest_id": "69941978", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1621910730227449856/iW8AGVCr_normal.jpg"}, "core": {"created_at": "Sat Aug 29 19:28:43 +0000 2009", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "| Tech Writer, Educator | Python, Java, JavaScript, SQL | DSA, Development | Free Resources, AI Tools | Other Version: @therealswapna | Building @JabardastDEV |", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "buymeacoffee.com/swapnakpanda", "expanded_url": "https://buymeacoffee.com/swapnakpanda", "url": "https://t.co/1n2NQhuGCw", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 91836, "followers_count": 204228, "friends_count": 197, "has_custom_timelines": true, "is_translator": false, "listed_count": 3427, "media_count": 3337, "normal_followers_count": 204228, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/69941978/1657945418", "profile_interstitial_type": "", "statuses_count": 55050, "translator_type": "none", "url": "https://t.co/1n2NQhuGCw", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "🌐  Support my work 👉"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1460994792075108352", "professional_type": "Creator", "category": [{"id": 1042, "name": "Content Creator", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1868924750161969594"], "editable_until_msecs": "1734424958000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "157872", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": false, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4Njg5MjQ3NTAxMDc0NjM2ODE=", "text": "-- FREE ALERT --\n\n4 Absolutely FREE Books on SQL", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "richtext": {"richtext_tags": [{"from_index": 3, "to_index": 13, "richtext_types": ["Bold"]}]}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 2235, "bookmarked": true, "created_at": "Tue Dec 17 07:42:38 +0000 2024", "conversation_id_str": "1868924750161969594", "display_text_range": [0, 48], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924728473260032", "indices": [49, 72], "media_key": "3_1868924728473260032", "media_url_https": "https://pbs.twimg.com/media/Ge_BI3iXkAAsc98.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1166, "w": 886, "resize": "fit"}, "medium": {"h": 1166, "w": 886, "resize": "fit"}, "small": {"h": 680, "w": 517, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1166, "width": 886, "focus_rects": [{"x": 0, "y": 130, "w": 886, "h": 496}, {"x": 0, "y": 0, "w": 886, "h": 886}, {"x": 0, "y": 0, "w": 886, "h": 1010}, {"x": 146, "y": 0, "w": 583, "h": 1166}, {"x": 0, "y": 0, "w": 886, "h": 1166}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924728473260032"}}}, {"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924728464830464", "indices": [49, 72], "media_key": "3_1868924728464830464", "media_url_https": "https://pbs.twimg.com/media/Ge_BI3gW8AA-V8s.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 666, "y": 58, "h": 131, "w": 131}]}, "medium": {"faces": [{"x": 662, "y": 57, "h": 130, "w": 130}]}, "small": {"faces": [{"x": 375, "y": 32, "h": 73, "w": 73}]}, "orig": {"faces": [{"x": 666, "y": 58, "h": 131, "w": 131}]}}, "sizes": {"large": {"h": 1206, "w": 974, "resize": "fit"}, "medium": {"h": 1200, "w": 969, "resize": "fit"}, "small": {"h": 680, "w": 549, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1206, "width": 974, "focus_rects": [{"x": 0, "y": 0, "w": 974, "h": 545}, {"x": 0, "y": 0, "w": 974, "h": 974}, {"x": 0, "y": 0, "w": 974, "h": 1110}, {"x": 0, "y": 0, "w": 603, "h": 1206}, {"x": 0, "y": 0, "w": 974, "h": 1206}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924728464830464"}}}, {"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924729886748672", "indices": [49, 72], "media_key": "3_1868924729886748672", "media_url_https": "https://pbs.twimg.com/media/Ge_BI8zXsAAAZWG.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 335, "w": 240, "resize": "fit"}, "medium": {"h": 335, "w": 240, "resize": "fit"}, "small": {"h": 335, "w": 240, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 335, "width": 240, "focus_rects": [{"x": 0, "y": 159, "w": 240, "h": 134}, {"x": 0, "y": 95, "w": 240, "h": 240}, {"x": 0, "y": 61, "w": 240, "h": 274}, {"x": 41, "y": 0, "w": 168, "h": 335}, {"x": 0, "y": 0, "w": 240, "h": 335}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924729886748672"}}}, {"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924728448008192", "indices": [49, 72], "media_key": "3_1868924728448008192", "media_url_https": "https://pbs.twimg.com/media/Ge_BI3cWQAAbLt3.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 215, "y": 334, "h": 88, "w": 88}, {"x": 97, "y": 544, "h": 145, "w": 145}]}, "medium": {"faces": [{"x": 215, "y": 334, "h": 88, "w": 88}, {"x": 97, "y": 544, "h": 145, "w": 145}]}, "small": {"faces": [{"x": 146, "y": 228, "h": 60, "w": 60}, {"x": 66, "y": 371, "h": 98, "w": 98}]}, "orig": {"faces": [{"x": 215, "y": 334, "h": 88, "w": 88}, {"x": 97, "y": 544, "h": 145, "w": 145}]}}, "sizes": {"large": {"h": 996, "w": 646, "resize": "fit"}, "medium": {"h": 996, "w": 646, "resize": "fit"}, "small": {"h": 680, "w": 441, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 996, "width": 646, "focus_rects": [{"x": 0, "y": 634, "w": 646, "h": 362}, {"x": 0, "y": 350, "w": 646, "h": 646}, {"x": 0, "y": 260, "w": 646, "h": 736}, {"x": 148, "y": 0, "w": 498, "h": 996}, {"x": 0, "y": 0, "w": 646, "h": 996}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924728448008192"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924728473260032", "indices": [49, 72], "media_key": "3_1868924728473260032", "media_url_https": "https://pbs.twimg.com/media/Ge_BI3iXkAAsc98.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1166, "w": 886, "resize": "fit"}, "medium": {"h": 1166, "w": 886, "resize": "fit"}, "small": {"h": 680, "w": 517, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1166, "width": 886, "focus_rects": [{"x": 0, "y": 130, "w": 886, "h": 496}, {"x": 0, "y": 0, "w": 886, "h": 886}, {"x": 0, "y": 0, "w": 886, "h": 1010}, {"x": 146, "y": 0, "w": 583, "h": 1166}, {"x": 0, "y": 0, "w": 886, "h": 1166}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924728473260032"}}}, {"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924728464830464", "indices": [49, 72], "media_key": "3_1868924728464830464", "media_url_https": "https://pbs.twimg.com/media/Ge_BI3gW8AA-V8s.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 666, "y": 58, "h": 131, "w": 131}]}, "medium": {"faces": [{"x": 662, "y": 57, "h": 130, "w": 130}]}, "small": {"faces": [{"x": 375, "y": 32, "h": 73, "w": 73}]}, "orig": {"faces": [{"x": 666, "y": 58, "h": 131, "w": 131}]}}, "sizes": {"large": {"h": 1206, "w": 974, "resize": "fit"}, "medium": {"h": 1200, "w": 969, "resize": "fit"}, "small": {"h": 680, "w": 549, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1206, "width": 974, "focus_rects": [{"x": 0, "y": 0, "w": 974, "h": 545}, {"x": 0, "y": 0, "w": 974, "h": 974}, {"x": 0, "y": 0, "w": 974, "h": 1110}, {"x": 0, "y": 0, "w": 603, "h": 1206}, {"x": 0, "y": 0, "w": 974, "h": 1206}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924728464830464"}}}, {"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924729886748672", "indices": [49, 72], "media_key": "3_1868924729886748672", "media_url_https": "https://pbs.twimg.com/media/Ge_BI8zXsAAAZWG.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 335, "w": 240, "resize": "fit"}, "medium": {"h": 335, "w": 240, "resize": "fit"}, "small": {"h": 335, "w": 240, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 335, "width": 240, "focus_rects": [{"x": 0, "y": 159, "w": 240, "h": 134}, {"x": 0, "y": 95, "w": 240, "h": 240}, {"x": 0, "y": 61, "w": 240, "h": 274}, {"x": 41, "y": 0, "w": 168, "h": 335}, {"x": 0, "y": 0, "w": 240, "h": 335}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924729886748672"}}}, {"display_url": "pic.x.com/L3t2NNHDO6", "expanded_url": "https://x.com/swapnakpanda/status/1868924750161969594/photo/1", "id_str": "1868924728448008192", "indices": [49, 72], "media_key": "3_1868924728448008192", "media_url_https": "https://pbs.twimg.com/media/Ge_BI3cWQAAbLt3.jpg", "type": "photo", "url": "https://t.co/L3t2NNHDO6", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": [{"x": 215, "y": 334, "h": 88, "w": 88}, {"x": 97, "y": 544, "h": 145, "w": 145}]}, "medium": {"faces": [{"x": 215, "y": 334, "h": 88, "w": 88}, {"x": 97, "y": 544, "h": 145, "w": 145}]}, "small": {"faces": [{"x": 146, "y": 228, "h": 60, "w": 60}, {"x": 66, "y": 371, "h": 98, "w": 98}]}, "orig": {"faces": [{"x": 215, "y": 334, "h": 88, "w": 88}, {"x": 97, "y": 544, "h": 145, "w": 145}]}}, "sizes": {"large": {"h": 996, "w": 646, "resize": "fit"}, "medium": {"h": 996, "w": 646, "resize": "fit"}, "small": {"h": 680, "w": 441, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 996, "width": 646, "focus_rects": [{"x": 0, "y": 634, "w": 646, "h": 362}, {"x": 0, "y": 350, "w": 646, "h": 646}, {"x": 0, "y": 260, "w": 646, "h": 736}, {"x": 148, "y": 0, "w": 498, "h": 996}, {"x": 0, "y": 0, "w": 646, "h": 996}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1868924728448008192"}}}]}, "favorite_count": 1397, "favorited": false, "full_text": "-- FREE ALERT --\n\n4 Absolutely FREE Books on SQL https://t.co/L3t2NNHDO6", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 21, "retweet_count": 181, "retweeted": false, "user_id_str": "69941978", "id_str": "1868924750161969594"}, "twe_private_fields": {"created_at": 1734421358000, "updated_at": 1748554127577, "media_count": 4}}}, {"id": "1869002930457919718", "created_at": "2024-12-17 15:53:17 +03:00", "full_text": "I've had the same experience when applying to Canonical\n\nHere's what I got\n\nDear <PERSON><PERSON><PERSON>\n\nThank you for your interest in our Software Engineer - App Stores Backend (Remote) position at Canonical.\n\nI am the hiring lead for this role and a member of the leadership team. If I think there are other roles that would be interesting for you I'll suggest them to you, and I will be happy to answer questions you have about the company and our engineering approach.\n\nI have reviewed your resume and application answers and moved you forward. We start with a formal assessment stage consisting of a written interview and tests that you can take at your own pace, followed by three stages of interviews. In outline:\n\nReview of your application materials (done)\nWritten interview and psychometric assessment (this step)\nTechnical assessment and team interviews\nHiring manager & senior leadership Interviews\nOffer\nThis is your written interview, a prepared statement to cover your strengths, interests, priorities, experience, ideas and ambition.   For your submission please write in your own words and allow us to understand specific examples that highlight your skills and experience. We will refer back to this submission during the interview process to explore your listed experience further. Please note that AI generated responses cannot be accepted.\n\nWe use an initial written interview to get a sense of your experience, your priorities, and how you communicate. We have multiple reviewers assess your answer in an anonymized system to reduce bias and allow candidates from a range of backgrounds to demonstrate their ability, experience and insights. Please feel free to reuse answers if you have made multiple applications to roles at Canonical. There is no rush, I will leave your application open a few weeks to let you find the time for this step.\n\nPlease address the following in a PDF document:\n\nEngineering experience\n\nDescribe your level of experience in Python, and how you have attained it.\nDescribe your experience with SQL and relational data modeling, and summarize your learning with large-scale database backed applications.\nDescribe a case where it was very difficult to test code you were writing, but you found a reliable way to do it.\nWhat kinds of software projects have you worked on before? Which operating systems, development toolchains, languages, databases?\nWould you describe yourself as a high quality coder? Why?\nWould you describe yourself as an architect of resilient software? If so, why, and in which sorts of applications?\nOutline your thoughts on open source software development. What is important to get right in open source projects? What open source projects have you worked on? Have you been an open source maintainer, on which projects, and what was your role?\nDescribe your experience building large systems with many services - web front ends, REST APIs, data stores, event processing and other kinds of integration between components. What are the key things to think about in regard to architecture, maintainability, and reliability in these large systems?\nHow comprehensive would you say your knowledge of a Linux distribution is, from the kernel up? How familiar are you with low-level system architecture, runtimes and Linux distro packaging? How have you gained this knowledge?\nDescribe any experience you have with low-level embedded systems engineering, on Linux or other embedded operating systems\nDescribe your experience with large-scale IT operations, SAAS, or other running services, in a devops or IS or system administration capacity\nDescribe your experience with public cloud based operations - how well do you understand large-scale public cloud estate management and developer experience?\nDescribe your experience with enterprise infrastructure and application management, either as a user running enterprise operations, or as a vendor targeting the enterprise market\nOutline your thoughts on quality in software development.", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": "1868952268139381084", "retweeted_status": null, "quoted_status": null, "favorite_count": 36, "retweet_count": 1, "bookmark_count": 42, "quote_count": 1, "reply_count": 2, "views_count": 13114, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1869002930457919718", "metadata": {"__typename": "Tweet", "rest_id": "1869002930457919718", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MjA4NTc3ODY0ODYwOTk5Njg=", "rest_id": "720857786486099968", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1870928839959785472/uBOp8AKo_normal.jpg"}, "core": {"created_at": "Fri Apr 15 06:14:23 +0000 2016", "name": "pavi2410", "screen_name": "PavitraGolchha"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Computerphile. 👨‍💻 Co-founder, @KodularIO. Building @HelloFolo, #ProjectJigsaw, #based the everything database app", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "pavi2410.me", "expanded_url": "https://pavi2410.me", "url": "https://t.co/i1QCOjdAOF", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 29804, "followers_count": 533, "friends_count": 950, "has_custom_timelines": true, "is_translator": false, "listed_count": 7, "media_count": 558, "normal_followers_count": 533, "pinned_tweet_ids_str": ["1917642337083351239"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/720857786486099968/1686486231", "profile_interstitial_type": "", "statuses_count": 6770, "translator_type": "none", "url": "https://t.co/i1QCOjdAOF", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "India"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1869002930457919718"], "editable_until_msecs": "1734443597000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "13114", "state": "EnabledWithCount"}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE4NjkwMDI5MzAxMTM5MjUxMjA=", "text": "I've had the same experience when applying to Canonical\n\nHere's what I got\n\nDear <PERSON><PERSON><PERSON>\n\nThank you for your interest in our Software Engineer - App Stores Backend (Remote) position at Canonical.\n\nI am the hiring lead for this role and a member of the leadership team. If I think there are other roles that would be interesting for you I'll suggest them to you, and I will be happy to answer questions you have about the company and our engineering approach.\n\nI have reviewed your resume and application answers and moved you forward. We start with a formal assessment stage consisting of a written interview and tests that you can take at your own pace, followed by three stages of interviews. In outline:\n\nReview of your application materials (done)\nWritten interview and psychometric assessment (this step)\nTechnical assessment and team interviews\nHiring manager & senior leadership Interviews\nOffer\nThis is your written interview, a prepared statement to cover your strengths, interests, priorities, experience, ideas and ambition.   For your submission please write in your own words and allow us to understand specific examples that highlight your skills and experience. We will refer back to this submission during the interview process to explore your listed experience further. Please note that AI generated responses cannot be accepted.\n\nWe use an initial written interview to get a sense of your experience, your priorities, and how you communicate. We have multiple reviewers assess your answer in an anonymized system to reduce bias and allow candidates from a range of backgrounds to demonstrate their ability, experience and insights. Please feel free to reuse answers if you have made multiple applications to roles at Canonical. There is no rush, I will leave your application open a few weeks to let you find the time for this step.\n\nPlease address the following in a PDF document:\n\nEngineering experience\n\nDescribe your level of experience in Python, and how you have attained it.\nDescribe your experience with SQL and relational data modeling, and summarize your learning with large-scale database backed applications.\nDescribe a case where it was very difficult to test code you were writing, but you found a reliable way to do it.\nWhat kinds of software projects have you worked on before? Which operating systems, development toolchains, languages, databases?\nWould you describe yourself as a high quality coder? Why?\nWould you describe yourself as an architect of resilient software? If so, why, and in which sorts of applications?\nOutline your thoughts on open source software development. What is important to get right in open source projects? What open source projects have you worked on? Have you been an open source maintainer, on which projects, and what was your role?\nDescribe your experience building large systems with many services - web front ends, REST APIs, data stores, event processing and other kinds of integration between components. What are the key things to think about in regard to architecture, maintainability, and reliability in these large systems?\nHow comprehensive would you say your knowledge of a Linux distribution is, from the kernel up? How familiar are you with low-level system architecture, runtimes and Linux distro packaging? How have you gained this knowledge?\nDescribe any experience you have with low-level embedded systems engineering, on Linux or other embedded operating systems\nDescribe your experience with large-scale IT operations, SAAS, or other running services, in a devops or IS or system administration capacity\nDescribe your experience with public cloud based operations - how well do you understand large-scale public cloud estate management and developer experience?\nDescribe your experience with enterprise infrastructure and application management, either as a user running enterprise operations, or as a vendor targeting the enterprise market\nOutline your thoughts on quality in software development.", "entity_set": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 42, "bookmarked": true, "created_at": "<PERSON><PERSON> Dec 17 12:53:17 +0000 2024", "conversation_id_str": "1868952268139381084", "display_text_range": [10, 289], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": [{"id_str": "2164623379", "name": "<PERSON><PERSON><PERSON><PERSON>", "screen_name": "ChShersh", "indices": [0, 9]}]}, "favorite_count": 36, "favorited": false, "full_text": "@ChShersh I've had the same experience when applying to Canonical\n\nHere's what I got\n\nDear <PERSON><PERSON><PERSON>\n\nThank you for your interest in our Software Engineer - App Stores Backend (Remote) position at Canonical.\n\nI am the hiring lead for this role and a member of the leadership team. If I think", "in_reply_to_screen_name": "ChShersh", "in_reply_to_status_id_str": "1868952268139381084", "in_reply_to_user_id_str": "2164623379", "is_quote_status": false, "lang": "en", "quote_count": 1, "reply_count": 2, "retweet_count": 1, "retweeted": false, "user_id_str": "720857786486099968", "id_str": "1869002930457919718"}, "twe_private_fields": {"created_at": 1734439997000, "updated_at": 1748554127577, "media_count": 0}}}]