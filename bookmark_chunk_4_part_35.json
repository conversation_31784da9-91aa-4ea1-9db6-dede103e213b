[{"id": "1707671329929904310", "created_at": "2023-09-29 11:19:06 +03:00", "full_text": "CISCO offers FREE training and certifications for people looking to learn: \n\nData Analytics\nData Science\nPython.\nCybersecurity\nJavaScript\n\nThese are self-paced courses, so you can practice as you learn.\n\n1. Data Analytics\nhttps://t.co/nr23WY9LAv\n\n2. Python Essentials\nhttps://t.co/4Qf35GP3WS\n\n3. JavaScript\nhttps://t.co/ryIoEfXP2t\n\n4. Cybersecurity\nhttps://t.co/3FWP5F2Tpy", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1789, "retweet_count": 657, "bookmark_count": 3107, "quote_count": 8, "reply_count": 22, "views_count": 332547, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1707671329929904310", "metadata": {"__typename": "Tweet", "rest_id": "1707671329929904310", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5MDMzNzQxOTQ4Mjk4MTk5MDU=", "rest_id": "903374194829819905", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1759706558844248064/dFKF9hDk_normal.jpg"}, "core": {"created_at": "Thu Aug 31 21:49:26 +0000 2017", "name": "Ezekiel", "screen_name": "ezekiel_aleke"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "Data Analyst | Author: DATA ANALYSIS MADE EASY | Visit: https://t.co/ubk4F6Uzcx | For 1:1 Training and Promotion DM", "entities": {"description": {"urls": [{"display_url": "selar.com/bpr5", "expanded_url": "https://selar.com/bpr5", "url": "https://t.co/ubk4F6Uzcx", "indices": [56, 79]}]}, "url": {"urls": [{"display_url": "ezekielaleke.substack.com", "expanded_url": "http://ezekielaleke.substack.com", "url": "https://t.co/hBRBJN38cJ", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 37649, "followers_count": 104910, "friends_count": 117, "has_custom_timelines": true, "is_translator": false, "listed_count": 871, "media_count": 3472, "normal_followers_count": 104910, "pinned_tweet_ids_str": ["1644045988045422592"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/903374194829819905/1714058812", "profile_interstitial_type": "", "statuses_count": 16525, "translator_type": "none", "url": "https://t.co/hBRBJN38cJ", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Receive my weekly message ⬇️"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1511413429231308812", "professional_type": "Business", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": true}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1707671329929904310"], "editable_until_msecs": "1695979146000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "332547", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "note_tweet": {"is_expandable": true, "note_tweet_results": {"result": {"id": "Tm90ZVR3ZWV0OjE3MDc2NzEzMjk4Mzc2NDU4MjQ=", "text": "CISCO offers FREE training and certifications for people looking to learn: \n\nData Analytics\nData Science\nPython.\nCybersecurity\nJavaScript\n\nThese are self-paced courses, so you can practice as you learn.\n\n1. Data Analytics\nhttps://t.co/nr23WY9LAv\n\n2. Python Essentials\nhttps://t.co/4Qf35GP3WS\n\n3. JavaScript\nhttps://t.co/ryIoEfXP2t\n\n4. Cybersecurity\nhttps://t.co/3FWP5F2Tpy", "entity_set": {"hashtags": [], "symbols": [], "urls": [{"display_url": "lnkd.in/dZfs9fb3", "expanded_url": "https://lnkd.in/dZfs9fb3", "url": "https://t.co/nr23WY9LAv", "indices": [222, 245]}, {"display_url": "lnkd.in/dm9zfMYJ", "expanded_url": "https://lnkd.in/dm9zfMYJ", "url": "https://t.co/4Qf35GP3WS", "indices": [268, 291]}, {"display_url": "lnkd.in/dCVhs_g8", "expanded_url": "https://lnkd.in/dCVhs_g8", "url": "https://t.co/ryIoEfXP2t", "indices": [307, 330]}, {"display_url": "lnkd.in/dt7tmAGN", "expanded_url": "https://lnkd.in/dt7tmAGN", "url": "https://t.co/3FWP5F2Tpy", "indices": [349, 372]}], "user_mentions": []}, "richtext": {"richtext_tags": []}, "media": {"inline_media": []}}}}, "grok_analysis_button": true, "legacy": {"bookmark_count": 3107, "bookmarked": true, "created_at": "Fri Sep 29 08:19:06 +0000 2023", "conversation_id_str": "1707671329929904310", "display_text_range": [0, 267], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "lnkd.in/dZfs9fb3", "expanded_url": "https://lnkd.in/dZfs9fb3", "url": "https://t.co/R6gwaFiEzU", "indices": [222, 245]}], "user_mentions": []}, "favorite_count": 1789, "favorited": false, "full_text": "CISCO offers FREE training and certifications for people looking to learn: \n\nData Analytics\nData Science\nPython.\nCybersecurity\nJavaScript\n\nThese are self-paced courses, so you can practice as you learn.\n\n1. Data Analytics\nhttps://t.co/R6gwaFiEzU\n\n2. Python Essentials", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 8, "reply_count": 22, "retweet_count": 657, "retweeted": false, "user_id_str": "903374194829819905", "id_str": "1707671329929904310"}, "twe_private_fields": {"created_at": 1695975546000, "updated_at": 1748554188923, "media_count": 0}}}, {"id": "1707859450701246735", "created_at": "2023-09-29 23:46:38 +03:00", "full_text": "🚨 MAJOR UPDATE 🚨\n\nI've updated my Python package so you can scrape FBRef's advanced league data... all top 5 &amp; the 13 others!\n\nIncluding the Championship, Brasileirão, Belgian Pro League, Argentina's top flights, and more!\n\nInstallation instructions 👇\nhttps://t.co/NMg9ovM99j", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 492, "retweet_count": 39, "bookmark_count": 474, "quote_count": 6, "reply_count": 13, "views_count": 88466, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1707859450701246735", "metadata": {"__typename": "Tweet", "rest_id": "1707859450701246735", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyNjMzNDYxMDI=", "rest_id": "263346102", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1913158830914940930/Pn0MQ0TE_normal.jpg"}, "core": {"created_at": "Wed Mar 09 21:37:16 +0000 2011", "name": "<PERSON>", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": false, "legacy": {"default_profile": false, "default_profile_image": false, "description": "Recruitment & Match Analyst for Kolding IF 🇩🇰 ex Loudoun United 🇺🇸 | Views my own | https://t.co/XssD42slXC | https://t.co/xAWVQwKGBB", "entities": {"description": {"urls": [{"display_url": "best11scouting.streamlit.app", "expanded_url": "http://best11scouting.streamlit.app", "url": "https://t.co/XssD42slXC", "indices": [84, 107]}, {"display_url": "football-match-reports.streamlit.app", "expanded_url": "http://football-match-reports.streamlit.app", "url": "https://t.co/xAWVQwKGBB", "indices": [110, 133]}]}, "url": {"urls": [{"display_url": "BenGriffis.com", "expanded_url": "https://BenGriffis.com", "url": "https://t.co/R0HGblE25g", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 46259, "followers_count": 29976, "friends_count": 1150, "has_custom_timelines": true, "is_translator": false, "listed_count": 292, "media_count": 11752, "normal_followers_count": 29976, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/263346102/1707620789", "profile_interstitial_type": "", "statuses_count": 45025, "translator_type": "none", "url": "https://t.co/R0HGblE25g", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "Kolding, Danmark"}, "media_permissions": {"can_media_tag": false}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1589597178531659781", "professional_type": "Creator", "category": [{"id": 1050, "name": "Statistician", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false}, "verification": {"verified": false}}}}, "card": {"rest_id": "https://t.co/NMg9ovM99j", "legacy": {"binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image", "value": {"image_value": {"height": 200, "width": 400, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=400x400"}, "type": "IMAGE"}}, {"key": "description", "value": {"string_value": "A collection of different functions I use to analyze soccer/football - griffisben/griffis_soccer_analysis", "type": "STRING"}}, {"key": "domain", "value": {"string_value": "github.com", "type": "STRING"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"height": 300, "width": 600, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=600x600"}, "type": "IMAGE"}}, {"key": "summary_photo_image_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "thumbnail_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "site", "value": {"scribe_key": "publisher_id", "type": "USER", "user_value": {"id_str": "13334762", "path": []}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"height": 202, "width": 386, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=386x202"}, "type": "IMAGE"}}, {"key": "summary_photo_image_large", "value": {"image_value": {"height": 419, "width": 800, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=800x419"}, "type": "IMAGE"}}, {"key": "thumbnail_image_small", "value": {"image_value": {"height": 72, "width": 144, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=144x144"}, "type": "IMAGE"}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=orig"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "A collection of different functions I use to analyze soccer/football - griffisben/griffis_soccer_analysis", "type": "STRING"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "github.com", "type": "STRING"}}, {"key": "photo_image_full_size", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "A collection of different functions I use to analyze soccer/football - griffisben/griffis_soccer_analysis", "type": "STRING"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 89.89}, {"rgb": {"blue": 124, "green": 118, "red": 114}, "percentage": 2.33}, {"rgb": {"blue": 53, "green": 77, "red": 48}, "percentage": 2.25}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 1.64}, {"rgb": {"blue": 12, "green": 91, "red": 218}, "percentage": 1.59}]}, "type": "IMAGE_COLOR"}}, {"key": "title", "value": {"string_value": "griffis_soccer_analysis/README.md at main · griffisben/griffis_soccer_analysis", "type": "STRING"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 89.89}, {"rgb": {"blue": 124, "green": 118, "red": 114}, "percentage": 2.33}, {"rgb": {"blue": 53, "green": 77, "red": 48}, "percentage": 2.25}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 1.64}, {"rgb": {"blue": 12, "green": 91, "red": 218}, "percentage": 1.59}]}, "type": "IMAGE_COLOR"}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "summary_photo_image", "value": {"image_value": {"height": 314, "width": 600, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=600x314"}, "type": "IMAGE"}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"blue": 255, "green": 255, "red": 255}, "percentage": 89.89}, {"rgb": {"blue": 124, "green": 118, "red": 114}, "percentage": 2.33}, {"rgb": {"blue": 53, "green": 77, "red": 48}, "percentage": 2.25}, {"rgb": {"blue": 166, "green": 115, "red": 53}, "percentage": 1.64}, {"rgb": {"blue": 12, "green": 91, "red": 218}, "percentage": 1.59}]}, "type": "IMAGE_COLOR"}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=png&name=2048x2048_2_exp"}, "type": "IMAGE"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/NMg9ovM99j", "type": "STRING"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"height": 600, "width": 1200, "url": "https://pbs.twimg.com/card_img/1926946195340943360/bNd8wpSB?format=jpg&name=orig"}, "type": "IMAGE"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "summary_large_image", "url": "https://t.co/NMg9ovM99j", "user_refs_results": [{"result": {"__typename": "User", "id": "VXNlcjoxMzMzNDc2Mg==", "rest_id": "13334762", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1633247750010830848/8zfRrYjA_normal.png"}, "core": {"created_at": "Mon Feb 11 04:41:50 +0000 2008", "name": "GitHub", "screen_name": "github"}, "dm_permissions": {"can_dm": false}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": false, "default_profile_image": false, "description": "The AI-powered developer platform to build, scale, and deliver secure software.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "github.com", "expanded_url": "http://github.com", "url": "https://t.co/bbJgfyzcJR", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 8172, "followers_count": 2620091, "friends_count": 328, "has_custom_timelines": true, "is_translator": false, "listed_count": 17801, "media_count": 2646, "normal_followers_count": 2620091, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/13334762/1747774520", "profile_interstitial_type": "", "statuses_count": 9700, "translator_type": "none", "url": "https://t.co/bbJgfyzcJR", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "San Francisco, CA"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Square", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {}, "verification": {"verified": false, "verified_type": "Business"}}}]}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1707859450701246735"], "editable_until_msecs": "1696023998000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"count": "88466", "state": "EnabledWithCount"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 474, "bookmarked": true, "created_at": "Fri Sep 29 20:46:38 +0000 2023", "conversation_id_str": "1707859450701246735", "display_text_range": [0, 279], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [{"display_url": "github.com/griffisben/gri…", "expanded_url": "https://github.com/griffisben/griffis_soccer_analysis/blob/main/README.md", "url": "https://t.co/NMg9ovM99j", "indices": [256, 279]}], "user_mentions": []}, "favorite_count": 492, "favorited": false, "full_text": "🚨 MAJOR UPDATE 🚨\n\nI've updated my Python package so you can scrape FBRef's advanced league data... all top 5 &amp; the 13 others!\n\nIncluding the Championship, Brasileirão, Belgian Pro League, Argentina's top flights, and more!\n\nInstallation instructions 👇\nhttps://t.co/NMg9ovM99j", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 6, "reply_count": 13, "retweet_count": 39, "retweeted": false, "user_id_str": "263346102", "id_str": "1707859450701246735"}, "twe_private_fields": {"created_at": 1696020398000, "updated_at": 1748554188923, "media_count": 0}}}]