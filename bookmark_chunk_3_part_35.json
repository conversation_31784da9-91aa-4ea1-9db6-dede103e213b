[{"id": "1553075123871252480", "created_at": "2022-07-29 20:48:58 +03:00", "full_text": "Five free VS Code extensions that will change the way you do web development:", "media": [], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 3402, "retweet_count": 868, "bookmark_count": 2215, "quote_count": 37, "reply_count": 138, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1553075123871252480", "metadata": {"__typename": "Tweet", "rest_id": "1553075123871252480", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4NTYxNTU1OTI1NzgxNTg1OTI=", "rest_id": "856155592578158592", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1612507803842932736/PLiJMD_l_normal.jpg"}, "core": {"created_at": "Sun Apr 23 14:39:34 +0000 2017", "name": "<PERSON><PERSON><PERSON>", "screen_name": "Prathku<PERSON>"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "I talk about web and social • DevRel @APILayer • Building https://t.co/niju9j3UA2 & https://t.co/TxBXHrPKDu • Prev @Rapid_API @HyperspaceAI", "entities": {"description": {"urls": [{"display_url": "TrioTech.Dev", "expanded_url": "http://TrioTech.Dev", "url": "https://t.co/niju9j3UA2", "indices": [58, 81]}, {"display_url": "RoastProfile.Social", "expanded_url": "http://RoastProfile.Social", "url": "https://t.co/TxBXHrPKDu", "indices": [84, 107]}]}, "url": {"urls": [{"display_url": "PrathamKumar.com", "expanded_url": "https://www.PrathamKumar.com", "url": "https://t.co/gGjMe9QuIY", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 148812, "followers_count": 435982, "friends_count": 891, "has_custom_timelines": true, "is_translator": false, "listed_count": 8454, "media_count": 5769, "normal_followers_count": 435982, "pinned_tweet_ids_str": ["1917933782612496654"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/856155592578158592/1696346737", "profile_interstitial_type": "", "statuses_count": 34425, "translator_type": "none", "url": "https://t.co/gGjMe9QuIY", "want_retweets": false, "withheld_in_countries": []}, "location": {"location": "New Delhi, India"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "professional": {"rest_id": "1473320674487721988", "professional_type": "Creator", "category": [{"id": 713, "name": "Science & Technology", "icon_name": "IconBriefcaseStroke"}]}, "privacy": {"protected": false}, "relationship_perspectives": {"following": false}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "", "patreon_handle": ""}, "super_follow_eligible": true, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1553075123871252480"], "editable_until_msecs": "1659118738000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://www.feedhive.com\" rel=\"nofollow\">FeedHive</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 2215, "bookmarked": true, "created_at": "Fri Jul 29 17:48:58 +0000 2022", "conversation_id_str": "1553075123871252480", "display_text_range": [0, 77], "entities": {"hashtags": [], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "favorite_count": 3402, "favorited": false, "full_text": "Five free VS Code extensions that will change the way you do web development:", "is_quote_status": false, "lang": "en", "quote_count": 37, "reply_count": 138, "retweet_count": 868, "retweeted": false, "user_id_str": "856155592578158592", "id_str": "1553075123871252480"}, "twe_private_fields": {"created_at": 1659116938000, "updated_at": 1748554255574, "media_count": 0}}}, {"id": "1553423997974503425", "created_at": "2022-07-30 19:55:16 +03:00", "full_text": "Do you know why I love both Engineering and Data Analysis so much?\n\nBecause the latter shows the engineering choices' effect on the performance!\n\nWant to see it too? I will show it to you with this simple example about the Alpine's beam wing in this short thread!😉 https://t.co/X2upIBm9sD", "media": [{"type": "photo", "url": "https://t.co/X2upIBm9sD", "thumbnail": "https://pbs.twimg.com/media/FY7angtWYAItpkr?format=jpg&name=thumb", "original": "https://pbs.twimg.com/media/FY7angtWYAItpkr?format=jpg&name=orig"}], "screen_name": null, "name": null, "profile_image_url": null, "in_reply_to": null, "retweeted_status": null, "quoted_status": null, "favorite_count": 1197, "retweet_count": 89, "bookmark_count": 88, "quote_count": 13, "reply_count": 19, "views_count": null, "favorited": false, "retweeted": false, "bookmarked": true, "url": "https://twitter.com/undefined/status/1553423997974503425", "metadata": {"__typename": "Tweet", "rest_id": "1553423997974503425", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTAyNjE2NTcxMTU1NTIxNTM2", "rest_id": "1502616571155521536", "affiliates_highlighted_label": {}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1677630690551029760/pw9d1xVr_normal.jpg"}, "core": {"created_at": "Sat Mar 12 12:08:14 +0000 2022", "name": "Formula Data Analysis", "screen_name": "FDataAnalysis"}, "dm_permissions": {"can_dm": true}, "has_graduated_access": true, "is_blue_verified": true, "legacy": {"default_profile": true, "default_profile_image": false, "description": "|📖 F1 Understanding Starts by Clicking Follow! ⬆️|📈Learn To Read F1 Telemetry Data 📊|⚙️ Motorsport Performance Engineer, PhD in Motorcycle Dynamics 🏎️|", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/fdataanalysis", "expanded_url": "https://linktr.ee/fdataanalysis", "url": "https://t.co/1BsSOomAMP", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 17919, "followers_count": 209088, "friends_count": 1533, "has_custom_timelines": true, "is_translator": false, "listed_count": 2116, "media_count": 3198, "normal_followers_count": 209088, "pinned_tweet_ids_str": ["1923424832315666901"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1502616571155521536/1664013347", "profile_interstitial_type": "", "statuses_count": 15292, "translator_type": "none", "url": "https://t.co/1BsSOomAMP", "want_retweets": true, "withheld_in_countries": []}, "location": {"location": "Turn Notifications On! 🔔"}, "media_permissions": {"can_media_tag": true}, "parody_commentary_fan_label": "None", "profile_image_shape": "Circle", "privacy": {"protected": false}, "relationship_perspectives": {"following": true}, "tipjar_settings": {"is_enabled": false, "bitcoin_handle": "**********************************", "ethereum_handle": "******************************************", "patreon_handle": ""}, "verification": {"verified": false}}}}, "unmention_data": {}, "edit_control": {"edit_tweet_ids": ["1553423997974503425"], "editable_until_msecs": "1659201916000", "is_edit_eligible": false, "edits_remaining": "5"}, "is_translatable": false, "views": {"state": "Enabled"}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "grok_analysis_button": true, "legacy": {"bookmark_count": 88, "bookmarked": true, "created_at": "Sat Jul 30 16:55:16 +0000 2022", "conversation_id_str": "1553423997974503425", "display_text_range": [0, 264], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/X2upIBm9sD", "expanded_url": "https://x.com/F1DataAnalysis/status/1553423997974503425/photo/1", "id_str": "1553419293819297794", "indices": [265, 288], "media_key": "3_1553419293819297794", "media_url_https": "https://pbs.twimg.com/media/FY7angtWYAItpkr.jpg", "type": "photo", "url": "https://t.co/X2upIBm9sD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 915, "w": 1024, "resize": "fit"}, "medium": {"h": 915, "w": 1024, "resize": "fit"}, "small": {"h": 608, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 915, "width": 1024, "focus_rects": [{"x": 0, "y": 149, "w": 1024, "h": 573}, {"x": 109, "y": 0, "w": 915, "h": 915}, {"x": 221, "y": 0, "w": 803, "h": 915}, {"x": 410, "y": 0, "w": 458, "h": 915}, {"x": 0, "y": 0, "w": 1024, "h": 915}]}, "media_results": {"result": {"media_key": "3_1553419293819297794"}}}], "symbols": [], "timestamps": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/X2upIBm9sD", "expanded_url": "https://x.com/F1DataAnalysis/status/1553423997974503425/photo/1", "id_str": "1553419293819297794", "indices": [265, 288], "media_key": "3_1553419293819297794", "media_url_https": "https://pbs.twimg.com/media/FY7angtWYAItpkr.jpg", "type": "photo", "url": "https://t.co/X2upIBm9sD", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 915, "w": 1024, "resize": "fit"}, "medium": {"h": 915, "w": 1024, "resize": "fit"}, "small": {"h": 608, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 915, "width": 1024, "focus_rects": [{"x": 0, "y": 149, "w": 1024, "h": 573}, {"x": 109, "y": 0, "w": 915, "h": 915}, {"x": 221, "y": 0, "w": 803, "h": 915}, {"x": 410, "y": 0, "w": 458, "h": 915}, {"x": 0, "y": 0, "w": 1024, "h": 915}]}, "media_results": {"result": {"media_key": "3_1553419293819297794"}}}]}, "favorite_count": 1197, "favorited": false, "full_text": "Do you know why I love both Engineering and Data Analysis so much?\n\nBecause the latter shows the engineering choices' effect on the performance!\n\nWant to see it too? I will show it to you with this simple example about the Alpine's beam wing in this short thread!😉 https://t.co/X2upIBm9sD", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 13, "reply_count": 19, "retweet_count": 89, "retweeted": false, "user_id_str": "1502616571155521536", "id_str": "1553423997974503425"}, "twe_private_fields": {"created_at": 1659200116000, "updated_at": 1748554255574, "media_count": 1}}}]